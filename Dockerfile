# Dockerfile
FROM node:22.12.0

# create destination directory
RUN mkdir -p /home/<USER>/app
WORKDIR /home/<USER>/app

# update and install dependency
# RUN apk update && apk upgrade
# RUN apk add git

# copy the app, note .dockerignore
COPY . /home/<USER>/app
RUN npm install
RUN npm install vue@3.3.8
RUN npm run build

EXPOSE 3000

ENV NUXT_HOST=0.0.0.0

ENV NUXT_PORT=3000

CMD [ "node", ".output/server/index.mjs" ]
