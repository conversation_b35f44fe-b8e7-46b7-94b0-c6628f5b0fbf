<script setup lang="ts">
import {usePageStore} from "~/store/page";
import {watchDebounced} from "@vueuse/shared";
import {useTaskTypeStore} from "~/store/task-type";
import ModalConfirmation from "~/components/general/ModalConfirmation.vue";
import type {ElementEvent} from "~/types/element";

const $page = usePageStore()
const $taskType = useTaskTypeStore()

let modalDeleteConfirm: ElementEvent | null = null

const initialQuery = {
  query: '',
  field: 'name',
  sort: 'desc',
  status: '',
  from: '',
  to: ''
}

const activeAction = ref()
const query = ref(initialQuery)

onMounted(() => {
  $page.setTitle('Tipe Tugas')

  fetchListTaskType()
})

watchDebounced(() => query.value.query, () => {
  fetchListTaskType()
}, {debounce: 300})



function fetchListTaskType() {
  $taskType.fetchListTaskType({...query.value})
}
</script>


<template>
  <div>
    <!--    <general-modal-->
    <!--        is-has-close-->
    <!--        id="modal-detail-task"-->
    <!--        @mounted="modalDetailLocation = $event"-->
    <!--        @modal-opened="dispatchResizeEvent()"-->
    <!--    >-->
    <!--      <template #icon>-->
    <!--        <icon-marker-pin class="stroke-primary-500"/>-->
    <!--      </template>-->
    <!--      <template #body>-->
    <!--        <detail-task-->
    <!--            :task="selectedTask"-->
    <!--            @on-click-update="isAddingLocation = false ;modalDetailLocation?.hide(); modalFormLocations?.show()"-->
    <!--            @on-click-delete="modalDetailLocation?.hide(); modalConfirmDelete?.show()"-->
    <!--        />-->
    <!--      </template>-->
    <!--    </general-modal>-->

    <div class="bg-white border rounded-lg shadow-sm overflow-hidden">
      <div class="w-full p-6 flex space-x-3 justify-between">
        <general-text-input v-model="query.query" clearable id="inputSearchEmployee" placeholder="Search">
          <template #prefix>
            <icon-search size="20"/>
          </template>
        </general-text-input>
        <general-button
            label="Tambah Tipe Tugas"
            @on-click="navigateTo('/task-types/create')"
        >
          <template #prefix>
            <icon-plus size="20"/>
          </template>
        </general-button>
      </div>

      <modal-confirmation
          id="modal-confirm-delete"
          :is-loading="$taskType.isLoading.form"
          confirm-label="Konfirmasi"
          :subtitle="`Kamu yakin ingin menghapus ${activeAction?.name}?`"
          title="Konfirmasi Hapus" @mounted="modalDeleteConfirm = $event"
          @negative="modalDeleteConfirm?.hide()"
          @positive="$taskType.deleteTaskType(activeAction.id).then(() => {fetchListTaskType(); modalDeleteConfirm?.hide()})"
      />

      <div class="overflow-x-auto">
        <table class="w-full text-sm text-left" style="empty-cells: show;">
          <thead class="text-xs text-gray-700 bg-gray-50 border-b">
          <tr>
            <th scope="col" class="px-6 py-3 font-medium">
              No
            </th>
            <th scope="col" class="px-6 py-3 font-medium">
              Identity
            </th>
            <th scope="col" class="px-6 py-3 font-medium">
              Name
            </th>
            <th scope="col" class="px-6 py-3 font-medium">
              Description
            </th>
            <th scope="col" class="px-6 py-3 font-medium"/>
          </tr>
          </thead>
          <tbody>
          <tr v-if="$taskType.$state.isLoading.list">
            <td></td>
            <td></td>
            <td></td>
            <td class="h-24 flex items-center justify-center">
              <icon-circle-loading/>
            </td>
            <td></td>
          </tr>
          <tr v-else v-for="(taskType, i) in $taskType.listTaskType" :key="taskType.id" class="bg-white border-b">
            <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
              {{ getDataTableNumber(i, 1) }}
            </td>
            <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
              {{ taskType.type_identity }}
            </td>
            <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
              {{ taskType.name }}
            </td>
            <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
              {{ taskType.description }}
            </td>
            <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
              <general-dropdown :id-dropdown="`dropdownAction-${i}`" id-activator="btnExport">
                <template #activator>
                  <general-icon-button
                      id="btnDropdownAction"
                      :data-dropdown-toggle="`dropdownAction-${i}`"
                      @click="activeAction = taskType"
                  >
                    <template #icon>
                      <icon-chevron-down/>
                    </template>
                  </general-icon-button>
                </template>

                <template #content>
                  <div class="py-2.5 px-4 flex items-center cursor-pointer hover:bg-gray-100">
                    <p class="text-sm" @click="navigateTo(`/task-types/${taskType.id}/update`)">Update</p>
                  </div>
                  <div class="py-2.5 px-4 flex items-center cursor-pointer hover:bg-gray-100">
                    <p class="text-sm" @click="modalDeleteConfirm?.show()">Delete</p>
                  </div>
                </template>
              </general-dropdown>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<style scoped>

</style>
