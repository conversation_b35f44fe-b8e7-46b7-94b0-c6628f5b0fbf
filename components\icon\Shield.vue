<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default: "24",
  },
  color: {
    type: String,
    default: "white",
  },
});
</script>

<template>
  <svg xmlns="http://www.w3.org/2000/svg" :width="props.size" :height="props.size" viewBox="0 0 49 48" fill="none">
  <path d="M23.104 43.2289C23.5468 43.4872 23.7682 43.6164 24.0806 43.6834C24.3231 43.7354 24.6769 43.7354 24.9194 43.6834C25.2318 43.6164 25.4532 43.4872 25.896 43.2289C29.7921 40.9559 40.5 33.816 40.5 23.9992V16.3992C40.5 14.2514 40.5 13.1775 40.1689 12.4152C39.8324 11.6404 39.4972 11.2282 38.8071 10.741C38.1282 10.2616 36.7973 9.98481 34.1354 9.4312C31.2016 8.82103 28.9486 7.71923 26.8888 6.1258C25.901 5.36169 25.4071 4.97964 25.0207 4.87544C24.6129 4.76548 24.3871 4.76548 23.9793 4.87544C23.5929 4.97964 23.099 5.36169 22.1112 6.1258C20.0514 7.71923 17.7984 8.82103 14.8646 9.4312C12.2027 9.98481 10.8718 10.2616 10.1929 10.741C9.50284 11.2282 9.16757 11.6404 8.83107 12.4152C8.5 13.1775 8.5 14.2514 8.5 16.3992V23.9992C8.5 33.816 19.2079 40.9559 23.104 43.2289Z" stroke="#EF3434" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
</template>

<style scoped>
</style>
