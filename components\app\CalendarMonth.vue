<script setup lang="ts">
import {endOfMonth, getDate, getDaysInMonth, isSameDay, subMonths} from "date-fns";

interface Day {
  isToday: boolean
  isCurrentMonth: boolean
  date: Date
}

const props = defineProps({
  modelValue: {
    type: Date,
    default: new Date()
  },
  currentMonth: {
    type: Number,
    required: true
  },
  currentYear: {
    type: Number,
    required: true
  },
  variant: {
    type: String as () => 'lg' | 'sm',
    default: 'lg'
  },
  isClickable: {
    type: Boolean,
    default: false
  },
  events: {
    type: Array as () => any[],
    default: []
  },
})

const emit = defineEmits(['update:model-value', 'on-click-more', 'on-click-event'])

const now = new Date()

const getStartDate = (year: number, month: number): Date => {
  return new Date(year, month, 1)
}

const buildCalendar = () => {
  const days: Day[] = []
  const currentMonth = props.currentMonth
  const currentYear = props.currentYear

  // Generate prev month dates
  const startDate = getStartDate(currentYear, currentMonth)
  const startDay = startDate.getDay()

  const prevDateMonth = subMonths(startDate, 1)
  const prevMonth = prevDateMonth.getMonth()
  const prevYear = prevDateMonth.getFullYear()
  const lastDateOfMonth = endOfMonth(prevDateMonth).getDate()

  for (let i = 0; i < startDay; i++) {
    days.unshift({
      date: new Date(prevYear, prevMonth, lastDateOfMonth - i),
      isToday: false,
      isCurrentMonth: false
    })
  }

  // Generate current month dates
  const totalDays = getDaysInMonth(new Date(currentYear, currentMonth))

  for (let i = 0; i < totalDays; i++) {
    days.push({
      date: new Date(currentYear, currentMonth, i + 1),
      isToday: i + 1 === now.getDate() &&
        currentMonth === now.getMonth() &&
        currentYear === now.getFullYear(),
      isCurrentMonth: true
    })
  }

  // Generate next month dates
  const nextDaysToAdd = 7 - (days.length % 7)

  if (nextDaysToAdd < 7) {
    for (let i = 0; i < nextDaysToAdd; i++) {
      days.push({
        date: new Date(currentYear, currentMonth + 1, i + 1),
        isToday: false,
        isCurrentMonth: false
      })
    }
  }

  return days
}

const days = ref(buildCalendar())

watch([() => props.currentYear, () => props.currentMonth], () => {
  days.value = buildCalendar()
})

const filteredEvents = (day: Day) => {
  return props.variant === 'lg'
    ? props.events.filter(event => isSameDay(day.date, event.date))
    : []
}

function onClickDate(date: Date) {
  if (props.isClickable) {
    emit('update:model-value', date)
  }
}

function onHover(id: string, status: 'ASSIGNED' | 'UNASSIGNED', type: 'OVER' | 'LEAVE') {
  const $itemsElement = document.querySelectorAll(`.schedule-item-${id}`)

  $itemsElement.forEach((item) => {
    type === 'OVER'
      ? item.classList.add(status === 'ASSIGNED' ? '!bg-success-200' : '!bg-primary-200')
      : item.classList.remove(status === 'ASSIGNED' ? '!bg-success-200' : '!bg-primary-200')
  })
}

const dateClass = (day: Day) => {
  const baseClass = 'p-1 flex flex-col space-y-3 transition'
  const hover = props.isClickable ? 'cursor-pointer hover:brightness-90' : ''
  const variant = props.variant === 'lg'
    ? 'aspect-[3/4] items-start justify-between outline outline-1 outline-gray-200 -outline-offset-[0.5px]'
    : 'aspect-square items-center justify-center'
  const background = !day.isCurrentMonth || day.isToday
    ? 'bg-gray-100'
    : 'bg-white'
  return `${baseClass} ${variant} ${background} ${hover}`
}

const dateNumberClass = (day: Day) => {
  const baseClass = 'w-6 h-6 rounded-full text-xs aspect-square flex items-center justify-center'
  const background = isSameDay(day.date, props.modelValue) ? 'bg-primary-500 text-white' : 'text-gray-900'
  return `${baseClass} ${background}`
}

const eventClass = (status: string) => {
  const baseClass = 'p-1 w-full rounded border text-xs cursor-pointer font-semibold transition truncate'
  const variant = status === 'ASSIGNED'
    ? 'border-success-500 bg-success-50 text-success-500'
    : 'border-primary-500 bg-primary-50 text-primary-500'
  return `${baseClass} ${variant}`
}
</script>

<template>
  <div class="grid grid-cols-7">
    <div
      v-for="day in ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']"
      :key="day"
      class="text-center text-xs"
      :class="variant === 'lg' ? 'py-1' : 'aspect-square flex items-center justify-center text-gray-500'"
    >
      {{ variant === 'lg' ? day : day.at(0) }}
    </div>

    <div
      v-for="day in days"
      :key="day.date"
      :class="`${dateClass(day)}`"
      @click="onClickDate(day.date)"
    >
      <span :class="dateNumberClass(day)">
        {{ getDate(day.date) }}
      </span>

      <div
        v-if="variant === 'lg'"
        class="h-full overflow-y-auto flex flex-col justify-end space-y-1 w-full"
      >
        <span
          v-for="event in filteredEvents(day).slice(0, 5)"
          :key="event.id"
          :class="`${eventClass(event.status)} schedule-item-${event.id}`"
          @mouseover="onHover(event.id, event.status, 'OVER')"
          @mouseleave="onHover(event.id, event.status, 'LEAVE')"
          @click="emit('on-click-event', event.id)"
        >
          {{ event.title }}
        </span>

        <span
          v-if="filteredEvents(day).length > 5"
          class="text-xs text-primary-500 cursor-pointer hover:text-primary-400"
          @click="emit('on-click-more', filteredEvents(day))"
        >
          +{{ filteredEvents(day).length - 5 }} More
        </span>
      </div>
    </div>
  </div>
</template>
