<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default: "24",
  },
});
</script>

<template>
  <svg
      :width="props.size"
      :height="props.size"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
  >
    <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M6.25 13C10.319 13 10.5 14.38 10.5 17.25C10.5 18.688 10.5 19.711 9.896 20.46C9.201 21.323 7.936 21.5 6.25 21.5C4.564 21.5 3.299 21.323 2.604 20.46C2 19.711 2 18.689 2 17.275L2.75 17.25H2C2 14.38 2.181 13 6.25 13ZM17.25 13C21.319 13 21.5 14.38 21.5 17.25C21.5 18.688 21.5 19.711 20.896 20.46C20.201 21.323 18.936 21.5 17.25 21.5C15.564 21.5 14.299 21.323 13.604 20.46C13 19.711 13 18.689 13 17.275L13.75 17.25H13C13 14.38 13.181 13 17.25 13ZM6.45616 14.5004L6.25 14.5C3.64103 14.5 3.50723 14.6872 3.50037 16.8771L3.50079 17.7302C3.50553 18.6011 3.53871 19.2303 3.771 19.52C4.036 19.848 4.823 20 6.25 20C7.677 20 8.464 19.847 8.729 19.519C9 19.182 9 18.382 9 17.274C9 14.7738 9 14.5122 6.45616 14.5004ZM17.4562 14.5004L17.25 14.5C14.641 14.5 14.5072 14.6872 14.5004 16.8771L14.5008 17.7302C14.5055 18.6011 14.5387 19.2303 14.771 19.52C15.036 19.848 15.823 20 17.25 20C18.677 20 19.464 19.847 19.729 19.519C20 19.182 20 18.382 20 17.274C20 14.7738 20 14.5122 17.4562 14.5004ZM6.25 2C10.319 2 10.5 3.38 10.5 6.25C10.5 7.688 10.5 8.711 9.896 9.46C9.201 10.323 7.936 10.5 6.25 10.5C4.564 10.5 3.299 10.323 2.604 9.46C2 8.711 2 7.689 2 6.275L2.75 6.25H2C2 3.38 2.181 2 6.25 2ZM17.25 2C21.319 2 21.5 3.38 21.5 6.25C21.5 7.688 21.5 8.711 20.896 9.46C20.201 10.323 18.936 10.5 17.25 10.5C15.564 10.5 14.299 10.323 13.604 9.46C13 8.711 13 7.689 13 6.275L13.75 6.25H13C13 3.38 13.181 2 17.25 2ZM6.45616 3.50045L6.25 3.5C3.64103 3.5 3.50723 3.68721 3.50037 5.87705L3.50079 6.73018C3.50553 7.60114 3.53871 8.23029 3.771 8.52C4.036 8.848 4.823 9 6.25 9C7.677 9 8.464 8.847 8.729 8.519C9 8.182 9 7.382 9 6.274C9 3.7738 9 3.51222 6.45616 3.50045ZM17.4562 3.50045L17.25 3.5C14.641 3.5 14.5072 3.68721 14.5004 5.87705L14.5008 6.73018C14.5055 7.60114 14.5387 8.23029 14.771 8.52C15.036 8.848 15.823 9 17.25 9C18.677 9 19.464 8.847 19.729 8.519C20 8.182 20 7.382 20 6.274C20 3.7738 20 3.51222 17.4562 3.50045Z"
    />
  </svg>
</template>

<style scoped></style>
