<script setup lang="ts">
import type {DrawerInterface} from "flowbite";
import {computed} from "vue";
import type {ElementEvent} from "~/types/element";

const $route = useRoute()
const $router = useRouter()
const windowWidth = ref(0)
let drawerSidebar: DrawerInterface | null = null

function resizeHandler() {
  if (process.client) {
    windowWidth.value = window.innerWidth
  }
}

onMounted(() => {
  if (process.client) {
    window.addEventListener('resize', resizeHandler)
    windowWidth.value = window.innerWidth
  }
})

const isMdAndUp = computed((): boolean => {
  return windowWidth.value >= 768
})

watch(isMdAndUp, () => {
  drawerSidebar?.hide()
})
</script>

<template>
  <div>
    <NuxtPwaManifest/>
    <div class="bg-[#F9FAFB]">
      <app-sidebar @on-mounted="drawerSidebar = $event" @on-click-close-sidebar="drawerSidebar?.hide()"/>

      <div class="p-10 md:pl-[300px] min-h-screen w-full">
        <app-header class="mb-10 pb-4 border-b" @on-click-open-sidebar="drawerSidebar?.show()">
          <template #header>

          </template>
        </app-header>
        <slot/>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
