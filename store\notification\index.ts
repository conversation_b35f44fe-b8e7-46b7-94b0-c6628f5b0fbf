import {defineStore} from 'pinia'
import { toast } from 'vue3-toastify'
import type { ValidationError } from 'yup'
import api from '~/services/api'
import type {Meta, notificationList} from '~/types/server-response'
import { DEFAULT_DATE_RANGE, DEFAULT_API_PARAMS } from '~/utils/variables'

interface State {
  isLoading: {
    list: boolean;
    read: boolean;
  };
  listNotification: notificationList[];
  meta: Meta | undefined;
  query?: string;
  from?: string;
  to?: string;
  field?: string;
  sort?: string;
  perpage?: string
}


export const useNotificationStore = defineStore('notification', {
   state: (): State => ({
    isLoading: {
      list: false,
      read: false,
    },
    query: '',
    from: '',
    to: '',
    field: DEFAULT_API_PARAMS.LIST.field,
    sort: DEFAULT_API_PARAMS.LIST.sortdesc,
    perpage: DEFAULT_API_PARAMS.LIST.perpage,
    listNotification: [] as notificationList[],
    meta: undefined as Meta | undefined,
  }),
  actions: {
    async fetchListNotification() {
      this.isLoading.list = true

      const queryParams: Record<string, string> = {
        field: this.field!,
        sort: this.sort!,
      }

      // Use state for from and to if they have values, fallback to default if they're empty
      queryParams.from = this.from || DEFAULT_DATE_RANGE.get().from
      queryParams.to = this.to || DEFAULT_DATE_RANGE.get().to

      const result = await api.get('/notification/get-notifications-data', {
        queryParams
      })

      if (result.status) {
        this.listNotification = result.data
        this.meta = result.meta
      }

      this.isLoading.list = false
    },

    async markAllAsRead () {
      this.isLoading.read = true
  
      try {
        const res = await api.post('/notification/mark-all-as-read')
        toast.success('Mark All Read')
        return res.data
      } catch (e) {
         this.isLoading.read = false;
          if (e as ValidationError) {
            toast.error((e as ValidationError).message);
          } else {
            toast.error('Error');
          }
          throw e;
       } finally {
        this.isLoading.read = false
      }
    },
  },
})
