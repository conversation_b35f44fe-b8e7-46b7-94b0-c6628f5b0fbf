<script setup lang="ts">
interface RoundedType {
    type: "full" | "lg" | "md" | "sm";
}
const props = defineProps({
    id: {
        type: String,
        default: "",
    },
    rounded: {
        type: String as () => "full" | "lg" | "md" | "none",
        default: "lg",
    },
    bordered: {
        type: Boolean,
        default: true,
    },
    disabled: {
        type: Boolean,
        default: false,
    },
    class: {
        type: String,
        default: "",
    },
});

const emit = defineEmits(["on-click"]);

const buttonClass = (): string => {
    const rounded = `rounded-${props.rounded}`;
    const bordered = props.bordered
        ? "border border-gray-300 hover:bg-gray-100"
        : "bg-primary-500 hover:bg-primary-400";
    const disabled = props.disabled
        ? "!bg-gray-100 cursor-not-allowed"
        : "cursor-pointer";
    return `h-12 flex items-center justify-center aspect-square stroke-gray-600 transition ${rounded} ${bordered} ${disabled} ${props.class}`;
};
</script>

<template>
    <button
        :id="props.id"
        :disabled="props.disabled"
        type="button"
        :class="buttonClass()"
        @click="emit('on-click')"
    >
        <slot name="icon" />
    </button>
</template>

<style scoped></style>
