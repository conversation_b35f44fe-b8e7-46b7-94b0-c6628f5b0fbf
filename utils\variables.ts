
// Default date range parameters
export const DEFAULT_DATE_RANGE = {
   //Get default date range (1 month ago to today)
   //returns Object containing from and to dates in YYYY-MM-DD format
  get: (): { from: string; to: string } => {
    const today = new Date()
    const oneMonthAgo = new Date(today)
    oneMonthAgo.setMonth(today.getMonth() - 1)
    // Format dates to YYYY-MM-DD
    const fromDate = oneMonthAgo.toISOString().split('T')[0]
    const toDate = today.toISOString().split('T')[0]
    return { from: fromDate, to: toDate }
  }
}
 //Default API parameters
export const DEFAULT_API_PARAMS = {
  // Default parameters for list endpoints
  LIST: {
    field: 'created_at',
    sortdesc: 'desc',
    sortasc: 'asc',
    perpage: '-1'
  }
}


















