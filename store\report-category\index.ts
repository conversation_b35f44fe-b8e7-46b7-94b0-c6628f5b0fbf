import { defineStore } from 'pinia'
import { toast } from 'vue3-toastify'
import type { ValidationError } from 'yup'
import api from '~/services/api'
import type { Meta, Task } from '~/types/server-response'

interface InputType {
  value: string
  text: string
}

export const useActivityCategoryStore = defineStore('activitycategory', {
  state: () => ({
    isLoading: {
      list: false,
      form: false,
      detail: false,
      search: false,
      inputTypes: false,
    },
    listActivityCategory: [] as Task[],
    detailActivityCategory: null as null | Task,
    meta: undefined as Meta | undefined,
    inputTypes: [] as InputType[],
  }),

  actions: {
    async fetchListActivityCategory({
      query = '',
      field = 'created_at',
      sort = 'desc',
      page = 1,
      perpage = '10',
    }) {
      this.isLoading.list = true

      const result = await api.get('/activity-category/list-category', {
        queryParams: {
          query,
          field,
          sort,
          page: `${page}`,
          perpage: `${perpage}`,
        },
      })

      if (result.status) {
        this.listActivityCategory = result.data
        this.meta = result.meta
      }

      this.isLoading.list = false
    },

    async fetchDetail(id: number) {
      this.isLoading.detail = true
      try {
        const result = await api.get(`/activity-category/detail-category/${id}`)
        if (result.status) {
          this.detailActivityCategory = result.data
        }
      } catch (error) {
        console.error('Error fetching category detail:', error)
        console.log(id, 'id')
        toast.error('Gagal mengambil detail kategori')
      } finally {
        this.isLoading.detail = false
      }
    },

    async updateStatusCategory(activityCategoryId: number, currentStatus: 'ACTIVE' | 'INACTIVE') {
      this.isLoading.form = true
      try {
        const newStatus = currentStatus === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE'
        const result = await api.put(`/activity-category/update-status-category/${activityCategoryId}`, {
          body: {
            status: newStatus,
            _method: 'PUT'
          }
        })
        if (result.status) {
          toast.success('Status berhasil diperbarui')
          return result
        }
      } catch (e) {
        toast.error((e as ValidationError).message)
        throw e
      } finally {
        this.isLoading.form = false
      }
    },

    async fetchInputTypes() {
      this.isLoading.inputTypes = true
      try {
        const result = await api.get('/activity-category/input-types')
        if (result.status) {
          this.inputTypes = result.data
        }
      } catch (error) {
        console.error('Error fetching input types:', error)
        toast.error('Gagal mengambil data tipe input')
      } finally {
        this.isLoading.inputTypes = false
      }
    },

    async createCategory(formData: any) {
      this.isLoading.form = true
      try {
        const result = await api.post('/activity-category/create-category', {
          body: formData
        })
        
        if (result.status) {
          toast.success('Kategori berhasil dibuat')
          return true
        }
        return false
      } catch (error) {
        console.error('API Error:', error)
        toast.error('Gagal membuat kategori')
        return false
      } finally {
        this.isLoading.form = false
      }
    },

    async updateCategory(activityCategoryId: number, formData: any) {
      this.isLoading.form = true
      try {
        const result = await api.put(`/activity-category/update-category/${activityCategoryId}`, {
          body: {
            ...formData,
            _method: 'PUT'
          }
        })
        if (result.status) {
          toast.success('Kategori berhasil diperbarui')
          return result
        }
      } catch (e) {
        toast.error((e as ValidationError).message)
        throw e
      } finally {
        this.isLoading.form = false
      }
    },

    async deleteCategory(activityCategoryId: number) {
      this.isLoading.form = true
      try {
        const result = await api.delete(`/activity-category/delete-activity-category/${activityCategoryId}`)
        if (result.status) {
          toast.success('Kategori berhasil dihapus')
          return true
        }
      } catch (e) {
        toast.error('Gagal menghapus kategori')
        console.error('Error deleting category:', e)
        return false
      } finally {
        this.isLoading.form = false
      }
    },
  }
})
