import {defineStore} from 'pinia'
import api from '~/services/api'
import type {Meta, TaskType} from '~/types/server-response'
import {toast} from 'vue3-toastify'
import type {ValidationError} from 'yup'

export const useTaskTypeStore = defineStore('tasktype', {
  state: () => ({
    isLoading: {
      list: false,
      detail: false,
      form: false,
      search: false,
    },
    listTaskType: [] as TaskType[],
    detailTaskType: null as null | TaskType,
    meta: undefined as Meta | undefined,
  }),
  actions: {
    async fetchListTaskType({
      query = '',
      field = 'name',
      sort = 'desc',
      from = '',
      to = '',
    }) {
      this.isLoading.list = true

      const result = await api.get('/type/view-all', {
        queryParams: {
          query,
          field,
          sort,
          from,
          to,
          perpage: '2000',
        },
      })

      if (result.status) {
        this.listTaskType = result.data
        this.meta = result.meta
      }

      this.isLoading.list = false
    },

    async createTaskType(
      typeIdentity: string,
      name: string,
      description: string | undefined
    ) {
      this.isLoading.form = true

      try {
        const res = await api.post('/type/store-type', {
          body: {
            type_identity: typeIdentity,
            name,
            description,
          },
        })
        this.isLoading.form = false
        toast.success('Success create task type')
        return res.data
      } catch (e) {
        toast.error((e as ValidationError).message)
        throw e
      } finally {
        this.isLoading.form = false
      }
    },

    async getTaskType(
        id: string,
    ) {
      this.isLoading.detail = true

      try {
        const result = await api.get(`/type/view-detail/${id}`)
        this.isLoading.detail = false

        if (result.status) {
          this.detailTaskType = result.data
        }

        this.isLoading.detail = true
      } catch (e) {
        toast.error((e as ValidationError).message)
        throw e
      } finally {
        this.isLoading.detail = false
      }
    },


    async updateTaskType(
        id: number,
        typeIdentity: string,
        name: string,
        description: string
    ) {
      this.isLoading.form = true
      try {
        const res = await api.post(`/type/update-type/${id}`, {
          body: {
            type_identity: typeIdentity,
            name,
            description,
            _method: 'PUT'
          },
        })
        this.isLoading.form = false
        toast.success('Success update task type')
        return res.data
      } catch (e) {
        toast.error((e as ValidationError).message)
        throw e
      } finally {
        this.isLoading.form = false
      }
    },

    async deleteTaskType(
        id: string,
    ) {
      this.isLoading.form = true

      try {
        const res = await api.delete('/type/delete-type/' + id)
        this.isLoading.form = false
        toast.success('Success delete task type')
        return res.data
      } catch (e) {
        toast.error((e as ValidationError).message)
        throw e
      } finally {
        this.isLoading.form = false
      }
    },
  },
})
