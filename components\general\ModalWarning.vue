<script setup lang="ts">

const emit = defineEmits(['mounted', 'close', 'transfer'])
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  subtitle: {
    type: String,
    default: ''
  },
  userCount: {
    type: Number,
    default: 0
  }
})

</script>

<template>
  <general-modal id="modal-warning" title="" @mounted="emit('mounted', $event)" class-modal="max-w-sm">
    <template #body>
      <div class="flex flex-col items-center px-6 py-8">
        <!-- Warning Icon Container -->
        <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mb-6">
          <div class="w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center">
            <icon-alert size="24" stroke="white"/>
          </div>
        </div>

        <!-- Content -->
        <div class="text-center mb-8">
          <h3 class="text-xl font-semibold text-gray-900 mb-3">{{ title }}</h3>
          <p class="text-sm text-gray-600 leading-relaxed">
            {{ subtitle }}
          </p>
        </div>

        <!-- Action Button -->
        <div>
          <button
            @click="emit('transfer')"
            class="px-6 py-3 bg-red-500 mr-4 text-white text-sm font-medium rounded-lg hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors"
          >
            Ya, ganti
          </button>
          <button
            @click="emit('close')"
            class="px-6 py-3 bg-gray-200 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
          >
            Batal
          </button>
        </div>
      </div>
    </template>
  </general-modal>
</template>

<style scoped>

</style>