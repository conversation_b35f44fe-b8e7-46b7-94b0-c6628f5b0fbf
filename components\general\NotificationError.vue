<script setup lang="ts">
import {computed} from "vue";

const props = defineProps({
  id: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: ''
  },
  subtitle: {
    type: String,
    default: ''
  },
  isShow: {
    type: Boolean,
    default:  false
  }
})

const emit = defineEmits(['on-click-close'])

const notificationErrorClass = computed((): string => {
  const isShow = props.isShow ? 'opacity-1' : 'opacity-0 hidden'
  return `w-full p-4 flex items-start rounded-lg bg-red-50 border border-red-500 transition-opacity duration-300 ease-out ${isShow}`
})
</script>

<template>
  <div :id="props.id" :class="notificationErrorClass" role="alert">
    <div class="mr-4 p-0.5 relative">
      <icon-info size="20" class="stroke-primary-500"/>
      <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg" class="absolute -top-[6px] -left-[6px]">
        <circle cx="18" cy="18" r="12.5" stroke="#DC0303" stroke-width="1.5" stroke-opacity="0.3"/>
        <circle cx="18" cy="18" r="16" stroke="#DC0303" stroke-width="1.5" stroke-opacity="0.1"/>
      </svg>
    </div>
    <div class="w-full">
      <div class="flex items-center justify-between">
        <p class="text-sm font-[600] text-gray-700">{{props.title}}</p>
        <button :data-dismiss-target="`#${props.id}`" class="p-0.5 rounded-full hover:bg-red-100" @click="emit('on-click-close')">
          <icon-close size="20" color="#98A2B3" />
        </button>
      </div>
      <p class="text-sm font-[400] text-gray-600">
        {{props.subtitle}}
      </p>
    </div>
  </div>
</template>

<style scoped> </style>