import {
  differenceInDays,
  differenceInHours,
  differenceInMinutes,
  formatDuration,
} from 'date-fns'

export function encodeJson(json: string) {
  try {
    return Object.values(JSON.parse(json))
  } catch(e) {
    return json
  }
}


export function onClickImage(url: string) {
  window.open(url, '_blank');
}

export const defaultLatLng = [-0.7893, 113.9213]

export function isEmpty(value: any) {
  return !value || value.length === 0 || value === ''
}

export function isObjectHasNull(obj: any) {
  return Object.values(obj).includes(null)
}

export function isSameString(value: string, key: string) {
  try {
    return value.toLowerCase().includes(key.toLowerCase())
  } catch (_) {
    return false
  }
}

export function isSameArray(
  arr1: number[] | string[],
  arr2: number[] | string[]
) {
  if (arr1.length !== arr2.length) return false
  return arr1.sort().every((v, i) => v === arr2.sort()[i])
}

export function getCenterMap(coordinates: { lat: number; lng: number }[]): {
  lat: number
  lng: number
} {
  const latitudes = coordinates?.map((c) => c.lat)
  const longitudes = coordinates?.map((c) => c.lng)

  return {
    lat: (Math.min(...latitudes) + Math.max(...latitudes)) / 2,
    lng: (Math.min(...longitudes) + Math.max(...longitudes)) / 2,
  }
}

export function getZoomMap(
  coordinates: { lat: number; lng: number }[]
): number {
  const latitudes = coordinates.map((c) => c.lat)
  const longitudes = coordinates.map((c) => c.lng)

  const latDiff = Math.max(...latitudes) - Math.min(...latitudes)
  const lngDiff = Math.max(...longitudes) - Math.min(...longitudes)

  const latZoom = Math.ceil(Math.log(360 / latDiff) / Math.LN2)
  const lngZoom = Math.ceil(Math.log(360 / lngDiff) / Math.LN2)

  return Math.min(latZoom, lngZoom)
}

export function capitalizeString(str: string, separator: string | null = null) {
  if (!separator) {
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
  } else {
    return str
      .toLowerCase()
      .split(separator)
      .map((s) => s.charAt(0).toUpperCase() + s.slice(1))
      .join(' ')
  }
}

export function getDataTableNumber(i: number, page: number) {
  return i + 1 + 15 * (page - 1)
}

export function hasWhiteSpace(str: string) {
  return /\s/.test(str)
}

export function removeSpaces(str: string) {
  return str.replaceAll(/\s/g, '')
}

export function getTimeDifference(end: Date, start: Date) {
  const days = differenceInDays(end, start)
  const totalHours = differenceInHours(end, start)
  const hours = totalHours % 24
  const minutes = differenceInMinutes(end, start) % 60

  const dayString = days > 0 ? `${days} Days` : ''
  const hourString = hours > 0 ? ` ${hours} hr` : ''
  const minuteString = minutes > 0 ? ` ${minutes} min` : ''

  return `${dayString}${hourString}${minuteString}`
}

export function convertSecondsToTimeEstimation(seconds: number) {
  const minuteInSeconds = 60
  const hourInSeconds = 60 * minuteInSeconds
  const dayInSeconds = 24 * hourInSeconds
  const monthInSeconds = 30 * dayInSeconds
  const yearInSeconds = 365 * dayInSeconds

  return formatDuration({
    years: Math.floor(seconds / yearInSeconds),
    months: Math.floor((seconds % yearInSeconds) / monthInSeconds),
    days: Math.floor((seconds % monthInSeconds) / dayInSeconds),
    hours: Math.floor((seconds % dayInSeconds) / hourInSeconds),
    minutes: Math.floor((seconds % hourInSeconds) / minuteInSeconds),
  })
}

export function downloadBlob(response: Blob, fileName: string) {
  const file = new File([response], fileName, { type: response.type })
  const url = URL.createObjectURL(file)
  const downloadLink = document.createElement('a')
  downloadLink.href = url
  downloadLink.download = fileName
  downloadLink.click()
  URL.revokeObjectURL(url)
}

/**
 * Merges two arrays, `a` and `b`, into a new array `c`.
 * `predicate` is an optional function that determines whether two items are considered equal.
 * If a matching item is found in `a`, it is not added to `c`.
 *
 * @param {Array} a - The first array to be merged.
 * @param {Array} b - The second array to be merged.
 * @param {Function} [predicate=(a, b) => a === b] - The function that determines whether two items are considered equal.
 * @return {Array} - The merged array `c`.
 */
export const merge = (
  a: Array<any>,
  b: Array<any>,
  predicate: Function = (a: any, b: any) => a === b
): Array<any> => {
  const c = [...a] // copy to avoid side effects
  // add all items from B to copy C if they're not already present
  b.forEach((bItem) =>
    c.some((cItem) => predicate(bItem, cItem)) ? null : c.push(bItem)
  )
  return c
}

export function formatDate(dateString: any) {
  //dd/mm/yyyy
  const date = new Date(dateString)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${month}/${day}/${year}`
}

export function formatDatetoTime(dateString: any) {
  //HH:ii
  const date = new Date(dateString)

  // Extract time components
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  // Format time
  // return `${hours}:${minutes}:${seconds}`
  return `${hours}:${minutes}`
}

export async function copyTextToClipboard(textToCopy: string) {
  await new Promise((r) => setTimeout(r, 200));

  if (navigator?.clipboard?.writeText) {
    return navigator.clipboard.writeText(textToCopy);
  }
  return Promise.reject('The Clipboard API is not available.');
}

export function formatDateUTC(dateString: string): string {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  
  const month = String(date.getUTCMonth() + 1).padStart(2, '0');
  const day = String(date.getUTCDate()).padStart(2, '0');
  const year = date.getUTCFullYear();
 
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}`;
}

