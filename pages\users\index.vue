<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { usePageStore } from "~/store/page";
import { format } from 'date-fns'
import { useUsersStore } from '~/store/users'
import { useRolesStore } from '~/store/roles'
import { useAuthStore } from '~/store/auth'
import { useCompaniesStore } from '~/store/companies'
import type { User } from '~/types/server-response';
import type { ElementEvent } from '~/types/element';
import ModalConfirmation from '~/components/general/ModalConfirmation.vue';

const $usersStore = useUsersStore()
const $rolesStore = useRolesStore()
const $companiesStore = useCompaniesStore()

const $auth = useAuthStore()


const query = ref({
  search_value: '',
  search_columns: 'name', // Kolom default untuk pencarian
  page: 1,
  page_size: 10,
  range: [] as Date[],
  labelFrom: '',
  labelTo: '',
  company_id: '',
  role_id: '',
  from: '',
  to: ''
})

function formatDate(date: string) {
  return format(new Date(date), 'yyyy-MM-dd HH:mm')
}

const $page = usePageStore()
const $router = useRouter()

onMounted(async () => {
  $page.setTitle('Akun Pengguna')
})


function fetchUsers() {
    $usersStore.fetchUsers({
        page: query.value.page,
        page_size: query.value.page_size,
        from: query.value.from,
        to: query.value.to,
        company_id: query.value.company_id,
        role_id: query.value.role_id,
        search_value: query.value.search_value,
        search_columns: query.value.search_columns,
    })
}

onMounted(async () => {
  if ($auth.session.role.name === 'Super Admin') {
    $companiesStore.fetchCompanies({ page_size: 1000 })
  }
  $rolesStore.fetchRoles({ page_size: 1000 })
  fetchUsers() 
})

const companies = computed(() => $companiesStore.companies.map(company => ({
  text: company.name,
  value: company.id.toString()
})))

const roles = computed(() => $rolesStore.roles.map(role => ({
  text: role.name,
  value: role.id.toString()
})))


function applyFilter() {
  query.value.page = 1
  fetchUsers()
}

function clearDate() {
  query.value.range = []
  query.value.from = ''
  query.value.to = ''
  query.value.labelFrom = ''
  query.value.labelTo = ''
}

const options = ref([
  { text: '10', value: '10' },
  { text: '20', value: '20' },
  { text: '30', value: '30' },
  { text: '40', value: '40' },
  { text: '50', value: '50' }
])

const selected = ref('10')

function resetFilter() {
  query.value.company_id = ''
  query.value.role_id = ''
  query.value.search_value = '' // Tambahkan ini
  clearDate()
  applyFilter()
}


function dateString(dates: Date) {
  const date = new Date(dates)
  return format(date, 'yyyy-MM-dd')
}

function dateStringDMY(dates: Date) {
  const date = new Date(dates)
  return format(date, 'dd/MM/yyyy')
}

watch(() => selected.value, (newValue) => {
  query.value.page_size = Number(newValue)
  query.value.page = 1
  fetchUsers()
})

// Watcher untuk perubahan halaman
watch(() => query.value.page, () => {
  fetchUsers()
})

// Watcher untuk input pencarian dengan debounce
let searchTimeout: NodeJS.Timeout | null = null

// memantau search_value
watch(() => query.value.search_value, () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  query.value.page = 1
  
  searchTimeout = setTimeout(() => {
    fetchUsers()
  }, 300)
})


const stopRangeWatcher = watch(
  () => [query.value.range?.[0], query.value.range?.[1]],
  ([start, end]) => {
    if (start) {
      query.value.from = dateString(start)
      query.value.labelFrom = dateStringDMY(start)
    } else {
      query.value.from = ''
      query.value.labelFrom = ''
    }

    if (end) {
      query.value.to = dateString(end)
      query.value.labelTo = dateStringDMY(end)
    } else {
      query.value.to = ''
      query.value.labelTo = ''
    }
  }
)

onUnmounted(() => {
  // Membersihkan semua watcher dan timeout saat komponen dihancurkan
  if (searchTimeout) {
    clearTimeout(searchTimeout)
    searchTimeout = null
  }
  stopRangeWatcher()
})

const activeAction = ref<User | null>(null)
let modalDeleteConfirm: ElementEvent | null = null
let modalPasswordConfirm: ElementEvent | null = null
const deletePassword = ref('')
const isPasswordModalLoading = ref(false)
const showPassword = ref(false)

function goDetail(id: string) {
  $router.push(`/users/${id}#detail`)
}

function goToEdit(id: string) {
  $router.push(`/users/${id}#edit`)
}

async function handleDeleteUser(user: User) {
  activeAction.value = user
  modalDeleteConfirm?.show()
}

function proceedToPasswordConfirmation() {
  modalDeleteConfirm?.hide()
  deletePassword.value = ''
  modalPasswordConfirm?.show()
}

async function confirmDeleteWithPassword() {
  if (!activeAction.value || !deletePassword.value) return

  isPasswordModalLoading.value = true
  const success = await $usersStore.deleteUser(activeAction.value.id, deletePassword.value)
  if (success) {
    await fetchUsers() // Gunakan fetchBaseUsers
    modalPasswordConfirm?.hide()
  }

  isPasswordModalLoading.value = false
}
</script>

<template>
  <div class="bg-white rounded-xl space-y-4">
    <!-- Title and Subtitle -->
    <div class="p-6">
      <p class="text-xs text-gray-500 mt-1">Kelola daftar pengguna, peran, dan akses ke sistem.</p>
    </div>

    <!-- Filter/Search/Date Bar -->
    <div class="p-6">
      <div class="w-full flex">
        <general-text-input v-model="query.search_value" clearable id="inputSearchUser" placeholder="Search">
          <template #prefix>
            <icon-search size="20" />
          </template>
        </general-text-input>
        <general-dropdown id-activator="btnDropdownFilter" id-dropdown="dropdownFilter">
          <template #activator>
            <general-outlined-button label="Filters" id="btnDropdownFilter" data-dropdown-toggle="dropdownFilter"
              class="!h-11 ml-3">
              <template #prefix>
                <icon-filter-lines size="20" />
              </template>
            </general-outlined-button>
          </template>

          <template #content>
            <label for="filter" class="mb-1.5 text-lg font-[600] text-gray-700 p-4">
              Filter
            </label>
            <form>
              <div>
                <div class="p-4">
                  <general-range-datepicker v-model="query.range" id="inputFilterDateRange" label="Date Range"
                    placeholder="Select Date Range" :enable-time-picker="false" class="flex-1" />
                </div>
                <div class="p-4">
                  <general-combobox id="selectFilterRole" v-model="query.role_id" has-search-field
                    id-trigger="triggerDropdownRole" id-target="dropdownRole" label="Role" :items="roles" plain
                    placeholder="Pilih Role" />
                </div>
                <div v-if="$auth.session?.role?.name === 'Super Admin'" class="p-4">
                  <general-combobox id="selectFilterCompany" v-model="query.company_id" has-search-field
                    id-trigger="triggerDropdownCompany" id-target="dropdownCompany" label="Company" :items="companies"
                    plain placeholder="Pilih Company" />
                </div>
                <div class="p-4">
                  <div class="flex gap-4">
                    <general-button id="btnFilterApply" label="Terapkan Filter" class="p-2" @click="applyFilter">
                    </general-button>
                    <general-outlined-button id="btnFilterReset" label="Reset" class="p-2" @click="resetFilter">
                    </general-outlined-button>
                  </div>
                </div>
              </div>
            </form>
          </template>
        </general-dropdown>
        <div class="flex space-x-3 w-full items-center justify-end">
          <general-dropdown id-activator="btnExport" id-dropdown="dropdownExport">
            <template #activator>
              <general-outlined-button id="btnExport" data-dropdown-toggle="dropdownExport" label="Export"
                class="border-primary-500 border-2 text-primary-500">
                <template #prefix>
                  <icon-download-cloud size="20" class="stroke-primary-500" />
                </template>
              </general-outlined-button>
            </template>

            <template #content>
              <div class="py-2.5 px-4 flex items-center cursor-pointer hover:bg-gray-100" @click="'#'">
                <p class="text-sm">Export Excel</p>
              </div>
              <div class="py-2.5 px-4 flex items-center cursor-pointer hover:bg-gray-100" @click="'#'">
                <p class="text-sm">Export PDF</p>
              </div>
            </template>
          </general-dropdown>

          <general-button label="Tambah" @on-click="navigateTo('/users/create')">
            <template #prefix>
              <icon-plus size="20" />
            </template>
          </general-button>
        </div>
      </div>
      <div class="w-full">
        <general-dropdown id-activator="btnDropdownCalendar" id-dropdown="dropdownCalendar">
          <template #activator>
            <div v-show="query.labelFrom != '' && query.labelTo != ''"
              class="flex justify-center w-[250px] items-center mt-5 m-0 text-sm text-gray-700 border rounded-lg p-3"
              style="white-space: nowrap">
              <div v-show="query.labelFrom != '' && query.labelTo != ''">
                {{ query.labelFrom }} - {{ query.labelTo }}
                <span @click="clearDate" style="cursor: pointer">
                  <icon-close width="20" height="20" style="
                  aspect-ratio: 210 / 210;
                  object-fit: cover;
                  display: inline-block;
                " class="stroke-gray-700 ml-2" />
                </span>
              </div>
            </div>
          </template>

          <template #content>
            <general-range-datepicker v-model="query.range" inline :enable-time-picker="false" />
          </template>
        </general-dropdown>
      </div>
    </div>

    <!-- Companies List -->
    <div class="rounded-xl overflow-x-auto border border-gray-200">
      <table class="w-full text-sm text-left" style="empty-cells: show;" aria-describedby="Tabel Daftar Perusahaan">
        <thead class="text-xs text-gray-700 bg-gray-50 border-b">
          <tr>
            <th class="px-6 py-4 text-left text-xs font-bold text-gray-500">No</th>
            <th class="px-6 py-4 text-left text-xs font-bold text-gray-500">Nama</th>
            <th class="px-6 py-4 text-left text-xs font-bold text-gray-500">Tipe Pengguna</th>
            <th class="px-6 py-4 text-left text-xs font-bold text-gray-500">Role</th>
            <th class="px-6 py-4 text-left text-xs font-bold text-gray-500">Email</th>
            <th class="px-6 py-4 text-left text-xs font-bold text-gray-500">Perusahaan</th>
            <th class="px-6 py-4 text-left text-xs font-bold text-gray-500">No Telepon</th>
            <th class="px-6 py-4 text-left text-xs font-bold text-gray-500">Dibuat Pada</th>
            <th class="px-6 py-4 text-right text-xs font-bold text-gray-500"></th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(user, index) in $usersStore.users" :key="user.id">
            <td class="px-6 py-4 text-sm text-gray-900 align-middle">{{ ((query.page - 1) * query.
              page_size) + index + 1 }}</td>
            <td class="px-6 py-4">
              <div class="flex items-center gap-3">
                <div>
                  <div class="flex items-center gap-2">
                    <span class="text-sm font-semibold text-gray-900">{{ user?.name || '-' }}</span>
                  </div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 text-sm text-gray-500 font-semibold align-middle">{{ user?.user_type || '-' }}</td>
            <td class="px-6 py-4 text-sm text-gray-500 font-semibold align-middle">{{ user?.role?.name || '-' }}</td>
            <td class="px-6 py-4 text-sm text-gray-500 font-semibold align-middle">{{ user?.email || '-' }}</td>
            <td class="px-6 py-4 text-sm text-gray-500 font-semibold align-middle">{{ user?.company?.name || '-' }}</td>
            <td class="px-6 py-4 text-sm text-gray-500 font-semibold align-middle">{{ user?.phone || '-' }}</td>
            <td class="px-6 py-4 text-sm text-gray-500 font-semibold align-middle">{{ formatDate(user?.created_at) ||
              '-' }}
            </td>
            <td class="px-1 py-4 font-normal text-gray-900 whitespace-nowrap">
              <general-dropdown :id-dropdown="`dropdownAction-${index}`" id-activator="btnDropdownAction"
                placement="right">
                <template #activator>
                  <general-icon-button id="btnDropdownAction" :data-dropdown-toggle="`dropdownAction-${index}`"
                    @click="'#'">
                    <template #icon>
                      <icon-three-dot />
                    </template>
                  </general-icon-button>
                </template>

                <template #content>
                  <div class="py-2.5 px-4 flex items-center cursor-pointer hover:bg-gray-100 group"
                    @click="goToEdit(user.id)">
                    <p class="text-sm group-hover:text-red-500">Edit</p>
                  </div>
                  <div class="py-2.5 px-4 flex items-center cursor-pointer hover:bg-gray-100 group"
                    @click="goDetail(user.id)">
                    <p class="text-sm group-hover:text-red-500">Detail</p>
                  </div>
                  <div class="py-2.5 px-4 flex items-center cursor-pointer hover:bg-gray-100 group">
                    <p class="text-sm group-hover:text-red-500" @click="handleDeleteUser(user)">Hapus</p>
                  </div>
                </template>
              </general-dropdown>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Delete Confirmation Modal -->
    <ModalConfirmation id="modal-confirm-delete" :is-loading="false" confirm-label="Ya"
      :subtitle="`Apakah Anda yakin ingin menghapus user ${activeAction?.name || ''} dari My Task?`" title="Hapus User?"
      @mounted="modalDeleteConfirm = $event" @negative="modalDeleteConfirm?.hide()"
      @positive="proceedToPasswordConfirmation()">
      <template #icon>
        <icon-trash class="stroke-red-500" size="24" />
      </template>
    </ModalConfirmation>

    <!-- Password Confirmation Modal -->
    <general-modal id="modal-password-confirm" title="" class-modal="max-w-md" @mounted="modalPasswordConfirm = $event">
      <template #body>
        <div class="flex flex-col gap-4 items-center p-2">
          <div class="p-3 rounded-full bg-red-50">
            <div class="p-2 rounded-full bg-red-200">
              <icon-shield class="text-red-900" size="24" />
            </div>
          </div>

          <div class="space-y-2 mb-4 text-center">
            <p class="text-lg text-gray-900 font-semibold">Konfirmasi Penghapusan Perusahaan</p>
            <p class="text-sm text-gray-500">Semua data perusahaan akan dihapus secara permanen. Jika Anda yakin,
              masukkan kata sandi untuk melanjutkan.</p>
          </div>

          <div class="w-full mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Password *</label>
            <div class="relative">
              <input v-model="deletePassword" :type="showPassword ? 'text' : 'password'" :class="[
                'w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2',
                'border-gray-300 focus:ring-blue-500'
              ]" placeholder="Input Password">
              <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center"
                @click="showPassword = !showPassword">
                <svg v-if="!showPassword" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24"
                  stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                <svg v-else class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                </svg>
              </button>
            </div>
          </div>

          <div class="flex w-full gap-3">
            <general-button label="Hapus" class="w-full bg-red-500 hover:bg-red-600" :loading="isPasswordModalLoading"
              :disabled="!deletePassword" @on-click="confirmDeleteWithPassword()" />
            <general-outlined-button label="Batal" class="w-full" @on-click="modalPasswordConfirm?.hide()" />
          </div>
        </div>
      </template>
    </general-modal>

    <!-- Pagination & Info -->
    <div class="flex justify-between mt-2 items-center">
      <div class="flex">
        <select v-model="selected" style="width: 80px;  border-radius: 8px">
          <option v-for="option in options" :value="option.value">
            {{ option.text }}
          </option>
        </select>
        <p class="flex items-center ml-2">{{ selected }} From <span class="ml-2">{{ $usersStore.users.length || 0 }}
            Data</span></p>
      </div>
      <div>
        <general-pagination v-model="query.page" :active-page="$usersStore.page.page"
          :total-page="$usersStore.page.total_pages" :is-prev-btn="false" />
      </div>
    </div>
  </div>
</template>
