import { defineStore } from 'pinia'
import { toast } from 'vue3-toastify'
import api from '~/services/api'
import type { Role } from '~/types/server-response'

export const useRolesStore = defineStore('roles', {
  state: () => ({
    isLoading: {
      list: false,
      form: false,
      detail: false,
      search_value: false,
      inputTypes: false,
    },
    roles: [] as Role[],
    detail: null as Role | null,
    loading: false,
    total: 0,
    page: 1,
    page_size: 10,
  }),

  actions: {
    async fetchRoles(params: { page?: number; page_size?: number; search_value?: string; search_columns?: string; from?: string; to?: string; industry_id?: string; company_id?: string } = {}, useGo = true) {

      this.loading = true
      try {
        const { data } = await api.get('/api/v1/roles', { queryParams: {
          page: params.page?.toString() || '',
          page_size: params.page_size?.toString() || '',
          search_columns: params.search_columns || 'name',
          search: params.search_value || '',
          company_id: params.company_id || '',
          from: params.from || '',
          to: params.to || '',
        }, useGo })
        this.roles = data.list
        this.page = data.pagination
      } catch (error: any) {
        toast.error(error.message || 'Failed to fetch roles')
      } finally {
        this.loading = false
      }
    },
    async createRole(roleData: { name: string; description: string; company_id: string; permissions: string[] }, useGo = true) {
      this.loading = true;
      try {
        const response = await api.post('/api/v1/roles', {
          body: {
            name: roleData.name,
            company_id: roleData.company_id,
            description: roleData.description,
            permissions: roleData.permissions
          },
          useGo
        });
        
        if (response) {
          toast.success('Role berhasil dibuat');
          return response;
        }
        return null;
      } catch (error: any) {
        console.error('Error creating role:', error);
        const errorMessage = error.data?.message || error.message || 'Gagal membuat role';
        toast.error(errorMessage);
        throw error;
      } finally {
        this.loading = false;
      }
    },
    async fetchDetail(id: string) {
      this.isLoading.detail = true;
      try {
        const result = await api.get(`/api/v1/roles/${id}`, {
          useGo: true,
        });

        if (result.success) {
          this.detail = result.data;
        }
      } catch (error) {
        console.error("Error fetching role detail:", error);
        console.log(id, "id");
        toast.error("Gagal mengambil detail role");
      } finally {
        this.isLoading.detail = false;
      }
    },
    async updateRole(id: string, roleData: { name: string; description: string; company_id: string; permissions: string[] }, useGo = true) {
      this.isLoading.form = true;
      try {
        const response = await api.put(`/api/v1/roles/${id}`, {
          body: roleData,
          useGo,
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (response.success) {
          toast.success("Role berhasil diubah");
          return response;
        }
        return null;
      } catch (error: any) {
        console.error("Error updating role:", error);
        toast.error(error.message || "Gagal mengubah data role");
        throw error;
      } finally {
        this.isLoading.form = false;
      }
    },
    async deleteRole(id: string,  useGo = true) {
      this.loading = true;
      try {
        const response = await api.delete(`/api/v1/roles/${id}`, {
          useGo
        });
        
        if (response) {
          toast.success('Role berhasil dihapus');
          return { success: true, data: response };
        }
        return { success: false };
      } catch (error: any) {
        console.error('Error deleting role:', error);
        
        // Handle 400 response specifically (role still has users)
        if (error.status === 400 || error.response?.status === 400) {
          return { 
            success: false, 
            error: 'ROLE_HAS_USERS',
            message: error.data?.message || error.message,
            userCount: error.data?.user_count || 0
          };
        }
        
        const errorMessage = error.data?.message || error.message || 'Gagal menghapus role';
        toast.error(errorMessage);
        return { success: false, error: 'UNKNOWN_ERROR', message: errorMessage };
      } finally {
        this.loading = false;
      }
    },
    async bulkUpdateUsers(payload: { role_id: string; user_ids: number[] }, useGo = true) {
      this.loading = true;
      try {
        const response = await api.put('/api/v1/roles/bulk-update', {
          body: payload,
          useGo
        });
        
        if (response) {
          toast.success('Pengguna berhasil dipindahkan ke role yang dituju');
          return { success: true, data: response };
        }
        return { success: false };
      } catch (error: any) {
        console.error('Error bulk updating users:', error);
        const errorMessage = error.data?.message || error.message || 'Gagal memindahkan pengguna';
        toast.error(errorMessage);
        return { success: false, error: 'BULK_UPDATE_ERROR', message: errorMessage };
      } finally {
        this.loading = false;
      }
    },
  }
})
