meta {
  name: Get Task Type
  type: http
  seq: 1
}

get {
  url: {{base_url}}/type/view-all?perpage=10&query=natapo
  body: none
  auth: bearer
}

query {
  perpage: 10
  query: natapo
  ~page: 
  ~field: name
  ~sort: asc
}

auth:bearer {
  token: {{token}}
}

script:pre-request {
  // 
}

tests {
  // if(pm.response.code == 200) {
  //     pm.test("Status code is 200");
  // } else if(pm.response.code == 401) {
  //     pm.test("Status code is 401");
  // }
  // 
  // var getParams = pm.request.url.query.all();
  // if(getParams) {
  //     getParams.forEach(function(getParam){
  //         pm.test("Param value = " + getParam.value);
  //     });   
  // }
  //  
}
