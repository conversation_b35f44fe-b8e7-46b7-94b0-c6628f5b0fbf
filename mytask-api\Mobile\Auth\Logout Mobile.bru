meta {
  name: Logout Mobile
  type: http
  seq: 2
}

post {
  url: {{mobile_url}}/logout
  body: multipartForm
  auth: bearer
}

headers {
  X-Mobile-App: Android
  X-Mobile-App-Version: 4.4
}

auth:bearer {
  token: {{mobileToken}}
}

script:pre-request {
  // 
}

tests {
  // if(pm.response.to.have.status(200)) {
  //         pm.test("Status code is 200", function () {
  //         pm.response.to.have.status(200);
  //         pm.collectionVariables.set("mobileToken", "");
  //     });
  // }
}
