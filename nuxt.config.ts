// https://nuxt.com/docs/api/configuration/nuxt-config

import pkg from './package.json'

export default defineNuxtConfig({
  devtools: {
    enabled: true,

    timeline: {
      enabled: true,
    },
  },
  app: {
    pageTransition: { name: 'page', mode: 'out-in' },
    layoutTransition: { name: 'layout', mode: 'out-in' },
    head: {
      title: 'MyTask - Solusi Terbaik untuk Kelola Tugas Anda',
      link: [
        {
          rel: 'icon',
          type: 'image/x-icon',
          href: '/favicon.ico',
        },
        {
          rel: 'apple-touch-icon',
          sizes: '180x180',
          href: '/apple-touch-icon-180x180.png',
        },
        {
          rel: 'mask-icon',
          sizes: '512x512',
          href: '/maskable-icon-512x512.png',
          color: '#ffffff',
        },
      ],
      meta: [
        {
          name: 'theme-color',
          content: '#ffffff',
        },
        {
          name: 'viewport',
          content: 'width=device-width, initial-scale=1, maximum-scale=1',
        },
        {
          name: 'description',
          content:
            'Solusi lengkap kebutuhan untuk pengelolaan tugas, penentuan rute, hingga monitoring perjalanan dari proses pengiriman order di dalam satu dashboard yang mudah digunakan.',
        },
        {
          name: 'og:',
        },
        {
          property: 'og:title',
          content: `MyTask - Solusi Terbaik untuk Kelola Tugas Anda`,
        },
        {
          property: 'og:description',
          content: `Solusi lengkap kebutuhan untuk pengelolaan tugas, penentuan rute, hingga monitoring perjalanan dari proses pengiriman order di dalam satu dashboard yang mudah digunakan.`,
        },
        {
          property: 'og:url',
          content: `https://mytask.transtrack.id`,
        },
        {
          property: 'og:image',
          content: `https://res.cloudinary.com/dku4l3dsm/image/upload/v1706591327/TransTRACK/pdcj6ak60syrjjeeqsac.png`,
        },
        {
          property: 'og:image:width',
          content: '1200',
        },
        {
          property: 'og:image:height',
          content: '630',
        },
        {
          property: 'og:image:type',
          content: 'image/png',
        },
        {
          property: 'twitter:card',
          content: 'summary_large_image',
        },
        {
          property: 'og:type',
          content: 'website',
        },
        {
          property: 'og:locale',
          content: 'id_ID',
        },
      ],
      charset: 'utf-8',
      viewport: 'width=device-width, initial-scale=1',
    },
  },
  build: {
    transpile: ['@vuepic/vue-datepicker', 'gsap'],
  },
  css: ['@/assets/css/global.css'],
  modules: [
    '@vite-pwa/nuxt',
    '@nuxtjs/tailwindcss',
    '@pinia/nuxt',
    '@pinia-plugin-persistedstate/nuxt',
    '@sidebase/nuxt-auth',
    'nuxt3-leaflet',
    '@nuxt/image',
  ],
  pwa: {
    registerType: 'autoUpdate',
    manifest: {
      name: 'MyTask',
      display: 'standalone',
      background_color: '#ffffff',
      short_name: 'MyTask',
      lang: 'en',
      scope: '/',
      start_url: '/login',
      theme_color: '#ffffff',
      icons: [
        {
          src: 'pwa-192x192.png',
          sizes: '192x192',
          type: 'image/png',
          purpose: 'any',
        },
        {
          src: 'pwa-512x512.png',
          sizes: '512x512',
          type: 'image/png',
          purpose: 'any',
        },
        {
          src: 'maskable-icon-512x512.png',
          sizes: '512x512',
          type: 'image/png',
          purpose: 'maskable',
        },
      ],
      id: 'MyTask',
      description:
        'Solusi lengkap kebutuhan untuk pengelolaan tugas, penentuan rute, hingga monitoring perjalanan dari proses pengiriman order di dalam satu dashboard yang mudah digunakan.',
      orientation: 'portrait',
      categories: ['utilities'],
      screenshots: [
        {
          src: 'landing-page-1.webp',
          sizes: '4320×2421',
          type: 'image/webp',
          form_factor: 'wide',
        },
        {
          src: 'landing-page-3.webp',
          sizes: '2880x3168',
          type: 'image/webp',
          form_factor: 'narrow',
        },
      ],
      launch_handler: {
        client_mode: 'focus-existing',
      },
      prefer_related_applications: true,
      dir: 'ltr',
      related_applications: [
        {
          platform: 'webapp',
          url: 'https://telematics.transtrack.id',
        },
        {
          platform: 'webapp',
          url: 'https://mytask.transtrack.id',
        },
      ],
    },
    workbox: {
      globPatterns: ['**/*.{js,css,html,png,svg,ico}'],
    },
    client: {
      installPrompt: true,
      // you don't need to include this: only for testing purposes
      // if enabling periodic sync for update use 1 hour or so (periodicSyncForUpdates: 3600)
      // periodicSyncForUpdates: 20,
    },
    devOptions: {
      enabled: true,
      suppressWarnings: true,
      navigateFallbackAllowlist: [/^\/$/],
      type: 'module',
    },
  },
  image: {
    format: ['webp'],
  },
  ssr: false,
  auth: {
    provider: {
      type: 'local',
      endpoints: {
        signIn: {
          path: `/signIn`,
          method: 'post',
        },
        signOut: {
          path: '/signOut',
          method: 'post',
        },
      },
      pages: {
        login: '/',
      },
      token: {
        signInResponseTokenPointer: '/data/access_token',
        maxAgeInSeconds: 60 * 60 * 24,
      },
    },
    globalAppMiddleware: true,
  },
  routeRules: {
    '/': {
      ssr: false,
    },
  },
  runtimeConfig: {
    appKey: process.env.APP_KEY,
    public: {
      accessTokenFMS: process.env.ACCESS_TOKEN_FMS,
      secretKey: process.env.SECRET_KEY,
      accessTokenMapBox: process.env.ACCESS_TOKEN_MAPBOX,
      baseAPI: process.env.API_BASE_URL,
      apiGoBaseUrl: process.env.API_GO_BASE_URL,
      clientVersion: pkg.version,
    },
  },
})
