<script setup lang="ts">
import { ref, onMounted, computed, watch } from "vue";
import { useRouter } from "vue-router";
import { useUsersStore } from "~/store/users";
import { useRolesStore } from "~/store/roles";
import { useCompaniesStore } from "~/store/companies";
import { toast } from "vue3-toastify";
import { useAuthStore } from "~/store/auth";

const props = defineProps<{
  userId?: string;
  isEdit?: boolean;
}>();

const $usersStore = useUsersStore();
const $rolesStore = useRolesStore();
const $companiesStore = useCompaniesStore();
const $auth = useAuthStore();

const router = useRouter();

const form = ref({
  userType: "web",
  name: "",
  email: "",
  phone: "",
  credentials: "",
  role: "",
  company_id: "",
});

const showPassword = ref(true);
const isSubmitting = ref(false);
const isLoading = ref(false);

const loadUserData = async () => {
  if (props.isEdit && props.userId) {
    isLoading.value = true;
    try {
      await $usersStore.fetchDetail(props.userId);
      const user = $usersStore.detail;
      if (user) {
        form.value = {
          userType: user.user_type?.toLowerCase() || "web",
          name: user.name || "",
          email: user.email || "",
          phone: user.phone || "",
          credentials: "",
          role: user.role?.id || "",
          company_id: user.company?.id || "",
        };
      }
    } catch (error) {
      console.error("Error loading user data:", error);
      toast.error("Gagal memuat data pengguna");
    } finally {
      isLoading.value = false;
    }
  }
};

onMounted(async () => {
  await $rolesStore.fetchRoles({ page_size: 1000 });
  await $companiesStore.fetchCompanies();
  await loadUserData();
});

const roles = computed(() => $rolesStore.roles);
const companies = computed(() => $companiesStore.companies);

const handleSubmit = async () => {
  try {
    isSubmitting.value = true;

    const payload: any = {
      name: form.value.name,
      email: form.value.email,
      user_type: form.value.userType.toUpperCase(),
      phone: form.value.phone.toString(),
    };

    if (form.value.userType !== 'mobile') {
      payload.role_id = form.value.role;
    }

    if (form.value.company_id && form.value.company_id !== "") {
      payload.company_id = form.value.company_id.toString();
    }

    if (form.value.credentials) {
      if (form.value.userType === 'web') {
        payload.password = form.value.credentials;
      } else if (form.value.userType === 'mobile') {
        payload.passcode = form.value.credentials;
      }
    }

    if (props.isEdit && props.userId) {
      await $usersStore.updateUser(props.userId, payload, true);
      toast.success("Pengguna berhasil diperbarui");
    } else {
      if (!form.value.credentials) {
        toast.error("Password/PIN harus diisi");
        return;
      }
      if (form.value.userType === 'web') {
        payload.password = form.value.credentials;
      } else if (form.value.userType === 'mobile') {
        payload.passcode = form.value.credentials;
      }

      await $usersStore.createUser(payload);
      toast.success("Pengguna berhasil ditambahkan");
    }

    router.push("/users");
  } catch (error) {
    console.error("Error saving user:", error);
    toast.error(
      props.isEdit ? "Gagal memperbarui pengguna" : "Gagal menambahkan pengguna"
    );
  } finally {
    isSubmitting.value = false;
  }
};

const goBack = () => {
  router.push("/users");
};

const isNumber = (evt: KeyboardEvent) => {
  const charCode = evt.which ? evt.which : evt.keyCode;
  if (charCode > 31 && (charCode < 48 || charCode > 57) && charCode !== 46) {
    evt.preventDefault();
  }
};

const handlePinInput = (event: Event) => {
  if (form.value.userType === 'mobile' && showPassword.value) {
    const target = event.target as HTMLInputElement;
    if (target.value.length > 6) {
      target.value = target.value.slice(0, 6);
      form.value.credentials = target.value;
    }
  }
};
</script>

<template>
  <div v-if="isLoading" class="flex justify-center items-center py-8">
    <div
      class="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"
    ></div>
  </div>

  <div v-else class="bg-white rounded-xl shadow-sm">
    <!-- Header -->
    <div class="p-6 border-b border-gray-200">
      <h2 class="text-xl font-semibold text-gray-900">
        {{ isEdit ? "Edit Pengguna" : "Tambah Pengguna Baru" }}
      </h2>
      <p class="text-sm text-gray-600 mt-1">
        {{
          isEdit
            ? "Perbarui informasi pengguna"
            : "Isi form di bawah untuk menambahkan pengguna baru"
        }}
      </p>
    </div>

    <!-- Form Content -->
    <div class="p-6">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- User Type Selection -->
        <div class="md:col-span-2">
          <label class="block text-sm font-medium text-gray-700 mb-3"
            >Tipe Pengguna <span class="text-red-500">*</span></label
          >
          <div class="flex space-x-4">
            <label class="flex items-center">
              <input
                v-model="form.userType"
                :disabled="isEdit"
                type="radio"
                value="web"
                class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
              />
              <span class="ml-2 text-sm text-gray-700">Web User</span>
            </label>
            <label class="flex items-center">
              <input
                v-model="form.userType"
                :disabled="isEdit"
                type="radio"
                value="mobile"
                class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
              />
              <span class="ml-2 text-sm text-gray-700">Mobile User</span>
            </label>
          </div>
        </div>

        <!-- Company Selection -->
        <div
          v-if="$auth.session?.role?.name === 'Super Admin' || $auth.session?.session?.role?.name === 'Super Admin'"
          class="md:col-span-2"
        >
          <label
            for="company"
            class="block text-sm font-medium text-gray-700 mb-1"
            >Perusahaan <span class="text-red-500">*</span></label
          >
          <select
            id="company"
            v-model="form.company_id"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500"
          >
            <option value="">Pilih Perusahaan</option>
            <option
              v-for="company in companies"
              :key="company.id"
              :value="company.id"
            >
              {{ company.name }}
            </option>
          </select>
        </div>

        <!-- Role Selection (Web only) -->
        <div v-if="form.userType === 'web'">
          <label for="role" class="block text-sm font-medium text-gray-700 mb-1"
            >Role <span class="text-red-500">*</span></label
          >
          <select
            id="role"
            v-model="form.role"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500"
          >
            <option value="">Pilih Role</option>
            <option v-for="role in roles" :key="role.id" :value="role.id">
              {{ role.name }}
            </option>
          </select>
        </div>

        <!-- Full Name -->
        <div>
          <label for="name" class="block text-sm font-medium text-gray-700 mb-1"
            >Nama Pengguna <span class="text-red-500">*</span></label
          >
          <input
            id="name"
            v-model="form.name"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500"
            placeholder="Ex : John Transtrack"
          />
        </div>

        <!-- Email -->
        <div>
          <label
            for="email"
            class="block text-sm font-medium text-gray-700 mb-1"
            >Email <span class="text-red-500">*</span></label
          >
          <input
            id="email"
            v-model="form.email"
            :disabled="isEdit"
            type="email"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500"
            placeholder="Ex : <EMAIL>"
          />
        </div>

        <!-- Phone Number -->
        <div>
          <label
            for="phone"
            class="block text-sm font-medium text-gray-700 mb-1"
            >Nomor Telepon <span class="text-red-500">*</span></label
          >
          <input
            id="phone"
            v-model="form.phone"
            type="tel"
            pattern="[0-9]*"
            inputmode="numeric"
            class="block w-full px-3 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500"
            placeholder="Ex : 08123456***"
            @keypress="isNumber($event)"
          />
        </div>

        <!-- Password -->
        <div>
          <label
            for="password"
            class="block text-sm font-medium text-gray-700 mb-1"
          >
            {{ form.userType === "web" ? "Kata Sandi" : "PIN Akun" }}
            <span class="text-red-500">*</span>
          </label>
          <div class="relative">  
              <input
                :id="form.userType === 'web' ? 'password' : 'pin'"
                v-model="form.credentials"
                :type="form.userType === 'web' ? (showPassword ? 'text' : 'password') : (showPassword ? 'number' : 'password')"
                :maxlength="form.userType === 'web' ? undefined : (showPassword ? '6' : '6')"
                :max="form.userType === 'mobile' && showPassword ? '999999' : '6'"
                @input="handlePinInput"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500"
                :placeholder="
                  form.userType === 'web' ? 'Masukkan kata sandi' : 'Masukkan PIN'"
              />
              <button
                type="button"
                class="absolute inset-y-0 right-0 pr-3 flex items-center"
                @click="showPassword = !showPassword"
              >
                <svg
                  v-if="showPassword"
                  class="h-5 w-5 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                  />
                </svg>
                <svg
                  v-else
                  class="h-5 w-5 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"
                  />
                </svg>
              </button>
            </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex justify-end space-x-4 p-6 border-t border-gray-200">
      <button
        type="button"
        @click="goBack"
        class="px-4 py-2 border border-red-500 text-red-500 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
      >
        Batal
      </button>
      <button
        type="button"
        @click="handleSubmit"
        :disabled="isSubmitting"
        class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <span v-if="isSubmitting">
          <svg
            class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            ></circle>
            <path
              class="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          Memproses...
        </span>
        <span v-else>{{ isEdit ? "Perbarui" : "Simpan" }}</span>
      </button>
    </div>
  </div>
</template>

<style scoped>
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
</style>
