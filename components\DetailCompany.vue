<script setup lang="ts">
import { useCompaniesStore } from '~/store/companies'
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { format } from 'date-fns'
import { usePermissionsStore } from '~/store/permissions'

const props = defineProps<{
  id: string;
  isPublic?: boolean;
}>();

const $router = useRouter()
const $companiesStore = useCompaniesStore()
const $permissionsStore = usePermissionsStore()
const isLoading = ref(true)
const isEditMode = ref(false)

const formattedDate = computed(() => {
  if (!$companiesStore.company?.created_at) return ''
  return format(new Date($companiesStore.company.created_at), 'yyyy-MM-dd HH:mm')
})

// Add computed property to get role permissions
const companiesPermissions = computed(() => {
  return $companiesStore.company?.permissions || [];
});


const permissions = computed(() => $permissionsStore.permissions);

// Transform the permissions data for the table
const features = computed(() => {
  if (!permissions.value) return [];

  return permissions.value.map((group: any) => {
    const permissionsMap: Record<string, { id: string; checked: boolean }> = {};
    group.permissions.forEach((permission: any) => {
      const permissionType = permission.name.toLowerCase();
      // Check if this permission exists in role's permissions
      const hasPermission = companiesPermissions.value.some(
        (rp) => rp.id === permission.id
      );

      permissionsMap[permissionType] = {
        id: permission.id,
        checked: hasPermission,
      };
    });

    const featureId = group.group;
    // Check if feature is selected based on any permission being checked
    const isSelected = Object.values(permissionsMap).some((p) => p.checked);
    if (isSelected) {
      selectedFeatures.value.add(featureId);
    }

    return {
      id: featureId,
      name: group.group
        .split("_")
        .map((word: any) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" "),
      selected: isSelected,
      permissions: {
        view: permissionsMap.view || { id: "", checked: false },
        create: permissionsMap.create || { id: "", checked: false },
        edit: permissionsMap.edit || { id: "", checked: false },
        delete: permissionsMap.delete || { id: "", checked: false },
      },
    };
  });
});

// Add this ref to track selected features
const selectedFeatures = ref<Set<string>>(new Set());

// Add this ref to track feature search
const featureSearch = ref('');
const searchQuery = ref('')

// Computed property for filtered features based on search
const filteredFeatures = computed(() => {
  if (!searchQuery.value && !featureSearch.value) return features.value;
  const searchTerm = searchQuery.value.toLowerCase() || featureSearch.value.toLowerCase();
  return features.value.filter((feature: any) =>
    feature.name.toLowerCase().includes(searchTerm)
  );
});

// Add a computed property to check if a feature is selected
const isFeatureSelected = computed(() => (featureId: string) => {
  return selectedFeatures.value.has(featureId);
});

// Toggle all features
const areAllFeaturesSelected = computed(() => {
  return (
    filteredFeatures.value.length > 0 &&
    filteredFeatures.value.every((feature: any) =>
      isFeatureSelected.value(feature.id)
    )
  );
});

const featureGroups = [
  { id: 'dashboard', name: 'Dashboard' },
  { id: 'activity_report', name: 'Laporan Aktivitas' },
  { id: 'tasks', name: 'Tugas' },
  { id: 'user', name: 'Pengguna' },
  { id: 'role_management', name: 'Role' },
  { id: 'company', name: 'Perusahaan' },
  { id: 'gps_tracking', name: 'GPS Tracking' },
  { id: 'attendance_list', name: 'Daftar Absen' },
  { id: 'employee', name: 'Karyawan' },
  { id: 'assignment_type', name: 'Tipe Penugasan' },
  { id: 'report_category', name: 'Kategori Laporan' },
];

const hasPermission = (groupId: string, permission: string) => {
  return $companiesStore.company?.permissions?.some(
    p => p.group === groupId && p.key === `${groupId}@${permission}`
  );
};

async function fetchCompanyDetail() {
  try {
    await $companiesStore.fetchDetail(Number(props.id))
  } catch (error) {
    console.error('Error fetching company detail:', error)
  } finally {
    isLoading.value = false
  }
}

function goBack() {
 navigateTo('/companies')
}

async function goToEdit() {
  isLoading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    isEditMode.value = true
    window.location.hash = 'edit'
  } finally {
    isLoading.value = false
  }
}

onMounted(async () => {
  await $permissionsStore.fetchPermissions(); 
  fetchCompanyDetail()
  if (window.location.hash === '#edit') {
    goToEdit()
  }
})

watch(() => window.location.hash, (newHash) => {
    if (newHash !== '#edit') {
    isEditMode.value = false
  }
})
</script>

<template>
  <div v-if="isLoading" class="flex justify-center items-center min-h-screen">
    <general-loading />
  </div>
  <div v-else-if="!isEditMode">
    <div class="flex justify-between items-center mb-6">
      <div>
        <p class="text-gray-500">Detail dari data perusahaan</p>
      </div>
    </div>

    <div class="mt-2 mb-6">
      <button
        class="flex mb-5 items-center space-x-2 px-3 py-2 text-black rounded-md"
        @click="goBack"
      >
        <svg
          id="back-arrow"
          class="-ml-4"           
          height="24"
          viewBox="0 0 24 24"
          width="24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M0 0h24v24H0V0z" fill="none" opacity=".87"></path>
          <path
            d="M16.62 2.99c-.49-.49-1.28-.49-1.77 0L6.54 11.3c-.39.39-.39 1.02 0 1.41l8.31 8.31c.49.49 1.28.49 1.77 0s.49-1.28 0-1.77L9.38 12l7.25-7.25c.48-.48.48-1.28-.01-1.76z"
          ></path>
        </svg>
        <span class="font-bold text-xl">Kembali</span>
      </button>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-lg font-semibold">Informasi Perusahaan</h2>
        <div class="text-sm text-gray-500">
          Dibuat pada {{ formattedDate || '-' }}
        </div>
      </div>

      <div class="space-y-6">
        <div class="w-24 h-24 rounded-lg overflow-hidden border border-gray-200">
          <img
            v-if="$companiesStore.company?.avatar"
            :src="$companiesStore.company.avatar"
            alt="Company Logo"
            class="w-full h-full object-cover"
          />
          <div v-else class="w-full h-full bg-gray-100 flex items-center justify-center">
            <span class="text-gray-400 text-xs">No logo</span>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <p class="text-xs font-medium font-semibold mb-1">Nama Perusahaan</p>
            <p class="text-sm text-gray-900">{{ $companiesStore.company?.name || '-' }}</p>
          </div>
          <div>
            <p class="text-xs font-medium font-semibold mb-1">Industri</p>
            <p class="text-sm text-gray-900">{{ $companiesStore.company?.industry?.name || '-' }}</p>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <p class="text-xs font-medium font-semibold mb-1">Email Perusahaan</p>
            <p class="text-sm text-gray-900">{{ $companiesStore.company?.email || '-' }}</p>
          </div>
          <div>
            <p class="text-xs font-medium font-semibold mb-1">No Telepon Perusahaan</p>
            <p class="text-sm text-gray-900">{{ $companiesStore.company?.phone || '-' }}</p>
          </div>
        </div>

        <div class="pt-2">
          <p class="text-xs font-medium font-semibold mb-1">Deskripsi</p>
          <p class="text-sm text-gray-900">
            {{ $companiesStore.company?.description || 'Tidak ada deskripsi' }}
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <p class="text-xs font-medium font-semibold mb-1">Map Engine</p>
            <p class="text-sm text-gray-900">{{ $companiesStore.company?.map_engine || '-' }}</p>
          </div>
          <div>
            <p class="text-xs font-medium font-semibold mb-1">Total Jam Kerja</p>
            <p class="text-sm text-gray-900">{{ $companiesStore.company?.total_working_hour || '-' }}</p>
          </div>
          <div>
            <p class="text-xs font-medium font-semibold mb-1">Toleransi Keterlambatan</p>
            <p class="text-sm text-gray-900">{{ $companiesStore.company?.late_tolerance ? $companiesStore.company.late_tolerance + '%' : '-' }}</p>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
      <h2 class="text-lg font-semibold mb-6">Informasi Admin Pengguna</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <p class="text-xs font-medium font-semibold mb-1">Nama Admin</p>
          <p class="text-sm text-gray-900">
            {{ $companiesStore.company?.first_user?.name || '-' }}
          </p>
        </div>
        
        <div>
          <p class="text-xs font-medium font-semibold mb-1">Email Admin</p>
          <p class="text-sm text-gray-900">
            {{ $companiesStore.company?.first_user?.email || '-' }}
          </p>
        </div>
        
        <div>
          <p class="text-xs font-medium font-semibold mb-1">Nomor Telepon Admin</p>
          <p class="text-sm text-gray-900">
            {{ $companiesStore.company?.first_user?.phone || '-' }}
          </p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mt-6">
      <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-semibold mb-2">
            Informasi Perizinan<span class="text-red-500">*</span>
          </h2>
          <div class="text-sm font-semibold">
            <div class="flex items-center">
              <p>Total Fitur</p>
              (
              <p class="text-red-500">{{ selectedFeatures.size }}</p>
              )
            </div>
          </div>
        </div>
        
        <div class="flex justify-start mb-4">
          <div class="relative w-1/3">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <input
              v-model="searchQuery"
              type="text"
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder="Cari fitur"
            />
          </div>
        </div>
      
      <div class="overflow-x-auto">
        <table
          class="min-w-full divide-y divide-gray-200"
          aria-describedby="Tabel Daftar Fitur"
        >
          <thead>
            <tr>
              <th
                class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                <div class="flex items-center">
                  <input
                    type="checkbox"
                    :checked="areAllFeaturesSelected"
                    disabled
                    class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded mr-2"
                  />
                  Fitur
                </div>
              </th>
              <th
                class="px-6 py-3 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                View
              </th>
              <th
                class="px-6 py-3 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Create
              </th>
              <th
                class="px-6 py-3 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Edit
              </th>
              <th
                class="px-6 py-3 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Delete
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="feature in filteredFeatures" :key="feature.id">
              <td
                class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"
              >
                <div class="flex items-center">
                  <input
                    type="checkbox"
                    :checked="isFeatureSelected(feature.id)"
                    disabled
                    class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded mr-2"
                  />
                  {{ feature.name }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <input
                  v-model="feature.permissions.view.checked"
                  type="checkbox"
                  class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                  disabled
                />
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <input
                  v-model="feature.permissions.create.checked"
                  type="checkbox"
                  class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                  disabled
                />
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <input
                  v-model="feature.permissions.edit.checked"
                  type="checkbox"
                  disabled
                  class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                />
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-center">
                <input
                  v-model="feature.permissions.delete.checked"
                  type="checkbox"
                  disabled
                  class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="mt-3">
    <button
        class="flex items-center gap-2 px-4 py-2 text-white bg-red-600 hover:bg-red-700 rounded-lg"
        @click="goToEdit"
        >
        <span>Edit</span>
    </button>
    </div>
  </div>
  <div v-else>
    <forms-form-create-company
      :company="$companiesStore.company"
      @on-cancel="goBack"
      @on-success="fetchCompanyDetail(); goBack()"
    />
  </div>
</template>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
