<script setup lang="ts">
import type {ValidationError} from "yup";
import {object, string} from "yup";
import {toast} from "vue3-toastify";
import type {TaskType} from "~/types/server-response";
import {useTaskTypeStore} from "~/store/task-type";

const props = defineProps({
  taskType: {
    type: Object as () => TaskType | null,
    default: null,
  },
});

const router = useRouter();
const $taskType = useTaskTypeStore();

const goBack = () => {
  router.back()
};

const initialForm = ref({
  typeIdentity: '',
  name: '',
  description: '',
});

const form = reactive({...initialForm.value});
const isLoading = ref(false);
const emit = defineEmits(["on-success"]);

watch(() => props.taskType, (value) => {
  if (value) {
    form.typeIdentity = value.type_identity
    form.name = value.name
    form.description = value.description
  }
})

const schema = object({
  typeIdentity: string().required("Type identity field is required"),
  name: string()
      .required("Task title field is required")
      .max(50, "Task titles cannot be longer than 50 characters"),
  description: string(),
});

const isValid = () => {
  try {
    return schema.validateSync({
      typeIdentity: form.typeIdentity,
      name: form.name,
      description: form.description,
    });
  } catch (e) {
    const message = (e as ValidationError).message;
    toast.error(message);
    return false;
  }
};

async function onSubmit() {
  // if (isLoading.value == true) return;
  // isLoading.value = true;

  if (isValid()) {
    let result;

    if(props.taskType) {
      result = await $taskType.updateTaskType(
          props.taskType.id,
          form.typeIdentity,
          form.name,
          form.description,
      );
    } else {
      result = await $taskType.createTaskType(
          form.typeIdentity,
          form.name,
          form.description,
      );
    }

    if (result) {
      emit("on-success");
      router.back()
    }
  }
  isLoading.value = false;
}
</script>

<template>
  <div>
    <p class="text-gray-500 -mt-14 mb-10">
      Lengkapi data berikut Untuk membuat tipe tugas baru
    </p>
    <div class="mt-4">
      <button
          class="flex mb-5 items-center space-x-2 px-3 py-2 text-black rounded-md"
          @click="goBack"
      >
        <svg
            id="back-arrow"
            class="-ml-4"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M0 0h24v24H0V0z" fill="none" opacity=".87"></path>
          <path
              d="M16.62 2.99c-.49-.49-1.28-.49-1.77 0L6.54 11.3c-.39.39-.39 1.02 0 1.41l8.31 8.31c.49.49 1.28.49 1.77 0s.49-1.28 0-1.77L9.38 12l7.25-7.25c.48-.48.48-1.28-.01-1.76z"
          ></path>
        </svg>
        <span class="font-bold text-xl">Kembali</span>
      </button>
    </div>
    <div
        class="bg-white mb-6 border px-10 pt-12 rounded-lg shadow-sm overflow-hidden"
    >
      <div class="grid gap-3 grid-cols-2 mb-6">
        <general-text-input
            id="typeName"
            v-model="form.name"
            label="Nama Tipe Penugasan"
            placeholder="Ex: Tipe A"
            @update:model-value="() => form.typeIdentity = form.name.replace(/[^a-zA-Z0-9]/g, '').toUpperCase()"
            required
        />
        <general-text-input
            id="typeIdentity"
            v-model="form.typeIdentity"
            label="Kode Tipe Penugasan"
            placeholder="Ex: T01"
            required
        />
        <general-textarea
            id="typeDescription"
            v-model="form.description"
            label="Informasi"
            placeholder="Ex: Informasi A"
        />
      </div>
    </div>
    <div class="flex w-2/3 space-x-3">
      <general-button
          :label="props.taskType ? 'Rubah Tipe Tugas':'Buat Tipe Tugas'"
          :loading="$taskType.isLoading.form"
          class="w-52"
          type="Buat Tugas"
          @click="onSubmit"
      />
      <general-outlined-button
          class="w-44"
          label="Batal "
          @click="goBack"
          @on-click="
        form = { ...initialForm };
        emit('on-click-close');
      "
      />
    </div>
  </div>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
