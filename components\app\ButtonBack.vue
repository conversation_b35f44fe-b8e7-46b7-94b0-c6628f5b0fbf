<script setup lang="ts">
import {useRouter} from "vue-router";

const $router = useRouter()

const props = defineProps({
  id: {
    type: String,
    default: 'btnBack'
  },
  label: {
    type: String,
    default: 'Back'
  },
  step: {
    type: Number,
    default: -1
  },
  path: {
    type: String || null,
    default: null
  }
})

function redirect() {
  props.path
    ? $router.push(props.path)
    : $router.go(props.step)
}
</script>

<template>
  <general-text-button
    :label="props.label"
    @on-click="redirect()"
  >
    <template #prefix>
      <icon-chevron-left size="20"/>
    </template>
  </general-text-button>
</template>
