<script setup lang="ts">
import {usePageStore} from "~/store/page";
import {useTaskTypeStore} from "~/store/task-type";

const $page = usePageStore()
const $route = useRoute()
const $taskType = useTaskTypeStore()

onMounted(() => {
  $page.setTitle('Rubah Tipe Tugas')

  const taskTypeId = String($route.params.id)
  $taskType.getTaskType(taskTypeId)
})

</script>

<template>
  <forms-form-task-type :task-type="$taskType.detailTaskType"/>
</template>

<style scoped>

</style>
