<script setup lang="ts">
import {usePageStore} from "~/store/page";
import {watchDebounced} from "@vueuse/shared";
import { useActivityCategoryStore } from "~/store/report-category";
import { useRouter } from 'vue-router'
import type { ActivityCategory } from "~/types/server-response";
import type {ElementEvent} from "~/types/element";
import ModalConfirmation from '~/components/general/ModalConfirmation.vue';
import { ref } from 'vue'

const $page = usePageStore()
const $router = useRouter()
const $activityCategoryStore = useActivityCategoryStore()

let modalDeleteConfirm: ElementEvent | null = null
const activeAction = ref<ActivityCategory | null>(null)
const selected = ref('10')
const options = ref([
  { text: '10', value: '10' },
  { text: '20', value: '20' },
  { text: '30', value: '30' },
  { text: '40', value: '40' },
  { text: '50', value: '50' }
])

const initialQuery = {
  query: '',
  field: 'created_at',
  sort: 'desc',
  status: '',
  from: '',
  to: '',
  page: 1,
  perpage: selected.value,
}

const query = ref(initialQuery)

async function toggleStatus(activityCategory: ActivityCategory) {
  try {
    await $activityCategoryStore.updateStatusCategory(activityCategory.id, activityCategory.status)
    await fetchListActivityCategory()
  } catch (error) {
    console.error('An error occurred:', error)
  }
}

function getStatusText(status: 'ACTIVE' | 'INACTIVE') {
  return status === 'ACTIVE' ? 'Non Aktif' : 'Aktif'
}

function getStatusColor(status: 'ACTIVE' | 'INACTIVE') {
  return status === 'ACTIVE' ? 'text-error-500' : 'text-success-500'
}

function fetchListActivityCategory() {
  $activityCategoryStore.fetchListActivityCategory({...query.value})
}

function goDetail(id: number) {
  $router.push(`/report-category/${id}#detail`)
}

function goToEdit(id: number) {
  $router.push(`/report-category/${id}#edit`)
}

async function handleDeleteCategory(activityCategory: ActivityCategory) {
  activeAction.value = activityCategory
  modalDeleteConfirm?.show()
}

async function confirmDelete() {
  if (!activeAction.value) return
  
  const success = await $activityCategoryStore.deleteCategory(activeAction.value.id)
  if (success) {
    await fetchListActivityCategory()
  }
  modalDeleteConfirm?.hide()
}

onMounted(() => {
  $page.setTitle('Daftar Kategori Laporan')
  fetchListActivityCategory()
})

watch(() => query.value.query, (newQuery) => {
  fetchListActivityCategory()
  if (newQuery) {
    query.value.page = 1; 
  }
});

watch(() => query.value.page, () => {
  fetchListActivityCategory()
})

watch(() => selected.value, (newValue) => {
  query.value.perpage = newValue
  query.value.page = 1
  fetchListActivityCategory()
})

const getDataTableNumber = (index: number, total: number) => {
  return (query.value.page - 1) * query.value.perpage + index + 1
}

</script>


<template>
  <div>
      <p class="p-0 mb-5">Berikut merupakan data kategori laporan</p>
      <div class="bg-white border rounded-lg shadow-sm">
      <div class="w-full p-6 flex space-x-3 justify-between">
        <general-text-input v-model="query.query" clearable id="inputSearchEmployee" placeholder="Search">
          <template #prefix>
            <icon-search size="20"/>
          </template>
        </general-text-input>
        <general-button
            label="Tambah Kategori"
            @on-click="navigateTo('/report-category/create')"
        >
          <template #prefix>
            <icon-plus size="20"/>
          </template>
        </general-button>
      </div>
  
      <div class="overflow-x-auto">
        <table 
          class="w-full text-sm text-left" 
          style="empty-cells: show;" 
          aria-describedby="Tabel Daftar Kategori Laporan"
        >
          <thead class="text-xs text-gray-700 bg-gray-50 border-b">
          <tr>
            <th scope="col" class="px-6 py-3 font-medium">
              No
            </th>
            <th scope="col" class="px-6 py-3 font-medium">
              Kode Laporan
            </th>
            <th scope="col" class="px-6 py-3 font-medium">
              Nama Laporan
            </th>
            <th scope="col" class="px-6 py-3 font-medium">
              Total Input
            </th>
            <th scope="col" class="px-6 py-3 font-medium">
              Deskripsi
            </th>
            <th scope="col" class="px-6 py-3 font-medium">
              Status
            </th>
            <th scope="col" class="px-6 py-3 font-medium"/>
          </tr>
          </thead>
          <tbody>
          <tr v-if="$activityCategoryStore.$state.isLoading.list">
            <td></td>
            <td></td>
            <td></td>
            <td class="h-24 flex items-center justify-center">
              <icon-circle-loading/>
            </td>
            <td></td>
          </tr>
          <tr v-else v-for="(activityCategory, i) in $activityCategoryStore.listActivityCategory" :key="activityCategory.id" class="bg-white border-b">
            <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
              {{ getDataTableNumber(i, 1) }}
            </td>
            <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
              {{ activityCategory.identity }}
            </td>
            <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
              {{ activityCategory.title }}
            </td>
            <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
              {{ activityCategory.category_input_count }}
            </td>
            <td class="px-6 py-4 font-normal text-gray-900">
              <div class="max-h-20 overflow-y-auto whitespace-normal break-words">
                {{ activityCategory.description }}
              </div>
            </td>
            <td class="px-6 py-4 font-normal whitespace-nowrap">
              <general-chip
                :label="activityCategory.status"
                :class="{
                  'bg-success-50 text-success-700': activityCategory.status === 'ACTIVE',
                  'bg-error-50 text-error-700': activityCategory.status === 'INACTIVE'
                }"
              />
            </td>
            <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
              <general-dropdown :id-dropdown="`dropdownAction-${i}`" id-activator="btnExport">
                <template #activator>
                  <general-icon-button
                      id="btnDropdownAction"
                      :data-dropdown-toggle="`dropdownAction-${i}`"
                      @click="activeAction = activityCategory"
                  >
                    <template #icon> 
                      <icon-three-dot/>
                    </template>
                  </general-icon-button>
                </template>

                <template #content>
                  <div class="py-2.5 px-4 flex items-center cursor-pointer hover:bg-gray-100">
                    <p class="text-sm" @click="goDetail(activityCategory.id)">Detail</p>
                  </div>
                  <div class="py-2.5 px-4 flex items-center cursor-pointer hover:bg-gray-100">
                    <p class="text-sm" @click="goToEdit(activityCategory.id)">Edit</p>
                  </div>
                  <div class="py-2.5 px-4 flex items-center cursor-pointer hover:bg-gray-100">
                    <p 
                      class="text-sm" 
                      :class="getStatusColor(activityCategory.status)"
                      @click="toggleStatus(activityCategory)"
                    >
                      {{ getStatusText(activityCategory.status) }}
                    </p>
                  </div>
                  <div class="py-2.5 px-4 flex items-center cursor-pointer hover:bg-gray-100 text-error-500">
                    <p class="text-sm" @click="handleDeleteCategory(activityCategory)">Hapus</p>
                  </div>
                </template>
              </general-dropdown>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
    <!-- Delete Confirmation Modal -->
    <ModalConfirmation
      id="modal-confirm-delete"
      :is-loading="$activityCategoryStore.isLoading.form"
      confirm-label="Konfirmasi"
      :subtitle="`Kamu yakin ingin menghapus ${activeAction?.title}?`"
      title="Konfirmasi Hapus"
      @mounted="modalDeleteConfirm = $event"
      @negative="modalDeleteConfirm?.hide()"
      @positive="confirmDelete()"
    />
    <div class="flex justify-between mt-2 items-center">
        <div class="flex">
            <select v-model="selected" style="width: 80px;  border-radius: 8px">
            <option v-for="option in options" :value="option.value">
                {{ option.text }}
            </option>
            </select>
            <p class="flex items-center ml-2">{{ selected }} From <span class="ml-2">{{ $activityCategoryStore.meta?.total || 0 }} Data</span></p>
        </div>
        <div>
          <general-pagination
                v-model="query.page"
                 :active-page="$activityCategoryStore.meta?.page"
                :total-page="Math.ceil($activityCategoryStore.meta?.total/$activityCategoryStore.meta?.perpage)"
                :is-prev-btn="false"
            />
        </div>
    </div>
  </div>
</template>

<style scoped>

</style>
