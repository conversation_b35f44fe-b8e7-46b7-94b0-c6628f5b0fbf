import { number, string } from 'yup'

interface ServerResponse<T> {
  code: number
  success: boolean
  message: string
  data: T
}

export interface User {
  corporate_id: string
  email: string
  id: string
  is_active: boolean
  name: string
  notes: string
  phone: string
  phone_number: string
  photo: string
  role: Role
  role_id: string
  token: string
  username: string
  user_type: string
  company: {
    id: string
    name: string
  }
  pin: string
}
export interface Dashboard {
  jumlah_karyawan: Number
  tugas_selesai: Number
  tugas_belum_selesai: Number
  tugas_bulan_ini: Number
  absensi_hari_ini: Number
  list_attendance: {
    nama_karyawan: string
    check_in_date: string
    check_in_time: string
    lokasi_check_in: string
  }[]
  data_statistik: {
    labelStatistik: string[]
    valueStatistik: number[]
    totalStatistik: number
  }
}

export interface Meta {
  page: number
  perpage: number
  pages: number
  from: number
  to: number
  total: number
}

export interface Employee {
  id: number
  name: string
  imei: string
  device_id: number
  sim_number: string
  task_uncompleted: string
  task_completed: string
}

export interface Attendance {
  name: string
  lokasi_check_out: string
  check_in_date: string
  check_in_time: string
  lokasi_check_in: string
  check_out_date: string
  check_out_time: string
}

export interface Task {
  id: number
  user_id: string
  employee_id?: Employee
  no_referensi: string
  title: string
  penerima: string
  deadline: string
  time: string
  alamat_asal: string
  alamat_tujuan: string
  nama_lokasi_asal?: string
  nama_lokasi_tujuan?: string
  destination_name?: string
  lat_asal?: string
  long_asal?: string
  lat_tujuan?: string
  long_tujuan?: string
  type?: TaskType
  description?: string
  status: string
  sign?: string
  foto_task?: string
  foto_task_url?: string | string[]
  parent_task_id: number | null // Include parent_task_id property
  child_tasks?: Task[] // Include child_tasks property for type completeness
  childTasks?: Task[] // This is for Detail, response from API
  sub_tasks?: Task[] // This is for Detail, response from API
  isOpen: boolean
  created_at?: string
  tipe_penugasan?: string
}

export interface TaskType {
  id: number
  type_identity: string
  name: string
  description: number
}

export interface ActivityCategory {
  id: number
  identity: string
  title: string
  description: string
  category_input_count: number
  status: 'ACTIVE' | 'INACTIVE'
}

export interface GeoLocation {
  // license: string
  // osm_type: string
  // osm_id: number
  // boundingbox: string[]
  // place_id: number
  lat: string
  long: string
  name: string
  address: string
//   place_rank: number
//   category: string
//   type: string
//   importance: number
}

export interface Location {
  lat: number | undefined
  long: number | undefined
  name: string | undefined
  address: string | undefined
  detail: any
  placeId: string | undefined
}

export interface FormLocation {
  id: number | undefined
  lat: number | null
  long: number | null
  name: string
  address: string
}

export interface notificationList {
  id: number | undefined
  title: string,
  body: string
  read_at: string
}

export interface UserPermissions {
  id: number | string;
  key: string;
  name: string;
  group: string;
}

export interface Permission {
  group: string;
  permissions: UserPermissions[];
}

export interface Role {
  id: number | string;
  name: string;
  created_at: string
  updated_at: string
  description: string
  company: any;
  permissions: UserPermissions[]
  group: string;
}


