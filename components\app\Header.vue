<script setup lang="ts">
import { useAuthStore } from "~/store/auth";
import { usePageStore } from "~/store/page";
import { watchDebounced } from "@vueuse/shared";
import type { ElementEvent } from "~/types/element";
import { useNotificationStore } from "~/store/notification";

let modalLogout: ElementEvent | null = null;

const $page = usePageStore();
const $auth = useAuthStore();
const $notification = useNotificationStore();
const $readNotification = useNotificationStore()

const { data } = useAuth();

onMounted(() => {
  fetchListNotification();
});

function fetchListNotification() {
  $notification.fetchListNotification({});
}

function readAllNotification() {
  $readNotification.markAllAsRead()
    .then(() => {
      fetchListNotification();
    })
}

const emit = defineEmits(["on-click-open-sidebar"]);

const version = computed(() => {
  return useRuntimeConfig().public.clientVersion;
});

const triggerModal = reactive({
  showNotification: false,
});

const unreadNotifications = computed(() => {
  return $notification.listNotification.filter(notification => notification.read_at === null);
});

const filteredNotifications = computed(() => {
  return $notification.listNotification.filter(notification => notification.read_at !== null);
});
</script>

<template>
  <div>
    <general-modal-confirmation
      id="modal-logout"
      title="Confirm Logout"
      subtitle="Are you sure you want to log out? This will end your current session. Press 'Log out' to continue."
      confirm-label="Log Out"
      @mounted="modalLogout = $event"
      @negative="modalLogout?.hide()"
      @positive="$auth.logout().then(() => modalLogout?.hide())"
    >
      <template #icon>
        <div class="p-2 aspect-square w-min rounded-full bg-error-50">
          <div class="p-2 aspect-square w-min rounded-full bg-error-100">
            <icon-info class="stroke-error-500" />
          </div>
        </div>
      </template>
    </general-modal-confirmation>
    <div class="w-full flex items-center justify-between">
      <div class="flex items-center">
        <general-icon-button
          class="block md:hidden mr-4"
          @click="emit('on-click-open-sidebar')"
        >
          <template #icon>
            <icon-menu />
          </template>
        </general-icon-button>

        <slot name="header" />

        <h1 v-if="$route.path !== '/dashboard'" class="text-2xl font-[600]">
          {{ $page.$state.title }}
        </h1>
      </div>

      <div class="flex items-center space-x-4">
        <div>
          <icon-bell
            @click="triggerModal.showNotification = true"
            class="stroke-gray-900 cursor-pointer"
            :notification="unreadNotifications.length > 0"
          />
        </div>
        <div class="text-right">
          <p class="text-base text-black font-[600] mb-1">
            {{ (data as any)?.data?.name ?? "User" }}
          </p>
        </div>

        <general-dropdown
          id-activator="dropdownActivator"
          id-dropdown="dropdown"
        >
          <template #activator>
            <general-icon-button
              id="dropdownActivator"
              data-dropdown-toggle="dropdown"
              rounded="full"
              :bordered="false"
              class="mx-3"
            >
              <template #icon>
                <icon-user class="stroke-white" />
              </template>
            </general-icon-button>
          </template>

          <template #content>
            <div
              class="py-2.5 px-4 flex items-center cursor-pointer hover:bg-gray-100"
            >
              <icon-version-control size="16" :color="undefined" class="mr-2" />
              <p class="text-sm">v{{ version }}</p>
            </div>
            <div
              class="py-2.5 px-4 flex items-center cursor-pointer hover:bg-gray-100"
              @click="modalLogout?.show()"
            >
              <icon-logout size="16" class="mr-2 stroke-gray-900" />
              <p class="text-sm">Log out</p>
            </div>
          </template>
        </general-dropdown>

        <general-icon-button v-show="false">
          <template #icon>
            <icon-bell class="stroke-gray-900" />
          </template>
        </general-icon-button>
      </div>
    </div>

    <transition name="fade">
      <div
        v-if="triggerModal.showNotification"
        class="fixed top-0 left-0 w-full h-full flex items-center justify-end z-50"
         data-modal-hide="default-modal"
      >
        <div
          class="bg-black bg-opacity-50 absolute top-0 left-0 w-full h-full"
        ></div>
        <div class="p-4 mr-5 bg-white rounded-lg shadow-lg h-full relative w-[700px]">
          <div class="p-3 m-2">
            <div class="p-7 m-3">
              <div class="flex justify-between">
                <h2 class="text-xl -ml-5 font-bold mb-5">Notifikasi</h2>
                <v-btn @click="triggerModal.showNotification = false" class="cursor-pointer"><icon-close class="stroke-black" /></v-btn>
              </div>
              <div v-if="$notification.$state.isLoading.list" class="mt-10">
                <icon-circle-loading />
              </div>
              <div v-else>
                <div v-if="$notification" class="p-0" style="height: 100vh;">
                  <div
                    class="mt-5 mb-2"
                    style="max-height: 100%; max-width: 700px; overflow: auto;"
                  >
                    <div
                      v-for="(notificationList, i) in unreadNotifications"
                      :key="notificationList.id"
                    >
                    <div v-if="notificationList?.read_at === null">
                      <div class="flex justify-between">
                      <p v-if="i === 0" class="p-0 mb-2 mt-2 font-medium">Terbaru</p>
                      <div v-if="i === 0" class="flex items-center">
                        <p 
                          class="p-0 mb-2 ml-2 mt-2 font-medium text-red-500 cursor-pointer" 
                          style="text-decoration: underline;" 
                          @click="readAllNotification"
                        >
                          Tandai semua sudah dibaca
                        </p>
                        <icon-eye 
                          class="p-0 mb-2 ml-2 mr-2 mt-2 stroke-error-500 cursor-pointer hover:bg-red-100"
                          @click="readAllNotification" 
                        />
                      </div>
                    </div>
                      <div class="p-5 mt-6 bg-error-100 rounded">
                        <div class="flex">
                          <div
                            class="aspect-square rounded-full mt-2 ml-7"
                          >
                            <div
                              class="p-3 aspect-square w-max rounded-full bg-error-200"
                            >
                            <div class="p-3 aspect-square w-max rounded-full bg-error-300">
                              <icon-file class="stroke-error-500"  />
                            </div>
                            </div>
                          </div>
                          <div class="p-2">
                            <p class="text-lg font-semibold">
                              {{ notificationList?.title }}
                            </p>
                            <p class="mt-4">
                              {{ notificationList?.body }}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                    </div>
                    <div
                      v-for="(notificationList, i) in filteredNotifications"
                      :key="notificationList.id"
                    >
                    <div>
                      <p v-if="i === 0" class="p-0 mb-2 mt-4 font-medium">Sudah Dibaca</p>
                      <div class="p-5 mt-6 bg-gray-100 rounded">
                        <div class="flex">
                          <div
                            class="aspect-square rounded-full mt-2 ml-7"
                          >
                            <div
                              class="p-3 aspect-square w-max rounded-full bg-gray-200"
                            >
                            <div class="p-3 aspect-square w-max rounded-full bg-gray-300">
                              <icon-file class="stroke-gray-500"  />
                            </div>
                            </div>
                          </div>
                          <div class="p-2">
                            <p class="text-lg font-semibold">
                              {{ notificationList?.title }}
                            </p>
                            <p class="mt-4">
                              {{ notificationList?.body }}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                    </div>

                  </div>
                </div>
                <div v-else class="flex justify-center">
                  <p class="m-0">Tidak Ada Notifikasi</p>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

