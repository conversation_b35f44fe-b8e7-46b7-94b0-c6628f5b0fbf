meta {
  name: Login Mobile
  type: http
  seq: 1
}

post {
  url: {{mobile_url}}/login
  body: multipartForm
  auth: none
}

headers {
  X-Mobile-App: Android
  X-Mobile-App-Version: 7.0
  X-Mobile-Imei: zein
  remember: true
  Accept: application/json
}

script:pre-request {
  // 
}

tests {
  // if(pm.response.to.have.status(200)) {
  //         pm.test("Status code is 200", function () {
  //         pm.response.to.have.status(200);
  //         var result = pm.response.json();
  //         pm.environment.set("mobileToken", result.token);
  //     });
  // }
}
