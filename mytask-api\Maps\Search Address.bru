meta {
  name: Search Address
  type: http
  seq: 2
}

get {
  url: {{base_url}}/v2/maps/search-place?lat=-6.92663985771417&long=107.61825621128084&text=transtrack
  body: multipartForm
  auth: bearer
}

query {
  lat: -6.92663985771417
  long: 107.61825621128084
  text: transtrack
  ~query: DONE
  ~from: 2021-08-19
  ~to: 2021-09-20
  ~field: name
  ~sort: asc
  ~status: 
}

auth:bearer {
  token: {{token}}
}

tests {
  // if(pm.response.code == 200) {
  //     pm.test("Status code is 200");
  // } else if(pm.response.code == 401) {
  //     pm.test("Status code is 401");
  // }
}
