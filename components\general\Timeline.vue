<script setup lang="ts">
const props = defineProps({
  timelines: {
    type: Array as () => any[],
    default: []
  }
})
</script>

<template>
  <ol class="border-gray-200">
    <li
      v-for="(timeline, i) in timelines"
      :key="timeline.id"
      class="grid grid-cols-10"
    >
      <div class="flex flex-col col-span-1 items-start pr-3">
        <span class="h-6 select-none">
          <slot name="label" :timeline="timeline"/>
        </span>
        <span class="h-full flex items-center select-none">
          <slot v-if="i < timelines.length - 1" name="prefix" :timeline="timeline"/>
        </span>
      </div>

      <div class="col-span-9 flex">
        <div class="w-auto flex flex-col justify-start items-center">
          <span
            class="flex items-center justify-center h-6 w-6 bg-primary-500 rounded-full text-white text-xs select-none">
            {{ i + 1 }}
          </span>
          <div class="border-l flex-1" :class="i === timelines.length - 1 ? 'border-transparent' : ''"/>
        </div>

        <div class="relative w-full pl-5" :class="i === timelines.length - 1 ? '' : 'pb-3'">
          <div class="p-5 border rounded-xl shadow">
            <slot name="item" :timeline="timeline"/>
          </div>
        </div>

        <div v-if="$slots.suffix" class="w-auto ml-5" :class="i === timelines.length - 1 ? '' : 'pb-3'">
          <slot name="suffix" :timeline="timeline" :index="i"/>
        </div>
      </div>
    </li>
  </ol>
</template>

<style scoped>

</style>