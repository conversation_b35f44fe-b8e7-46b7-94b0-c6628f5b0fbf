<script setup lang="ts">
import { useActivityReportStore } from '~/store/activity-report'
import { useRouter } from 'vue-router'
import { formatDateUTC } from '~/utils/functions';

const props = defineProps<{
  id: string | string[]
  isPublic?: boolean
}>()

const $router = useRouter()
const $activityReport = useActivityReportStore()

const fetchDetail = async () => {
  try {
    await $activityReport.fetchDetailActivityReport(Number(props.id))
  } catch (error) {
    console.error('Error fetching detail:', error)
  }
}

const goBack = () => {
  $router.push('/activity-report')
}

onMounted(() => {
  fetchDetail()
})
</script>

<template>
  <div class="bg-white p-6 rounded-lg shadow-sm">
    <div class="mt-2 mb-6">
      <button
        class="flex mb-5 items-center space-x-2 px-3 py-2 text-black rounded-md"
        @click="goBack"
      >
        <svg
          id="back-arrow"
          class="-ml-4"           
          height="24"
          viewBox="0 0 24 24"
          width="24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M0 0h24v24H0V0z" fill="none" opacity=".87"></path>
          <path
            d="M16.62 2.99c-.49-.49-1.28-.49-1.77 0L6.54 11.3c-.39.39-.39 1.02 0 1.41l8.31 8.31c.49.49 1.28.49 1.77 0s.49-1.28 0-1.77L9.38 12l7.25-7.25c.48-.48.48-1.28-.01-1.76z"
          ></path>
        </svg>
        <span class="font-bold text-xl">Kembali</span>
      </button>
    </div>

    <div class="p-3 border-2 border-grey">
      <div class="p-2">
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-xl font-bold">Informasi Karyawan</h1>
        </div>
    
        <div v-if="$activityReport.isLoading.detail">
          <icon-circle-loading />
        </div>
        <div v-else-if="$activityReport.detailActivityReport" class="space-y-6">
          <div class="grid grid-cols-2 gap-6">
            <div>
              <label class="block mb-1.5 text-sm font-[600] text-gray-700">Karyawan</label>
              <p class="text-base text-gray-900">
                {{ $activityReport.detailActivityReport.employee?.name }}
              </p>
            </div>
            <div>
              <label class="block mb-1.5 text-sm font-[600] text-gray-700">Judul Laporan</label>
              <p class="text-base text-gray-900">
                {{ $activityReport.detailActivityReport.activity_category?.title }}
              </p>
            </div>
            <div>
              <label class="block mb-1.5 text-sm font-[600] text-gray-700">Waktu Pembuatan</label>
              <p class="text-base text-gray-900">
                {{ formatDateUTC($activityReport.detailActivityReport.created_at) }}
              </p>
            </div>
            <div>
              <label class="block mb-1.5 text-sm font-[600] text-gray-700">Lokasi</label>
              <p class="text-base text-gray-900">
                {{ $activityReport.detailActivityReport.location_address }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="p-3 border-2 border-grey mt-8">
      <div class="p-2">
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-xl font-bold">Informasi Laporan Karyawan</h1>
        </div>
    
        <div v-if="$activityReport.isLoading.detail">
          <icon-circle-loading />
        </div>
        <div v-else-if="$activityReport.detailActivityReport" class="space-y-6">
          <div class="grid grid-cols-2 gap-6">
            <template v-for="input in $activityReport.detailActivityReport.activity_category?.category_input" :key="input.id">
              <div v-if="input.input_type?.type === 'SHORT_TEXT'">
                <label class="block mb-1.5 text-sm font-[600] text-gray-700">{{ input.title }}</label>
                <p class="text-base text-gray-900 break-words whitespace-normal">
                  {{ input?.activity_report_value?.[0]?.text || '-' }}
                </p>
              </div>

              <div v-if="input.input_type?.type === 'LOCATION'">
                <label class="block mb-1.5 text-sm font-[600] text-gray-700">{{ input.title }}</label>
                <p class="text-base text-gray-900">
                  {{ input?.activity_report_value?.[0]?.text || '-' }}
                </p>
              </div>

              <div v-if="input.input_type?.type === 'DATE_TIME'">
                <label class="block mb-1.5 text-sm font-[600] text-gray-700">{{ input.title }}</label>
                <p class="text-base text-gray-900">
                  {{ input?.activity_report_value?.[0]?.date_time || '-' }}
                </p>
              </div>

              <div v-if="input.input_type?.type === 'LONG_TEXT'">
                <label class="block mb-1.5 text-sm font-[600] text-gray-700">{{ input.title }}</label>
                <p class="text-base text-gray-900 break-words whitespace-normal">
                  {{ input?.activity_report_value?.[0]?.text || '-' }}
                </p>
              </div>

              <div v-if="input.input_type?.type === 'SINGLE_OPTION'">
                <label class="block mb-1.5 text-sm font-[600] text-gray-700">{{ input.title }}</label>
                <div class="flex flex-wrap gap-2 mt-2">
                  <template v-for="(item, index) in input?.activity_report_value" :key="index">
                    <div class="px-2 py-1 bg-white border border-gray-300 rounded text-gray-700">
                      {{ item.input_option?.name || '-' }}
                    </div>
                  </template>
                  <div v-if="!input?.activity_report_value || input.activity_report_value.length === 0" 
                       class="px-4 py-2 bg-white border border-gray-300 rounded-full text-gray-700">
                    -
                  </div>
                </div>
              </div>

              <div v-if="input.input_type?.type === 'MULTIPLE_OPTION'">
                <label class="block mb-1.5 text-sm font-[600] text-gray-700">{{ input.title }}</label>
                <div class="flex flex-wrap gap-2 mt-2">
                  <template v-for="(item, index) in input?.activity_report_value" :key="index">
                    <div class="px-2 py-1 bg-white border border-gray-300 rounded text-gray-700">
                      {{ item.input_option?.name || '-' }}
                    </div>
                  </template>
                  <div v-if="!input?.activity_report_value || input.activity_report_value.length === 0" 
                       class="px-4 py-2 bg-white border border-gray-300 rounded-full text-gray-700">
                    -
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>

    <div class="p-3 border-2 border-grey mt-8">
      <div class="p-2">
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-xl font-bold">File Pendukung</h1>
        </div>
    
        <div v-if="$activityReport.isLoading.detail">
          <icon-circle-loading />
        </div>
        <div v-else-if="$activityReport.detailActivityReport" class="space-y-6">
          <div class="grid grid-cols-2 gap-6">
            <template v-for="input in $activityReport.detailActivityReport.activity_category?.category_input" :key="input.id">
              <div v-if="input.input_type?.type === 'IMAGE'">
                <label class="block mb-1.5 text-sm font-[600] text-gray-700">{{ input.title }}</label>
                <div v-if="input?.activity_report_value?.[0]?.activity_photo_url">
                  <nuxt-img 
                    :src="input.activity_report_value[0].activity_photo_url"
                    alt="Activity Image"
                    class="h-25 w-auto object-contain rounded-lg cursor-pointer"
                  />
                </div>
                <p v-else class="text-base text-gray-900">-</p>
              </div>
            </template>
            <template v-for="input in $activityReport.detailActivityReport.activity_category?.category_input" :key="input.id">
              <div v-if="input.input_type?.type === 'FILE'">
                <label class="block mb-1.5 text-sm font-[600] text-gray-700">{{ input.title }}</label>
                <div v-if="input?.activity_report_value && input.activity_report_value.length > 0">
                  <div v-for="item in input.activity_report_value" :key="item.id" class="mb-3">
                    <div v-if="item.file_url">
                      <div v-if="item.file_url.toLowerCase().endsWith('.pdf')" 
                           class="flex items-center cursor-pointer"
                           @click="onClickImage(item.file_url)">
                        <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-red-500 mr-2">
                          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                          <polyline points="14 2 14 8 20 8"></polyline>
                          <path d="M9 15h6"></path>
                          <path d="M9 11h6"></path>
                        </svg>
                        <span class="text-sm text-gray-700">PDF Document</span>
                      </div>
                      
                      <nuxt-img 
                        v-else
                        :src="item.file_url"
                        alt="Activity File"
                        class="h-32 w-auto object-contain rounded-lg cursor-pointer"
                        @click="onClickImage(item.file_url)"
                      />
                    </div>
                  </div>
                </div>
                <p v-else class="text-base text-gray-900">-</p>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
