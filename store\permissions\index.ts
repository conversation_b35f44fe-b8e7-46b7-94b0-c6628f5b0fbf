import { defineStore } from 'pinia'
import type { Permissions } from '~/types/server-response'
import { toast } from 'vue3-toastify'
import api from '~/services/api'

export const usePermissionsStore = defineStore('permissions', {
  state: () => ({
    loading: false,
    permissions: [] as Permissions[],
  }),

  actions: {
    async fetchPermissions(useGo = true) {
      this.loading = true
      try {
        const { data } = await api.get('/api/v1/permissions', { useGo })
        this.permissions = data
      } catch (error: any) {
        toast.error(error.message || 'Failed to fetch permissions')
      } finally {
        this.loading = false
      }
    },
  }
})
