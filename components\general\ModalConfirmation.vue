<script setup lang="ts">

const emit = defineEmits(['mounted', 'negative', 'positive'])
const props = defineProps({
  isLoading: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: ''
  },
  subtitle: {
    type: String,
    default: ''
  },
  confirmLabel: {
    type: String,
    default: ''
  }
})

</script>

<template>
  <general-modal id="modal-confirmation" title="" @mounted="emit('mounted', $event)">
    <template #body>
      <div class="flex flex-col gap-2 items-center">
        <slot name="icon"/>

        <div class="space-y-2 mb-5">
          <p class="text-lg text-gray-900 font-[600] text-center">{{ title }}</p>
          <p class="text-sm text-gray-500 font-[400] text-center">{{ subtitle }}</p>
        </div>

        <div class="flex w-full gap-3">
          <general-outlined-button label="Tidak, Kembali" class="w-full" @on-click="emit('negative')"/>
          <general-button :loading="props.isLoading" :label="confirmLabel" class="w-full" @on-click="emit('positive')"/>
        </div>
      </div>
    </template>
  </general-modal>
</template>

<style scoped>

</style>
