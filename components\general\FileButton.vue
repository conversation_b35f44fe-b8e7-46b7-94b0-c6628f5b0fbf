<script setup lang="ts">
import {computed} from "vue";

const props = defineProps({
  id: {
    type: String,
    default: ''
  },
  type: {
    type: String as () => "button" | "submit" | "reset" | undefined,
    default: 'button',
  },
  label: {
    type: String,
    default: ''
  },
  subtitle: {
    type: String,
    default: ''
  },
  progressPercentage: {
    type: String,
    default: 0
  },
  disabled: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['on-click'])

const buttonClass = computed((): string => {
  const baseClass = 'py-2.5 px-3.5 text-sm font-[600] text-gray-700 stroke-gray-700 border rounded-lg flex items-center justify-center transition'
  const hoverClass = props.disabled || props.loading ? '' : 'hover:bg-[#0000000a]'
  const disabled = props.disabled ? 'bg-gray-100 opacity-75' : ''
  const cursor = props.disabled || props.loading ? 'cursor-not-allowed' : 'cursor-pointer'
  return `${baseClass} ${disabled} ${cursor} ${hoverClass}`
})
</script>

<template>
  <div
    :id="props.id"
    :type="type"
    :disabled="props.disabled"
    :class="buttonClass"
    @click="emit('on-click')"
  >
    <svg v-if="props.loading" aria-hidden="true" class="w-6 h-6 animate-spin text-gray-200 fill-gray-400" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
      <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
    </svg>
    <span v-else class="space-x-2 w-full flex justify-between">
      <div class="rounded-full bg-primary-50 bg-opacity-20 p-[5px] mb-auto">
        <div class="rounded-full bg-primary-50 bg-opacity-20 p-[5px]">
          
          <icon-file size="20" class="stroke-red-500"/>
        </div>
      </div>
      <div class="w-full flex flex-col justify-start">
        <span class="text-left font-semibold">{{props.label}}</span>
        <span class="text-left text-sm text-gray-500 font-normal">{{props.subtitle}}</span>
        <div class="w-full flex flex-row justify-between" v-if="progressPercentage">
          <div class="w-full h-2 bg-primary-50 bg-opacity-20 rounded-full my-auto mr-4">
            <div class="w-full h-2 bg-primary-500 rounded-full" :style="`width: ${progressPercentage}%`" />
          </div>
          <span class="font-normal">
            {{ progressPercentage }}%
          </span>
        </div>
      </div>
      <div class="mt-1">

        <slot name="suffix"/>
      </div>
    </span>
  </div>
</template>

<style scoped> </style>
