import {defineStore} from 'pinia'
import {toast} from 'vue3-toastify'
import type {ValidationError} from 'yup'
import api from '~/services/api'
import type {FormLocation, Meta, Task} from '~/types/server-response'

export enum StatusTask {
  TODO = 'TO DO',
  DOING = 'DOING',
  DONE = 'DONE',
}

export const useTasksStore = defineStore('tasks', {
  state: () => ({
    isLoading: {
      list: false,
      form: false,
      exportExcel: false,
      exportPdf: false,
      detail: false,
      search: false,
    },
    listTask: [] as Task[],
    detailTask: null as null | Task,
    meta: undefined as Meta | undefined,
  }),
  actions: {
    async fetchDetailTask(id: string) {
      this.isLoading.detail = true
      try {
        const response = await api.get(`/task/view-task/${id}`)
        this.detailTask = response.data
      } catch (e) {
        toast.error((e as ValidationError).message)
      } finally {
        this.isLoading.detail = false
      }
    },

    async fetchListTasks({
      query = '',
      field = '',
      sort = '',
      status = '',
      from = '',
      to = '',
      employee_id = '', page = 1,
    }) {
      this.isLoading.list = true

      const result = await api.get('/task/view-all', {
        queryParams: {
          query,
          field,
          sort,
          status,
          from,
          to,
          employee_id,
          page: `${page}`,
          perpage: '15',
        },
      })

      if (result.status) {
        // Merge data
        const tasks: Task[] = result.data
        // const tasksMap = new Map()

        // tasks.forEach((task) => {
        //   if (task.parent_task_id !== null) {
        //     const parentId = task.parent_task_id
        //     if (!tasksMap.has(parentId)) {
        //       tasksMap.set(parentId, { child_tasks: [task] })
        //     } else {
        //       tasksMap.get(parentId).child_tasks.push(task)
        //     }
        //   }
        // })
        //
        // tasks.forEach((task) => {
        //   if (task.parent_task_id === null && tasksMap.has(task.id)) {
        //     task.child_tasks = tasksMap.get(task.id).child_tasks
        //   }
        // })

        this.listTask = tasks
        this.meta = result.meta
      }

      this.isLoading.list = false
    },

    async createTask(
      type_id: any,
      employee_ids: any,
      title: string,
      referenceNumber: string,
      description: string,
      lat_asal: any,
      long_asal: any,
      destinations: FormLocation[],
      deadline: any,
      task_work_type: any,
      pickupNameLocation: string
    ) {
      this.isLoading.form = true
      

      let destinationNameLocation = []
      destinations.forEach((el) => {
        destinationNameLocation.push(el.name)
      })

      try {
        const res = await api.post('/task/create-task', {
          body: {
            type_id,
            employee_ids,
            title,
            no_referensi: referenceNumber,
            description,
            lat_asal,
            long_asal,
            destinations: destinations.map((e) => ({
              lat_tujuan: e.lat,
              long_tujuan: e.long,
            })),
            deadline,
            task_work_type,
            nama_lokasi_asal: pickupNameLocation,
            nama_lokasi_tujuan: destinationNameLocation,
          },
        })
        this.isLoading.form = false
        toast.success('Success creating task')
        return true
      } catch (e) {
        toast.error((e as ValidationError).message)
        throw e
      } finally {
        this.isLoading.form = false
      }
    },

    async updateTask(
      taskId: any,
      type_id: any,
      employee_id: string,
      title: string,
      referenceNumber: string,
      description: string,
      lat_asal: any,
      long_asal: any,
      destinations: FormLocation[],
      deadline: any,
      task_work_type: any,
      origin_name: string,
      hasChildren: boolean
    ) {
      this.isLoading.form = true
    
      try {
        let newDestination: any[] = [];
    
        const primaryLat = destinations[0]?.lat;
        const primaryLong = destinations[0]?.long;
        const primaryName = destinations[0]?.name;
    
        destinations.forEach((el) => {
          if (!el.id && (el.lat !== primaryLat || el.long !== primaryLong || el.name !== primaryName)) {
            newDestination.push({
              nama_tujuan: el.name,
              lat_tujuan: el.lat,
              long_tujuan: el.long,
            });
          }
        });
    
        let body: any = {
          _method: 'PUT',
          employee_id,
          title,
          no_referensi: referenceNumber,
          description,
          lat_asal,
          long_asal,
          deadline,
          task_work_type,
          type_id,
          origin_name,
        }
    
        if (newDestination.length > 0) {
          body.new_location = newDestination
        }
    
        if (!hasChildren) {
          body = {
            ...body,
            lat_tujuan: primaryLat,
            long_tujuan: primaryLong,
            destination_name: primaryName,
          };
        }
    
        if (newDestination.length === 0) {
          delete body.new_location
        }
    
        const res = await api.post(`/task/update-task/${taskId}`, {
          body,
        })
    
        if (hasChildren) {
          await Promise.all(
            destinations
              .filter((e) => e.id)
              .map((e) =>
                api.post(`/task/update-task/${e.id}`, {
                  body: {
                    _method: 'PUT',
                    lat_tujuan: e.lat,
                    long_tujuan: e.long,
                    destination_name: e.name,
                  },
                })
              )
          )
        }
    
        this.isLoading.form = false
        if (!res.status) {
          toast.error(res.message.prod)
        } else {
          toast.success(res.message.prod)
        }
        return true
      } catch (e) {
        this.isLoading.form = false
        toast.error((e as ValidationError).message)
        throw e
      } finally {
        this.isLoading.form = false
      }
    },

    async deleteTask(id: string) {
      this.isLoading.form = true

      try {
        await api.delete(`/task/delete-task/${id}`)
        toast.success('Success')
        return true
      } catch (e) {
        toast.error((e as ValidationError).message)
        return false
      } finally {
        this.isLoading.form = false
      }
    },
    async fetchExportExcel() {
      this.isLoading.exportExcel = true

      const result = await api.get('/task/export-excel')

      if (result.status) {
        window.open(result.url)
      }

      this.isLoading.exportExcel = false
    },

    async fetchExportPDF() {
      this.isLoading.exportPdf = true

      const result = await api.get('/task/export-pdf')

      if (result.status) {
        window.open(result.url)
      }

      this.isLoading.exportPdf = false
    },

    async cancelTask(id: string) {
      this.isLoading.form = true

      try {
        await api.post(`/task/canceled-task/${id}`, {
          body: {
            _method: 'PUT'
          }
        })
        toast.success('Success')
        return true
      } catch (e) {
        toast.error((e as ValidationError).message)
        return false
      } finally {
        this.isLoading.form = false
      }
    },
  },
})
