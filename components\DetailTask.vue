<script setup lang="ts">
import {useRoute, useRouter} from 'vue-router'
import {useTasksStore} from '~/store/tasks'
import {copyTextToClipboard, formatDate, onClickImage} from '~/utils/functions'
import {toast} from 'vue3-toastify'
import type {Task} from '~/types/server-response'
import type {ElementEvent} from "~/types/element";

const $route = useRoute()
const $router = useRouter()
const $tasks = useTasksStore()
const config = useRuntimeConfig()

const isEdit = ref(false)
let modalCancel: ElementEvent | null = null


const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  isPublic: {
    type: Boolean,
    required: true,
  },
})

const detailTask = computed<null | Task>(() => $tasks.$state.detailTask)
const taskType = ref('')

const fetchDetailTask = () => {
  $tasks.fetchDetailTask(props.id)
}

onMounted(() => {
  fetchDetailTask()

  if (window.location.hash === '#edit') {
    goToEdit()
  }
})

watch(
  () => $tasks.detailTask,
  (value) => {
    taskType.value = value.tipe_penugasan
    let myIframe = document.querySelector('#myIframe')
    let myURL = 'https://live.transtrack.id/api/v1/map-planner?map=google_road'
    let imei = value?.employee_id?.imei
    let origin: string[] = [value?.lat_asal ?? '', value?.long_asal ?? '']
    let destination: string[] = []
    if (value?.lat_tujuan && value?.long_tujuan) {
      destination = [value?.lat_tujuan ?? '', value?.long_tujuan ?? '']
    }
    if (imei) {
      myURL = myURL + '&imei=' + imei
    }
    myURL = myURL + '&pickup[]=' + origin.join(',')
    if (destination.length > 0)
      myURL = myURL + '&dropoff[]=' + destination.join(',')
    if (value?.childTasks && value?.childTasks.length > 0) {
      value.childTasks
        .map((e) => [e.lat_tujuan, e.long_tujuan])
        .forEach((e) => {
          myURL = myURL + '&dropoff[]=' + e.join(',')
        })
    }

    if (
      value?.sign ===
      'https://api-mytask-staging.transtrack.id/task/url-photo-generator?url='
    ) {
      value.sign = null
    }
    if (
        value?.foto_task_url ===
      'https://api-mytask-staging.transtrack.id/task/url-photo-generator?url='
    ) {
      value.foto_task_url = null
    }

    const tokenFMS = config.public.accessTokenFMS

    populateIframe(myIframe, myURL, [['Authorization', `Bearer ${tokenFMS}`]])
  }
)

const goBack = () => {
  navigateTo('/tasks')
}

const urlImage = (value: string | undefined) => {
  if(!value) return ''
  const url = new URL(value);
  const params = new URLSearchParams(url.search);

  return params.get('url');
}

function populateIframe(iframe: any, url: string, headers: any) {
  try {
    let xhr = new XMLHttpRequest()
    xhr.open('GET', url)
    xhr.onreadystatechange = handler
    xhr.responseType = 'blob'
    headers.forEach(function (header: any) {
      xhr.setRequestHeader(header[0], header[1])
    })
    xhr.send()

    function handler() {
      if (this.readyState === this.DONE) {
        if (this.status === 200) {
          iframe.src = URL.createObjectURL(this.response)
        } else {
          console.error('Request failed', this)
        }
      }
    }
  } catch (e) {}
}

function calculateDuration(finishedDate: Date, startedDate: Date) {
  if (!finishedDate || !startedDate) {
    return "N/A";
  }

  const finished = new Date(finishedDate);
  const started = new Date(startedDate);
  const durationInMilliseconds = finished - started;

  const minutes = Math.floor(durationInMilliseconds / 60000);
  const seconds = Math.floor((durationInMilliseconds % 60000) / 1000);

  return `${minutes} Menit ${seconds} Detik`;
}

async function onCopyLink() {
  try {
    let currentLink = window.location.origin + $route.fullPath

    if (!props.isPublic) {
      const secretKey = config.public.secretKey

      const { token } = useAuth()
      const payload = {
        fullPath: $route.fullPath,
        accessToken: token.value,
        taskId: props.id,
      }

      const { base64EncryptedData } = await encrypt(payload, secretKey)
      currentLink =
          window.location.origin +
          '/t' +
          `?d=${encodeURIComponent(base64EncryptedData)}`

      await copyTextToClipboard(currentLink)
    } else {
      await copyTextToClipboard(currentLink)
    }

    toast.success('URL Disalin')

  } catch(e) {
    console.error(e)
  }

}

const goToEdit = async () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth', // Smooth scrolling behavior
  })

  isEdit.value = !isEdit.value
  window.location.hash = 'edit'
  fetchDetailTask()
}

async function encrypt(
  payload: any,
  secretKey: string
): Promise<{ iv: string; base64EncryptedData: string }> {
  const alg = {
    name: 'AES-GCM',
    length: 256,
  }

  const base64Decode = (str: string) =>
    Uint8Array.from(atob(str), (c) => c.charCodeAt(0))

  const iv = base64Decode(secretKey)
  const key = await crypto.subtle.importKey(
    'raw',
    new TextEncoder().encode(secretKey),
    alg,
    false,
    ['encrypt']
  )

  const encodedPayload = new TextEncoder().encode(JSON.stringify(payload))

  const encryptedData = await crypto.subtle.encrypt(
    {
      name: alg.name,
      iv: iv,
    },
    key,
    encodedPayload
  )

  const base64EncryptedData = btoa(
    String.fromCharCode(...new Uint8Array(encryptedData))
  )

  const base64IV = btoa(String.fromCharCode(...iv))

  return {
    iv: base64IV,
    base64EncryptedData: base64EncryptedData,
  }
}
</script>

<template>
  <div>
  <div v-if="!isEdit">
    <general-modal-confirmation
        id="modal-cancel"
        :is-loading="$tasks.isLoading.form"
        confirm-label="Ya,Batalkan"
        subtitle="Apakah kamu yakin ingin membatalkan tugas?"
        title="Konfirmasi Pembatalan"
        @mounted="modalCancel = $event"
        @negative="modalCancel?.hide()"
        @positive="$tasks.cancelTask(props.id).then(() => {fetchDetailTask(); modalCancel?.hide();})">
      <template #icon>
        <div class="p-2 aspect-square w-min rounded-full bg-error-50">
          <div class="p-2 aspect-square w-min rounded-full bg-error-100">
            <icon-info class="stroke-error-500"/>
          </div>
        </div>
      </template>
    </general-modal-confirmation>

    <p class="text-gray-500 -mt-14 mb-10">
      Lengkapi data berikut Untuk membuat tugas baru
    </p>
    <div class="flex justify-between mt-4">
      <button
        v-if="!props.isPublic"
        @click="goBack"
        class="flex mb-5 items-center space-x-2 px-3 py-2 text-black rounded-md"
      >
        <svg
          class="-ml-4"
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          id="back-arrow"
        >
          <path fill="none" d="M0 0h24v24H0V0z" opacity=".87"></path>
          <path
            d="M16.62 2.99c-.49-.49-1.28-.49-1.77 0L6.54 11.3c-.39.39-.39 1.02 0 1.41l8.31 8.31c.49.49 1.28.49 1.77 0s.49-1.28 0-1.77L9.38 12l7.25-7.25c.48-.48.48-1.28-.01-1.76z"
          ></path>
        </svg>
        <span class="font-bold text-xl">Kembali</span>
      </button>

      <button
        class="flex mb-5 items-center space-x-2 px-3 py-2 text-black rounded-md"
        @click="onCopyLink"
      >
        <svg
          fill="none"
          height="24"
          viewBox="0 0 24 24"
          width="24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12.7651 15.8261L9.70425 18.887C9.70425 18.887 9.70425 18.8871 9.70418 18.8871C9.70418 18.8871 9.70418 18.8872 9.70411 18.8872C8.43835 20.153 6.37868 20.1531 5.11277 18.8872C4.49952 18.2739 4.16187 17.4586 4.16187 16.5915C4.16187 15.7244 4.49952 14.9092 5.11256 14.2959C5.11263 14.2959 5.1127 14.2958 5.11277 14.2957L8.17364 11.2348C8.59621 10.8121 8.59621 10.1269 8.17357 9.7043C7.751 9.28173 7.06574 9.28173 6.6431 9.7043L3.58223 12.7652C3.58202 12.7655 3.5818 12.7657 3.58158 12.766C2.56004 13.7879 1.99744 15.1465 1.99744 16.5915C1.99744 18.0368 2.56026 19.3956 3.5823 20.4177C4.63725 21.4725 6.02285 22 7.40851 22C8.79418 22 10.1799 21.4725 11.2347 20.4177C11.2347 20.4177 11.2347 20.4175 11.2347 20.4175L14.2956 17.3566C14.7182 16.934 14.7182 16.2487 14.2955 15.8261C13.873 15.4035 13.1878 15.4035 12.7651 15.8261Z"
            fill="black"
          />
          <path
            d="M22.0024 7.40856C22.0024 5.96323 21.4395 4.6044 20.4175 3.58235C18.3077 1.47261 14.8748 1.47268 12.7651 3.58235C12.7651 3.5825 12.7649 3.58257 12.7649 3.58272L9.70407 6.64344C9.28142 7.06601 9.28142 7.75134 9.70407 8.17391C9.91546 8.3853 10.1924 8.49092 10.4693 8.49092C10.7462 8.49092 11.0233 8.38523 11.2345 8.17391L14.2953 5.11318C14.2954 5.11304 14.2955 5.11297 14.2957 5.11282C15.5614 3.84706 17.6211 3.84699 18.887 5.11282C19.5002 5.72608 19.838 6.54142 19.838 7.40856C19.838 8.27563 19.5004 9.09083 18.8872 9.70409L18.887 9.7043L15.8262 12.7652C15.4036 13.1878 15.4036 13.8731 15.8262 14.2957C16.0376 14.507 16.3145 14.6127 16.5914 14.6127C16.8684 14.6127 17.1454 14.507 17.3567 14.2957L20.4176 11.2348C20.4178 11.2346 20.418 11.2343 20.4182 11.2341C21.4398 10.2122 22.0024 8.85354 22.0024 7.40856Z"
            fill="black"
          />
          <path
            d="M8.17379 15.8261C8.38511 16.0374 8.66209 16.1431 8.93899 16.1431C9.21597 16.1431 9.49294 16.0374 9.70426 15.8261L15.8261 9.70423C16.2488 9.28166 16.2488 8.5964 15.8261 8.17376C15.4036 7.75119 14.7183 7.75119 14.2957 8.17376L8.17379 14.2956C7.75115 14.7183 7.75115 15.4035 8.17379 15.8261Z"
            fill="black"
          />
        </svg>
        <span class="font-bold text-xl">Salin Link</span>
      </button>
    </div>
    <div
      class="bg-white mb-6 border px-10 pt-12 rounded-lg shadow-sm overflow-hidden"
    >
      <div class="grid gap-3 grid-cols-2 mb-6">
        <general-text-input
          disabled
          id="TaskType"
          label="Tipe Penugasan"
          placeholder="Pilih Tipe Tugas"
          :modelValue="detailTask?.type?.name"
        />

        <general-text-input
          disabled
          id="title"
          label="Judul Penugasan"
          placeholder="Ex: Pengiriman Device"
          :modelValue="detailTask?.title"
        />
      </div>
      <div class="grid gap-3 grid-cols-2 mb-6">
        <general-text-input
          disabled
          id="title"
          label="karyawan"
          placeholder="pilih karyawan"
          :modelValue="detailTask?.employee_id?.name"
        />
        <general-text-input
          disabled
          id="title"
          label="Tenggat Waktu"
          placeholder="Tenggat Waktu"
          :modelValue="detailTask?.deadline"
        />
      </div>
      <div
        class="relative h-[300px] w-full bg-gray-100 rounded-lg mb-4 overflow-hidden border mb-6"
      >
        <iframe id="myIframe" class="w-full h-full" src=""></iframe>
      </div>

      <div class="w-full gap-3 mb-6">
        <div
          class="flex flex-col items-start w-full mb-4"
        >
          <div class="flex justify-between w-full">
            <div>
              <label
                for="pickup-position"
                class="mb-1.5 text-sm font-[600] text-gray-700"
              >
                Lokasi Awal
              </label>
            </div>
          </div>

          <div
            class="grid gap-6 md:grid-cols-2 p-2 border rounded-lg mb-4 bg-gray-100 w-full"
          >
            <div class="col-span-2 md:col-span-1">
              <div class="flex flex-col bg-gray-100 p-4 rounded-lg mb-4 w-full">
                <div>
                  <p class="text-lg font-semibold text-gray-700">
                    Nama Lokasi Awal
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    {{ detailTask?.nama_lokasi_asal }}
                  </p>
                </div>
              </div>
            </div>
            <div class="col-span-2 md:col-span-1">
              <div class="flex flex-col bg-gray-100 p-4 rounded-lg mb-4">
                <div>
                  <p class="text-lg font-semibold text-gray-700">
                    Alamat Lokasi Awal
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    {{ detailTask?.alamat_asal }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div
          v-if="detailTask?.alamat_tujuan"
          class="flex flex-col items-start w-full"
        >
          <div class="w-full">
            <div class="flex justify-between">
              <label
                for="pickup-position"
                class="mb-1.5 text-sm font-[600] text-gray-700"
              >
                Lokasi Tujuan
              </label>
              <label
                for="pickup-position"
                class="mb-1.5 text-sm font-[600] text-gray-700"
              >
              </label>
            </div>

            <div
              class="grid gap-12 md:grid-cols-2 p-2 border rounded-lg mb-4 bg-gray-100 w-full"
            >
              <div class="col-span-2 md:col-span-1 w-full">
                <div
                  class="flex flex-col bg-gray-100 p-4 rounded-lg mb-4 w-full"
                >
                  <div>
                    <p class="text-lg font-semibold text-gray-700">
                      Alamat Tempat Tujuan
                    </p>
                  </div>
                  <div>
                    <p class="text-sm text-gray-500">
                      {{ detailTask?.nama_lokasi_tujuan }}
                    </p>
                  </div>
                </div>
              </div>
              <div class="col-span-2 md:col-span-1 w-full">
                <div
                  class="flex flex-col bg-gray-100 p-4 rounded-lg mb-4 w-full"
                >
                  <div>
                    <p class="text-lg font-semibold text-gray-700">
                      Alamat Tujuan
                    </p>
                  </div>
                  <div>
                    <p class="text-sm text-gray-500">
                      {{ detailTask?.alamat_tujuan }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div
          v-for="(childLocation, i) in detailTask?.childTasks"
          :key="i"
          class="flex flex-col items-start w-full"
        >
          <div class="w-full">
            <div class="flex justify-between">
              <label
                class="mb-1.5 text-sm font-[600] text-gray-700"
                for="pickup-position"
              >
                Lokasi Tujuan {{ i + 1 }}
              </label>
              <label
                class="mb-1.5 text-sm font-[600] text-gray-700"
                for="pickup-position"
              >
              </label>
            </div>

            <div
              class="grid gap-12 md:grid-cols-2 p-2 border rounded-lg mb-4 bg-gray-100 w-full"
            >
              <div class="col-span-2 md:col-span-1 w-full">
                <div
                  class="flex flex-col bg-gray-100 p-4 rounded-lg mb-4 w-full"
                >
                  <div>
                    <p class="text-lg font-semibold text-gray-700">
                      Nama Lokasi Tujuan
                    </p>
                  </div>
                  <div>
                    <p class="text-sm text-gray-500">
                      {{ childLocation?.destination_name }}
                    </p>
                  </div>
                </div>
              </div>
              <div class="col-span-2 md:col-span-1 w-full">
                <div
                  class="flex flex-col bg-gray-100 p-4 rounded-lg mb-4 w-full"
                >
                  <div>
                    <p class="text-lg font-semibold text-gray-700">
                      Alamat Lokasi Tujuan
                    </p>
                  </div>
                  <div>
                    <p class="text-sm text-gray-500">
                      {{ childLocation?.alamat_tujuan }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="grid gap-3 grid-cols-2 mb-6">
        <general-text-input
          disabled
          id="note"
          label="Deskripsi Penugasan"
          placeholder="Ex:Pengiriman"
          :modelValue="detailTask?.description"
        />

        <general-text-input
          disabled
          id="referenceNumber"
          label="No Referensi"
          placeholder="Masukan No Referensi"
          :modelValue="detailTask?.no_referensi"
        />
      </div>

      <label
        for="pickup-position"
        class="mb-3 text-sm font-[600] text-gray-700"
      >
        Tipe Pengerjaan Tugas
      </label>
      <div class="flex mt-5">
        <div class="flex items-center me-4">
          <general-radio
            required
            name="worktypedetailterususun"
            id="workTypeDetailTersusun"
            label="Tersusun"
            class="mb-8"
            value="TERSUSUN"
            :checked="taskType === 'TERSUSUN'"
            :disabled="true"
          />
        </div>
        <div class="flex items-center me-4">
          <general-radio
            v-model="taskType"
            required
            name="worktypedetailbebas"
            id="workTypeDetailBebas"
            label="Bebas"
            class="mb-8"
            value="BEBAS"
            :checked="taskType === 'BEBAS'"
            :disabled="true"
          />
        </div>
      </div>
    </div>
    <div v-if="detailTask?.childTasks?.length > 0">
      <label
        for="lampiran-penugasan"
        class="mb-5 text-lg font-[600] text-gray-700"
      >
        Lampiran Penugasan
      </label>
      <div
        v-for="(childLocation, i) in detailTask?.childTasks"
        :key="i"
        class="bg-white mb-6 border px-10 pt-12 mt-5 rounded-lg shadow-sm overflow-hidden"
      >
        <label
          for="title-location"
          class="mb-5 text-lg font-[600] text-gray-700"
        >
          Lokasi {{ i + 1 }}
        </label>
        <div class="grid gap-3 grid-cols-3 mb-6 mt-3">
          <label for="tenggat" class="mb-0 text-sm font-[600] text-gray-700">
            Tenggat Upload Tugas
          </label>

          <label
            for="pickup-position"
            class="mb-0 text-sm font-[600] text-gray-700"
          >
            Waktu Upload Tugas
          </label>

          <label
            for="duration"
            class="mb-0 text-sm font-[600] text-gray-700"
          >
            Durasi Pengerjaan
          </label>
          

          <label
            for="pickup-position"
            class="mb-5 text-sm font-[400] text-gray-700"
          >
            <span v-if="detailTask.deadline">
              {{ formatDate(detailTask.deadline) }}
            </span>
          </label>

          <label
            for="pickup-position"
            class="mb-5 text-sm font-[400] text-gray-700"
          >
            <span v-if="urlImage(childLocation.foto_task_url) && childLocation.updated_at">
              {{ formatDatetoTime(childLocation.updated_at) }}
            </span>
          </label>

          <label
            for="duration"
            class="mb-0 text-sm font-[600] text-gray-700"
          >
          <p v-if="childLocation?.finished_date">{{ calculateDuration(childLocation?.finished_date, childLocation?.started_date) }}</p>
          </label>
          
          <label
            for="pickup-position"
            class="mb-0 text-sm font-[600] text-gray-700"
          >
            Tanda Tangan
            <nuxt-img
              v-if="childLocation.sign"
              :src="`https://api-mytask-staging.transtrack.id/task/url-photo-generator?url=${childLocation.sign}`"
              alt="Image"
              class="p-10 w-full h-64 object-contain"
              @click="
                onClickImage(
                  `https://api-mytask-staging.transtrack.id/task/url-photo-generator?url=${childLocation.sign}`
                )
              "
            />

            <empty-image v-else />
          </label>
          <label
            for="pickup-position"
            class="mb-0 text-sm font-[600] text-gray-700"
          >
            E-POD
            <div
            :class="
              urlImage(childLocation.foto_task_url)
                ? 'w-full flex flex-wrap p-2'
                : 'w-full h-64 text-center p-2'
            "
          >
            <nuxt-img
                v-for="json in Object.values(childLocation.foto_task_url)"
                v-if="urlImage(childLocation.foto_task_url)"
                :src="`${json}`"
              alt="Image"
              class="p-10 h-64 object-contain cursor-pointer"
              @click="
                onClickImage(
                  `${json}`
                )
              "
            />
            <empty-image v-else />
          </div>
          </label>
        </div>
      </div>
    </div>

    <div v-if="detailTask?.childTasks?.length < 1">
      <label
        for="lampiran-penugasan"
        class="mb-5 text-lg font-[600] text-gray-700"
      >
        Lampiran Penugasan
      </label>
      <div
        class="bg-white mb-6 border px-10 pt-12 mt-5 rounded-lg shadow-sm overflow-hidden"
      >
        <label
          for="title-location"
          class="mb-5 text-lg font-[600] text-gray-700"
        >
          Lokasi 1
        </label>
        <div class="grid gap-3 grid-cols-2 mb-6 mt-3">
          <label for="tenggat" class="mb-0 text-sm font-[600] text-gray-700">
            Tenggat Upload Tugas
          </label>

          <label
            for="pickup-position"
            class="mb-0 text-sm font-[600] text-gray-700"
          >
            Waktu Upload Tugas
          </label>

          <span >
            <label
              for="pickup-position"
              class="mb-5 text-sm font-[400] text-gray-700"
            >
              <span v-if="detailTask?.deadline">
                {{ formatDate(detailTask?.deadline) }}
              </span>
            </label>
          </span>
          <label
            for="pickup-position"
            class="mb-5 text-sm font-[400] text-gray-700"
          >
            <span v-if="detailTask?.created_at">
              {{ formatDatetoTime(detailTask?.created_at) }}
            </span>
          </label>
          <label
            for="pickup-position"
            class="mb-0 text-sm font-[600] text-gray-700"
          >
            Tanda Tangan
          </label>
          <label
            for="pickup-position"
            class="mb-0 text-sm font-[600] text-gray-700"
          >
            E-POD
          </label>

          <div class="p-2 w-full h-64">
            <nuxt-img
                v-if="urlImage(detailTask.sign)"
              :src="`${detailTask.sign}`"
                alt="Image Sign"
              class="p-10 w-full h-64 object-contain cursor-pointer"
              @click="
                onClickImage(
                  `${detailTask.sign}`
                )
              "
            />
            <empty-image v-else />
          </div>

          <div class="p-2 w-full flex flex-wrap h-64">
            <nuxt-img
                v-for="json in encodeJson(detailTask.foto_task)"
                v-if="urlImage(detailTask.foto_task)"
                :src="`${json}`"
                alt="Image Task"
                class="p-10 h-64 object-contain cursor-pointer"
              @click="onClickImage(`${json}`)"
            />
            <empty-image v-else />
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="!props.isPublic && detailTask?.status !== 'DONE'"
      class="flex justify-between"
    >
      <general-button
        @click="goToEdit"
        :label="'Edit'"
        :loading="$tasks.isLoading.form"
        class="w-52"
      />
      <general-outlined-button
        v-if="detailTask?.status !== 'CANCEL'"
        :label="'Cancel'"
        :loading="$tasks.isLoading.form"
        class="w-52"
        @click="modalCancel?.show()"
      />
    </div>
  </div>

  <div v-else>
    <forms-form-task :task="detailTask"/>
  </div>
  </div>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
