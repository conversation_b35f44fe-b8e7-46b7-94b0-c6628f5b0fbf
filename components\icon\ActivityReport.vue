<script setup lang="ts">
const props = defineProps({
    size: {
        type: String,
        default: "24",
    },
});
</script>

<template>
    <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" viewBox="0 0 21 21" fill="none">
     <path fill-rule="evenodd" clip-rule="evenodd" d="M12.8968 0.8421C13.3108 0.8421 13.6468 1.1781 13.6468 1.5921C13.6468 2.0061 13.3108 2.3421 12.8968 2.3421H5.62882C3.12082 2.3421 1.49982 4.0661 1.49982 6.7361V14.8181C1.49982 17.5231 3.08182 19.2031 5.62882 19.2031H14.2328C16.7408 19.2031 18.3618 17.4821 18.3618 14.8181V7.7791C18.3618 7.3651 18.6978 7.0291 19.1118 7.0291C19.5258 7.0291 19.8618 7.3651 19.8618 7.7791V14.8181C19.8618 18.3381 17.5998 20.7031 14.2328 20.7031H5.62882C2.26182 20.7031 -0.000183105 18.3381 -0.000183105 14.8181V6.7361C-0.000183105 3.2111 2.26182 0.8421 5.62882 0.8421H12.8968ZM15.0122 7.6719C15.3402 7.9259 15.4002 8.3969 15.1462 8.7239L12.2162 12.5039C12.0942 12.6619 11.9142 12.7649 11.7162 12.7889C11.5162 12.8159 11.3182 12.7579 11.1602 12.6349L8.34222 10.4209L5.81122 13.7099C5.66322 13.9019 5.44122 14.0029 5.21622 14.0029C5.05622 14.0029 4.89522 13.9519 4.75922 13.8479C4.43122 13.5949 4.36922 13.1239 4.62222 12.7959L7.61522 8.9059C7.73722 8.7469 7.91822 8.6439 8.11622 8.6189C8.31822 8.5929 8.51622 8.6489 8.67322 8.7739L11.4932 10.9889L13.9602 7.8059C14.2142 7.4769 14.6842 7.4159 15.0122 7.6719ZM17.9672 0C19.4412 0 20.6392 1.198 20.6392 2.672C20.6392 4.146 19.4412 5.345 17.9672 5.345C16.4942 5.345 15.2952 4.146 15.2952 2.672C15.2952 1.198 16.4942 0 17.9672 0ZM17.9672 1.5C17.3212 1.5 16.7952 2.025 16.7952 2.672C16.7952 3.318 17.3212 3.845 17.9672 3.845C18.6132 3.845 19.1392 3.318 19.1392 2.672C19.1392 2.025 18.6132 1.5 17.9672 1.5Z" fill="#EF3434"/>
</svg>
</template>

<style scoped></style>
