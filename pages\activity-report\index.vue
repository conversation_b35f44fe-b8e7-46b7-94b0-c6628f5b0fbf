<script setup lang="ts">
import {usePageStore} from "~/store/page";
import {watchDebounced} from "@vueuse/shared";
import {useTaskTypeStore} from "~/store/task-type";
import ModalConfirmation from "~/components/general/ModalConfirmation.vue";
import type {ElementEvent} from "~/types/element";
import { useActivityReportStore } from '~/store/activity-report'
import { format } from 'date-fns'
import { useEmployeesStore } from '~/store/employees'

const $page = usePageStore()
const $taskType = useTaskTypeStore()
const activityReportStore = useActivityReportStore()
const $employees = useEmployeesStore()

let modalDeleteConfirm: ElementEvent | null = null

function dateString(dates: Date) {
  const date = new Date(dates)
  return format(date, 'yyyy-MM-dd')
}

function dateStringDMY(dates: Date) {
  const date = new Date(dates)
  return format(date, 'dd/MM/yyyy')
}

function clearDate() {
  query.value.range = []
  query.value.from = ''
  query.value.to = ''
  query.value.labelFrom = ''
  query.value.labelTo = ''
}

const initialQuery = {
  query: '',
  field: 'created_at',
  sort: 'desc',
  status: '',
  from: '',
  to: '',
  labelFrom: '',
  labelTo: '',
  range: [] as Date[],
  page: 1,
  perpage: 10,
  category_id: '',
  employee_id: ''
}

const activeAction = ref()
const query = ref({...initialQuery, ...activityReportStore.filterState})

const selected = ref('10')
const options = ref([
  { text: '10', value: '10' },
  { text: '20', value: '20' },
  { text: '30', value: '30' },
  { text: '40', value: '40' },
  { text: '50', value: '50' }
])

onMounted(async () => {
  $page.setTitle('List Laporan Aktifitas Karyawan')
  await $employees.fetchListEmployee({ perpage: '1000' })
  activityReportStore.fetchListActivityCategory()
})

// Update selected value from store if available
if (activityReportStore.filterState.perpage) {
  selected.value = activityReportStore.filterState.perpage.toString();
}

const displayDateRange = computed(() => {
  if (!query.value.range || query.value.range.length < 2) return ''
  
  const startDate = new Date(query.value.range[0])
  const endDate = new Date(query.value.range[1])
  
  return `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`
})

function clearDateRange() {
  query.value.range = []
}

const categoryItems = computed(() => 
  activityReportStore.listActivityCategory?.map(category => ({
    text: category.title,
    value: category.id.toString()
  })) || []
)


watch(categoryItems, (newItems) => {
  if (newItems.length > 0 && !query.value.category_id) {
    query.value.category_id = newItems[0].value
    fetchActivityReports()
  }
}, { immediate: true })


watch([() => query.value, () => selected.value], () => {
}, { deep: true })

watch(() => query.value.page, () => {
  fetchActivityReports()
})

watch(() => selected.value, (newValue) => {
  query.value.perpage = newValue
  query.value.page = 1
  fetchActivityReports()
})

function fetchActivityReports() {
  // Update store's filterState
  activityReportStore.updateFilterState({
    ...query.value,
    perpage: parseInt(selected.value)
  });
  
  // Fetch data using current filters
  activityReportStore.fetchListActivityReport({
    ...query.value,
    perpage: selected.value,
    category_id: query.value.category_id,
    page: query.value.page
  });
}

const getDataTableNumber = (index: number, total: number) => {
  return (query.value.page - 1) * query.value.perpage + index + 1
}

watch(
  () => query.value.range,
  (value) => {
    if (value[0]) {
      query.value.from = dateString(value[0])
      query.value.labelFrom = dateStringDMY(value[0])
    }

    if (value[1]) {
      query.value.to = dateString(value[1])
      query.value.labelTo = dateStringDMY(value[1])
    }
  },
  { deep: true }
)

const employeeItems = computed(() => 
  $employees.listEmployee.map(employee => ({
    text: employee.name,
    value: employee.id.toString()
  })) || []
)

function applyFilter() {
  query.value.page = 1
  fetchActivityReports()
}

function resetFilter() {
  query.value = { ...initialQuery }
  query.value.category_id = categoryItems.value[0]?.value || ''
  query.value.employee_id = ''
  clearDate()
  fetchActivityReports()
}

function handleExport(type: 'excel' | 'pdf') {
  const params = {
    query: query.value.query,
    field: query.value.field,
    sort: query.value.sort,
    from: query.value.from,
    to: query.value.to,
    employee_id: query.value.employee_id,
    category_id: query.value.category_id,
    perpage: '-1' //Fetch all data for export, ignoring pagination
  }

  if (type === 'excel') {
    activityReportStore.fetchExportExcel(params)
  } else {
    activityReportStore.fetchExportPDF(params)
  }
}

// Modify navigateToDetail to ensure state is saved before navigation
function navigateToDetail(reportId: string) {
  // Update store's filterState before navigation
  activityReportStore.updateFilterState({
    ...query.value,
    perpage: parseInt(selected.value)
  });
  
  // Navigate to detail page
  navigateTo(`/activity-report/${reportId}`);
}

</script>


<template>
  <div>
    <p class="p-0 mb-5">Berikut adalah daftar Laporan Aktifitas Karyawan</p>
    <div class="bg-white border rounded-lg shadow-sm">
        <div class="p-6">
            <div class="w-full flex mb-4">
              <div>
                  <general-combobox
                    id="selectFilterEmployees"
                    v-model="query.category_id"
                    has-search-field
                    id-trigger="triggerDropdownCategory"
                    id-target="dropdownCategory"
                    label="Kategori"
                    plain
                    placeholder="Kategori"
                    :items="categoryItems"
                    :loading="activityReportStore.isLoading.list"
                    @update:model-value="fetchActivityReports"
                  />
              </div>
            </div>
            <div class="w-full flex">
              <general-text-input v-model="query.query" @update:model-value="fetchActivityReports" clearable id="inputSearchEmployee" placeholder="Search">
                <template #prefix>
                  <icon-search size="20"/>
                </template>
              </general-text-input>
              <general-dropdown
          id-activator="btnDropdownFilter"
          id-dropdown="dropdownFilter"
        >
          <template #activator>
            <general-outlined-button
              label="Filters"
              id="btnDropdownFilter"
              data-dropdown-toggle="dropdownFilter"
              class="!h-11 ml-3"
            >
              <template #prefix>
                <icon-filter-lines size="20" />
              </template>
            </general-outlined-button>
          </template>

          <template #content>
            <label
              for="status"
              class="mb-1.5 text-lg font-[600] text-gray-700 p-4"
            >
              Filter
            </label>
            <form>
              <div class="grid gap-6 md:grid-cols-2 p-4">
                <div>
                  <general-range-datepicker
                    v-model="query.range"
                    id="inputFilterDateRange"
                    label="Date Range"
                    placeholder="Select Date Range"
                    :enable-time-picker="false"
                    class="flex-1"
                  />
                </div>
              </div>
              <div class="grid gap-6 md:grid-cols-1 p-4">
                <div>
                  <general-combobox
                    id="selectFilterEmployees"
                    v-model="query.employee_id"
                    has-search-field
                    id-trigger="triggerDropdownEmployee"
                    id-target="dropdownEmployee"
                    label="Karyawan"
                    plain
                    placeholder="Pilih Karyawan"
                    :items="employeeItems"
                    :loading="$employees.isLoading.list"
                  />
                </div>
                <div class="flex gap-4">
                  <general-button
                    id="btnFilterApply"
                    label="Terapkan Filter"
                    class="p-2"
                    @click="applyFilter"
                  >
                  </general-button>
                  <general-outlined-button
                    id="btnFilterReset"
                    label="Reset"
                    class="p-2"
                    @click="resetFilter"
                  >
                  </general-outlined-button>
                </div>
              </div>
            </form>
          </template>
        </general-dropdown>
            </div>
            <div class="w-full">
            <general-dropdown
            id-activator="btnDropdownCalendar"
            id-dropdown="dropdownCalendar"
          > 
            <template #activator>
              <div
                v-show="query.labelFrom != '' && query.labelTo != ''"
                class="flex justify-center w-[250px] items-center mt-5 m-0 text-sm text-gray-700 border rounded-lg p-3"
                style="white-space: nowrap"
              >
                <div v-show="query.labelFrom != '' && query.labelTo != ''">
                  {{ query.labelFrom }} - {{ query.labelTo }}
                  <span @click="clearDate" style="cursor: pointer">
                  <icon-close
                  width="20"
                  height="20"  
                  style="
                      aspect-ratio: 210 / 210;
                      object-fit: cover;
                      display: inline-block;
                    " 
                  class="stroke-gray-700 ml-2" />
                </span>
                </div>
              </div>
            </template>

            <template #content>
              <general-range-datepicker
                v-model="query.range"
                inline
                :enable-time-picker="false"
              />
            </template>
          </general-dropdown>
          <div class="flex space-x-3 w-full items-center justify-end">
            <general-dropdown id-activator="btnExport" id-dropdown="dropdownExport">
              <template #activator>
                <general-outlined-button
                  id="btnExport"
                  data-dropdown-toggle="dropdownExport"
                  label="Export"
                  class="border-primary-500 border-2 text-primary-500"
                >
                  <template #prefix>
                    <icon-download-cloud size="20" class="stroke-primary-500" />
                  </template>
                </general-outlined-button>
              </template>
  
              <template #content>
                <div 
                  class="py-2.5 px-4 flex items-center cursor-pointer hover:bg-gray-100"
                  @click="handleExport('excel')"
                >
                  <p class="text-sm">Export Excel</p>
                </div>
                <div 
                  class="py-2.5 px-4 flex items-center cursor-pointer hover:bg-gray-100"
                  @click="handleExport('pdf')"
                >
                  <p class="text-sm">Export PDF</p>
                </div>
              </template>
            </general-dropdown>
          </div>
          </div>
        </div>

      <div class="overflow-x-auto">
        <div id="activity-report-table-desc" class="sr-only">
          Tabel Report Activity
        </div>
        <table 
          class="w-full text-sm text-left" 
          style="empty-cells: show;"
          aria-describedby="activity-report-table-desc"
        >
          <thead class="text-xs text-gray-700 bg-gray-50 border-b">
          <tr>
            <th scope="col" class="px-6 py-3 font-medium">
              No
            </th>
            <th scope="col" class="px-6 py-3 font-medium">
              Karyawan
            </th>
            <th scope="col" class="px-6 py-3 font-medium">
              Judul Laporan
            </th>
            <th scope="col" class="px-6 py-3 font-medium">
              Waktu Pembuatan
            </th>
            <th scope="col" class="px-6 py-3 font-medium">
              Lokasi Pengisian
            </th>
            <th scope="col" class="px-6 py-3 font-medium"/>
          </tr>
          </thead>
          <tbody>
          <tr v-if="activityReportStore.isLoading.list">
            <td colspan="6" class="h-24 text-center">
              <icon-circle-loading/>
            </td>
          </tr>
          <tr v-else-if="activityReportStore.listActivityReport.length === 0">
            <td colspan="6" class="p-8" style="text-align: center">
              <nuxt-img
                src="/dashboard/Illustrasi_tidak_ada_data.png"
                width="300"
                height="300"
                class="rounded-lg p-8"
                alt="App icon"
                style="aspect-ratio: 300 / 300; object-fit: cover; display: inline-block"
              />
              <p class="text-sm font-bold">Tidak ada data tersedia</p>
            </td>
          </tr>
          <tr 
            v-else 
            v-for="(report, i) in activityReportStore.listActivityReport" 
            :key="report.id" 
            class="bg-white border-b"
          >
            <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
              {{ getDataTableNumber(i, 1) }}
            </td>
            <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
              {{ report.employee?.name }}
            </td>
            <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
              {{ report.activity_category?.title }}
            </td>
            <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
              {{ formatDate(report.created_at) }}
            </td>
            <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
              {{ report.location_address }}
            </td>
            <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
            </td>

            <td class="px-1 py-4 font-normal text-gray-900 whitespace-nowrap">
              <general-dropdown :id-dropdown="`dropdownAction-${i}`" id-activator="btnExport">
                <template #activator>
                  <general-icon-button
                      id="btnDropdownAction"
                      :data-dropdown-toggle="`dropdownAction-${i}`"
                      @click="'#'"
                  >
                    <template #icon> 
                      <icon-three-dot/>
                    </template>
                  </general-icon-button>
                </template>

                <template #content>
                  <div 
                    class="py-2.5 px-4 flex items-center cursor-pointer hover:bg-gray-100"
                    @click="navigateToDetail(report.id)"
                  >
                    <p class="text-sm">Detail</p>
                  </div>
                </template>
              </general-dropdown>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
      <div class="flex justify-between mt-2 p-3 items-center">
        <div class="flex">
          <select v-model="selected" style="width: 80px; border-radius: 8px">
            <option v-for="option in options" :value="option.value">
              {{ option.text }}
            </option>
          </select>
          <p class="flex items-center ml-2">
            {{ selected }} From <span class="ml-2">{{ activityReportStore.meta?.total || 0 }} Data</span>
          </p>
        </div>
        <div>
          <general-pagination
            v-model="query.page"
            :active-page="activityReportStore.meta?.page"
            :total-page="Math.ceil(activityReportStore.meta?.total/activityReportStore.meta?.perpage)"
            :is-prev-btn="false"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>

</style>
