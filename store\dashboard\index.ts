import { defineStore } from 'pinia'
import api from '~/services/api'
import { toast } from 'vue3-toastify'
import { ValidationError } from 'yup'

export const useDashboardStore = defineStore('dashboard', {
  state: () => ({
    data: {},
    isLoading: false,
  }),
  actions: {
    async fetchDashboard() {
      this.isLoading = true

      try {
        const res = await api.get('/dashboard/dashboard')
        this.data = res
        return res
      } catch (e) {
        toast.error((e as ValidationError).message)
        throw e
      } finally {
        this.isLoading = false
      }
    },
  },
})
