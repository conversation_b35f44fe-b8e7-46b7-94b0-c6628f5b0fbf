import { defineStore } from 'pinia'
import { toast } from 'vue3-toastify'
import type { ValidationError } from 'yup'
import api from '~/services/api'
import type { Meta } from '~/types/server-response'

interface ActivityCategory {
  id: number
  title: string
}

interface ActivityReport {
  id: number;
  activity_category?: {
    id: number;
    title: string;
    description?: string;
  };
  created_at: string;
  updated_at: string;
}

interface State {
  isLoading: {
    list: boolean
    categories: boolean
    exportExcel: boolean
    exportPdf: boolean
    detail: boolean
  }
  listActivityReport: []
  listActivityCategory: ActivityCategory[]
  meta: Meta | undefined
  detailActivityReport: ActivityReport | null
  lastErrorToastTime: number
  filterState: {
    query: string
    field: string
    sort: string
    status: string
    from: string
    to: string
    labelFrom: string
    labelTo: string
    range: Date[]
    page: number
    perpage: number | string
    category_id: string
    employee_id: string
  }
}


export const useActivityReportStore = defineStore('activityReport', {
  state: (): State => ({
    isLoading: {
      list: false,
      categories: false,
      exportExcel: false,
      exportPdf: false,
      detail: false
    },
    listActivityReport: [],
    listActivityCategory: [],
    meta: undefined,
    detailActivityReport: null,
    lastErrorToastTime: 0,
    filterState: {
      query: '',
      field: 'created_at',
      sort: 'desc',
      status: '',
      from: '',
      to: '',
      labelFrom: '',
      labelTo: '',
      range: [],
      page: 1,
      perpage: 10,
      category_id: '',
      employee_id: ''
    }
  }),

  actions: {
    updateFilterState(filters: Partial<State['filterState']>) {
      this.filterState = { ...this.filterState, ...filters };
    },

    async fetchListActivityCategory({
      field = 'created_at',
      sort = 'desc',
      status = 'ACTIVE',
      perpage = '-1'
    } = {}) {
      this.isLoading.categories = true
      try {
        const queryParams: Record<string, string> = {
          field,
          sort,
          status,
          perpage
        }
        
        const result = await api.get('/activity-category/list-category', {
          queryParams
        })
        if (result.status) {
          this.listActivityCategory = result.data
        }
      } catch (error) {
        toast.error((error as ValidationError).message)
      } finally {
        this.isLoading.categories = false
      }
    },

    async fetchListActivityReport({
      query = '',
      field = 'created_at',
      sort = 'desc',
      page = 1,
      perpage = '10',
      from = '',
      to = '',
      employee_id = '',
      category_id = ''
    } = {}) {
      this.isLoading.list = true

      try {
        const queryParams: Record<string, string> = {
          field,
          sort,
          page: `${page}`,
          perpage
        }

        if (query.trim()) queryParams.query = query
        if (from.trim()) queryParams.from = from
        if (to.trim()) queryParams.to = to
        if (employee_id.trim()) queryParams.employee_id = employee_id
        if (category_id.trim()) queryParams.category_id = category_id

        //Fetch all active categories for activity report
        const result = await api.get('/activity-report/list-activity', {
          queryParams
        })

        if (result.status) {
          this.listActivityReport = result.data
          this.meta = result.meta
        }
      } catch (error) {
        const now = Date.now();
        // Prevent showing duplicate error toasts within 1 second
        if (now - this.lastErrorToastTime > 1000) {
          toast.error((error as ValidationError).message);
          this.lastErrorToastTime = now;
        } 
      } finally {
        this.isLoading.list = false
      }
    },

    async fetchExportExcel(params: Record<string, string>) {
      this.isLoading.exportExcel = true

      try {

        const filteredParams = Object.fromEntries(
          Object.entries(params).filter(([_, value]) => value !== '')
        );

        const result = await api.get('/activity-report/list-activity-excel', {
          queryParams: filteredParams,
        })

        if (result.status) {
          window.open(result.url)
        }
      } catch (error) {
        toast.error((error as ValidationError).message)
      } finally {
        this.isLoading.exportExcel = false
      }
    },

    async fetchExportPDF(params: Record<string, string>) {
      this.isLoading.exportPdf = true

      try {
        const result = await api.get('/activity-report/list-activity-pdf', {
          queryParams: params,
        })
        if (result.status) {
          window.open(result.url)
        }
      } catch (error) {
        toast.error((error as ValidationError).message)
      } finally {
        this.isLoading.exportPdf = false
      }
    },

    async fetchDetailActivityReport(id: number) {
      this.isLoading.detail = true
      try {
        const result = await api.get(`/activity-report/detail-activity/${id}`)
        this.detailActivityReport = result.data
      } catch (error) {
        toast.error((error as ValidationError).message || 'Failed to fetch detail')
        throw error
      } finally {
        this.isLoading.detail = false
      }
    }
  }
})
