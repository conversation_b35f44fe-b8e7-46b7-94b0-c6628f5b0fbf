<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: String || Number || null,
    default: null,
  },
  id: {
    type: String,
    default: '',
  },
  type: {
    type: String,
    default: 'text',
  },
  label: {
    type: String || null,
    default: null,
  },
  placeholder: {
    type: String,
    default: '',
  },
  min: {
    type: Number,
    default: 0,
  },
  max: {
    type: Number,
    default: 999,
  },
  clearable: {
    type: Boolean,
    default: false,
  },
  required: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['input', 'on-click-clear', 'update:modelValue'])
const slots = useSlots()

function showHideClearButton(command: 'show' | 'hide') {
  const $buttonClearElement: HTMLElement | null = document.getElementById(
    `${props.id}Clear`
  )

  switch (command) {
    case 'show':
      $buttonClearElement?.classList.add('opacity-1')
      $buttonClearElement?.classList.remove('opacity-0')
      break
    case 'hide':
      $buttonClearElement?.classList.add('opacity-0')
      $buttonClearElement?.classList.remove('opacity-1')
      break
  }
}

function onClickClear() {
  emit('on-click-clear')
  emit('update:modelValue', '')
}

const inputClass = computed(() => {
  const baseClass =
    'block w-full h-full left-0 top-0 border border-gray-300 text-gray-900 rounded-lg focus:ring-primary-500 focus:border-primary-500'
  const disabled = props.disabled ? 'bg-gray-100 cursor-not-allowed' : ''
  const paddingLeft = slots.prefix ? 'pl-10' : 'pl-2.5'
  const paddingRight =
    slots.suffix && props.clearable
      ? 'pr-[68px]'
      : slots.suffix || props.clearable
      ? 'pr-10'
      : 'pr-2.5'
  const hideSpinButton =
    '[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none'
  return `${baseClass} ${paddingLeft} ${paddingRight} ${disabled} ${hideSpinButton}`
})

const inputPaddingTop = ref('')

/**
 * Function to observe the height changes of an element and update the input padding top accordingly.
 * This corresponds to the "prefix-above" element.
 *
 */
const observeHeightChanges = () => {
  // Create a new ResizeObserver
  const observer = new ResizeObserver((entries) => {
    const entry = entries[0]

    // Get the height of the element
    const height = entry.contentRect.height

    // Calculate the new input padding top value
    const paddingTop = `${height < 8 ? 8 : height.toFixed(0)}px`

    // Update the input padding top
    inputPaddingTop.value = paddingTop
  })

  // Get the element with the specified ID
  const element = document.getElementById(props.id + 'prefix-above')

  // If the element exists, observe it for height changes
  if (element !== null) {
    observer.observe(element)
  }
}

// Call the observeHeightChanges function when the component is mounted
onMounted(() => {
  observeHeightChanges()
})

watch(
  () => props.modelValue,
  (value: string | number) => {
    if (value) {
      if (props.type === 'number') {
        const number = Number(value)
        value =
          number > props.max
            ? props.max
            : number < props.min
            ? props.min
            : `${value}`
      } else if (props.type === 'text' && typeof value === 'string') {
        value = value.slice(0, props.max)
      }
    }
    emit('update:modelValue', value ?? '')
  }
)
</script>

<template>
  <div class="flex flex-col items-start">
    <label
      v-if="props.label"
      :for="id"
      class="mb-1.5 text-sm font-[600] text-gray-700"
    >
      {{ props.label }}
      <span v-if="props.required" class="text-primary-500">*</span>
    </label>

    <slot name="prefix-above" />

    <div class="w-full relative stroke-gray-500">
      <div :id="props.id + 'prefix-above'" class="absolute pl-2">
      </div>
      <div
        class="absolute pl-3 inset-y-0 left-0 flex items-center pointer-events-none"
      >
        <slot name="prefix" />
      </div>
      <input
        :id="props.id"
        :value="props.modelValue"
        :type="props.type"
        :placeholder="props.placeholder"
        :class="inputClass"
        :min="props.min"
        :max="props.max"
        step="any"
        :disabled="props.disabled"
        :style="{ 'padding-top': `${inputPaddingTop}` }"
        @input="emit('update:modelValue', $event.target.value)"
        autocomplete="off"
      />
      <div
       v-if="$slots.suffix || props.clearable"
        class="absolute pr-3 inset-y-0 right-0 flex items-center"
        :class="$slots.suffix && props.clearable ? 'space-x-2' : ''"
      >
        <icon-close
          v-if="props.clearable && props.modelValue"
          :id="`${props.id}Clear`"
          size="20"
          class="cursor-pointer transition-opacity hover:stroke-gray-700"
          @click.stop="onClickClear"
        />
        <div class="pointer-events-none">
          <slot name="suffix" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
