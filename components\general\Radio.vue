<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    required: true,
  },
  value: {
    type: String,
    required: true,
  },
  name: {
    type: String,
    default: '',
  },
  label: {
    type: String,
    default: '',
  },
  color: {
    type: String as () => 'default' | 'primary' | 'success',
    default: 'default',
  },
  checked: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:model-value'])

const inputClass = () => {
  const baseClass = 'rounded-xl border-gray-300 focus:ring-0 cursor-pointer'
  let color = ''
  switch (props.color) {
    case 'default':
      color = 'text-gray-500'
      break
    case 'primary':
      color = 'text-primary-500'
      break
    case 'success':
      color = 'text-success-500'
      break
  }
  return `${baseClass} ${color}`
}

const labelClass = () => {
  const baseClass = 'ml-1 text-xs font-semibold cursor-pointer'
  let color = ''
  switch (props.color) {
    case 'default':
      color = 'text-gray-500'
      break
    case 'primary':
      color = 'text-primary-500'
      break
    case 'success':
      color = 'text-success-500'
      break
  }
  return `${baseClass} ${color}`
}
</script>

<template>
  <div class="flex items-center">
    <input
      v-model="props.modelValue"
      :id="props.id"
      :name="props.name"
      type="radio"
      :value="props.value"
      :class="inputClass()"
      @change="emit('update:model-value', props.value)"
      :checked="props.checked"
      :disabled="props.disabled"
    />
    <label :for="props.id" :class="labelClass()">{{ props.label }}</label>
  </div>
</template>

<style scoped></style>
