<script setup lang="ts">
import VueDatePicker from "@vuepic/vue-datepicker";
import type { DatePickerInstance } from "@vuepic/vue-datepicker";
import "@vuepic/vue-datepicker/dist/main.css";
import { format } from "date-fns";
import type { VNodeRef } from "vue";

const props = defineProps({
    modelValue: {
        type: [Date, Object as () => null],
        default: null,
    },
    id: {
        type: String,
        default: "",
    },
    label: {
        type: String,
        default: "",
    },
    placeholder: {
        type: String,
        default: "",
    },
    required: {
        type: Boolean,
        default: false,
    },
    minDate: {
        type: Date || null,
        default: null,
    },
    maxDate: {
        type: Date || null,
        default: null,
    },
    noMaxDate: {
        type: Boolean,
        default: false,
    },
    inline: {
        type: Boolean,
        default: false,
    },
  format: {
    type: String,
    default: "yyyy-MM-dd",
  }
});

const selectedDate = ref<string | null>(null);
const datePicker = ref<DatePickerInstance | null>(null);

const emit = defineEmits(["update:modelValue"]);

watch(
    () => props.modelValue,
    (value) => {
        if (!props.modelValue) {
            return null;
        }

        selectedDate.value = format(new Date(props.modelValue), "MM-dd-yyyy");
        emit("update:modelValue", value);
    },
    { immediate: true },
);

function isHourIncrementDisabled(hours: number) {
    return props.noMaxDate ? false : isHourMaximum(hours);
}

function isHourDecrementDisabled(hours: number) {
    return props.noMaxDate ? false : isHourMinimum(hours);
}

function isMinuteIncrementDisabled(hours: number, minutes: number) {
    return props.noMaxDate
        ? false
        : isHourMaximum(hours)
          ? minutes >= (props.maxDate?.getMinutes() ?? 59)
          : false;
}

function isMinuteDecrementDisabled(hours: number, minutes: number) {
    return props.noMaxDate
        ? false
        : isHourMinimum(hours)
          ? minutes <= (props.minDate?.getMinutes() ?? 0)
          : false;
}

function isHourMaximum(hours: number) {
    return (
        format(new Date(props.maxDate), "MM-dd-yyyy") === selectedDate.value &&
        hours >= (props.maxDate?.getHours() ?? 23)
    );
}

function isHourMinimum(hours: number) {
    return (
        format(new Date(props.minDate), "MM-dd-yyyy") === selectedDate.value &&
        hours <= (props.minDate?.getHours() ?? 0)
    );
}

function handleInputHour(
    setHours: (hours: number) => void,
    setMinutes: (minutes: number) => void,
    hour?: number,
) {
    let target = document.getElementById("hourInput") as HTMLInputElement;

    let newHour = hour ?? Number(target.value);
    if (isHourMaximum(newHour)) {
        console.log("Hour is at maximum value");
        newHour = props.maxDate?.getHours();
    } else if (newHour > 23) {
        console.log("Hour is greater than 23");
        newHour = 23;
    } else if (isHourDecrementDisabled(newHour)) {
        console.log("Hour decrement is disabled");
        newHour = props.minDate?.getHours();
    } else if (newHour < 0) {
        console.log("Hour is less than 0");
        newHour = 0;
    }
    setHours(newHour);

    handleInputMinute(setMinutes, newHour);
}

function handleInputMinute(
    setMinutes: (minutes: number) => void,
    hours: number,
) {
    let target = document.getElementById("minuteInput") as HTMLInputElement;
    let newMinute = Number(target.value);
    if (newMinute > 59) {
        console.log("Minute exceeded 59. Setting minute to 59");
        newMinute = 59;
    } else if (isMinuteIncrementDisabled(hours, newMinute)) {
        console.log(
            "Minute increment disabled. Setting minute to max date minutes",
        );
        newMinute = props.maxDate?.getMinutes();
    } else if (isMinuteDecrementDisabled(hours, newMinute)) {
        console.log(
            "Minute decrement disabled. Setting minute to min date minutes",
        );
        newMinute = props.minDate?.getMinutes();
    } else if (newMinute < 0) {
        console.log("Negative minute. Setting minute to 0");
        newMinute = 0;
    }
    target.value = `${newMinute}`;
    setMinutes(newMinute);
}
</script>

<template>
    <div class="flex flex-col items-start text-sm font-[600] text-gray-700">
        <label
            v-if="props.label"
            :for="id"
            class="mb-1.5 text-sm font-[600] text-gray-700"
            @click="datePicker?.openMenu()"
        >
            {{ props.label }}
            <span v-if="props.required" class="text-primary-500">*</span>
        </label>
        <vue-date-picker
            ref="datePicker"
            :id="props.id"
            :min-date="props.minDate"
            :max-date="props.maxDate"
            :enable-time-picker="false"
            :model-value="props.modelValue as Date"
            :placeholder="props.placeholder as string"
            input-class-name="!py-2 !rounded-lg focus:!ring-primary-500"
            calendar-cell-class-name="!rounded-full"
            class="custom-date-picker"
            :inline="inline"
            :clearable="false"
            @date-update="selectedDate = format($event, 'MM-dd-yyyy')"
            @update:model-value="emit('update:modelValue', format($event, props.format))"
        >
            <template
                #time-picker-overlay="{ hours, minutes, setHours, setMinutes }"
            >
                <div class="py-5 px-8 space-y-5">
                    <div class="flex flex-col items-center">
                        <p class="font-medium mb-2 text-center">
                            {{ selectedDate ?? "MM-DD-YYYY" }}
                        </p>
                        <div class="flex items-center space-x-3 w-3/4">
                            <div class="flex flex-col items-center space-y-1">
                                <general-icon-button
                                    :disabled="
                                        isHourIncrementDisabled(hours) ||
                                        hours === 23
                                    "
                                    class="w-6 h-6 !rounded-full border-none"
                                    @on-click="
                                        handleInputHour(
                                            setHours,
                                            setMinutes,
                                            hours + 1,
                                        )
                                    "
                                >
                                    <template #icon>
                                        <icon-chevron-up size="20" />
                                    </template>
                                </general-icon-button>
                                <input
                                    id="hourInput"
                                    :value="hours < 10 ? `0${hours}` : hours"
                                    type="number"
                                    class="text-center w-full p-1 text-2xl border-none rounded-lg transition focus:bg-gray-100 focus:border-none focus:ring-0"
                                    @input="
                                        handleInputHour(setHours, setMinutes)
                                    "
                                />
                                <general-icon-button
                                    :disabled="isHourDecrementDisabled(hours)"
                                    class="w-6 h-6 !rounded-full border-none"
                                    @on-click="
                                        handleInputHour(
                                            setHours,
                                            setMinutes,
                                            hours - 1,
                                        )
                                    "
                                >
                                    <template #icon>
                                        <icon-chevron-up
                                            size="20"
                                            class="rotate-180"
                                        />
                                    </template>
                                </general-icon-button>
                            </div>
                            <span class="text-xl">:</span>
                            <div class="flex flex-col items-center space-y-1">
                                <general-icon-button
                                    :disabled="
                                        isMinuteIncrementDisabled(
                                            hours,
                                            minutes,
                                        ) || minutes === 59
                                    "
                                    class="w-6 h-6 !rounded-full border-none"
                                    @on-click="setMinutes(minutes + 1)"
                                >
                                    <template #icon>
                                        <icon-chevron-up size="20" />
                                    </template>
                                </general-icon-button>
                                <input
                                    id="minuteInput"
                                    :value="
                                        minutes < 10 ? `0${minutes}` : minutes
                                    "
                                    type="number"
                                    class="text-center w-full p-1 text-2xl border-none rounded-lg transition focus:bg-gray-100 focus:border-none focus:ring-0"
                                    @input="
                                        handleInputMinute(setMinutes, hours)
                                    "
                                />
                                <general-icon-button
                                    :disabled="
                                        isMinuteDecrementDisabled(
                                            hours,
                                            minutes,
                                        ) || minutes === 0
                                    "
                                    class="w-6 h-6 !rounded-full border-none"
                                    @on-click="setMinutes(minutes - 1)"
                                >
                                    <template #icon>
                                        <icon-chevron-up
                                            size="20"
                                            class="rotate-180"
                                        />
                                    </template>
                                </general-icon-button>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
            <template #action-row="{ modelValue, selectDate }">
                <div class="!w-full flex space-x-3">
                    <general-outlined-button
                        label="Cancel"
                        class="w-full"
                         @on-click="datePicker?.closeMenu()"
                
                    />
                    <general-button
                        label="Confirm"
                        class="w-full"
                        @on-click="selectDate"
                    />
                </div>
            </template>
        </vue-date-picker>
    </div>
</template>

<style scoped>
.custom-date-picker :deep(input) {
  font-weight: 400;
}

.custom-date-picker :deep(.dp__month_year_row) {
  font-weight: 400;
}

.custom-date-picker :deep(.dp__calendar_header) {
  font-weight: 500 !important;
}

.custom-date-picker :deep(.dp__calendar) {
  font-weight: 400 !important;
}

.custom-date-picker :deep(.dp__theme_light) {
  --dp-text-color: #344054;
  --dp-primary-color: #EF3434;
  --dp-range-between-dates-background-color: #FFF5F5;
  --dp-range-between-dates-text-color: #D72F2F;
}
</style>
