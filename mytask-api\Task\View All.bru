meta {
  name: View All
  type: http
  seq: 3
}

get {
  url: {{base_url}}/task/view-all?perpage=10000
  body: multipartForm
  auth: bearer
}

query {
  perpage: 10000
  ~query: DONE
  ~from: 2021-08-19
  ~to: 2021-09-20
  ~field: name
  ~sort: asc
  ~status: 
}

auth:bearer {
  token: {{token}}
}

tests {
  // if(pm.response.code == 200) {
  //     pm.test("Status code is 200");
  // } else if(pm.response.code == 401) {
  //     pm.test("Status code is 401");
  // }
}
