meta {
  name: Export Excel Employee
  type: http
  seq: 4
}

get {
  url: {{base_url}}/employee/export-excel
  body: none
  auth: bearer
}

auth:bearer {
  token: {{token}}
}

script:pre-request {
  // 
}

tests {
  // if(pm.response.code == 200) {
  //     pm.test("Status code is 200");
  // } else if(pm.response.code == 401) {
  //     pm.test("Status code is 401");
  // }
  // 
  // var getParams = pm.request.url.query.all();
  // if(getParams) {
  //     getParams.forEach(function(getParam){
  //         pm.test("Param value = " + getParam.value);
  //     });   
  // }
  //  
}
