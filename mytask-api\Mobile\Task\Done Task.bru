meta {
  name: Done Task
  type: http
  seq: 3
}

post {
  url: {{mobile_url}}/task/done-task
  body: multipartForm
  auth: bearer
}

headers {
  X-Mobile-App: Android
  X-Mobile-App-Version: 4.4
}

auth:bearer {
  token: {{mobileToken}}
}

body:multipart-form {
  task_id: 1
  task_sign: undefined
  foto_task[]: undefined
  note_task: Done
  foto_task[]: undefined
  foto_task[]: undefined
}

script:pre-request {
  // 
}

tests {
  // if(pm.response.to.have.status(200)) {
  //         pm.test("Status code is 200", function () {
  //         pm.response.to.have.status(200);
  //     });
  // }
}
