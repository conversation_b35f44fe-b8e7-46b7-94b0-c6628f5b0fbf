<script lang="ts" setup>
import {definePageMeta} from "#imports";
import api from '~/services/api'

const $route = useRoute()

definePageMeta({
  auth: {
    unauthenticatedOnly: false,
  },
  layout: false,
  layoutTransition: {
    mode: 'out-in'
  }
})

const taskId = ref<string | undefined>()

onMounted(async () => {
  if ($route.query.d) {
    const secretKey = useRuntimeConfig().public.secretKey
    const token = decodeURIComponent($route.query.d as string)
    const payload = await decryptData(token as string, secretKey)

    taskId.value = payload.taskId as string
    api.tempAccessToken = payload.accessToken as string
  }
})

async function decryptData(encryptedData: string, secretKey: string) {
  const base64Decode = (str: string) => Uint8Array.from(atob(str), c => c.charCodeAt(0));

  const decodedIV = base64Decode(secretKey);
  const decodedEncryptedData = base64Decode(encryptedData);

  const alg = {
    name: 'AES-GCM',
    iv: decodedIV
  };

  const key = await crypto.subtle.importKey(
      'raw',
      new TextEncoder().encode(secretKey),
      alg,
      false,
      ['decrypt']
  );

  try {
    const decryptedData = await crypto.subtle.decrypt(
        alg,
        key,
        decodedEncryptedData
    );

    const dec = new TextDecoder();
    const decryptedPayload = dec.decode(decryptedData);

    return JSON.parse(decryptedPayload);
  } catch (error) {
    console.error('Decryption failed', error);
    throw new Error('Decryption failed');
  }
}
</script>

<template>
  <div class="p-8">
    <detail-task v-if="taskId" :id="taskId" :is-public="true"/>
    <div v-else>
      No Permissions!!
    </div>
  </div>
</template>

<style scoped>

</style>
