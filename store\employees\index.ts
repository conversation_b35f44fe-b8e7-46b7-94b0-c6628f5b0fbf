import {defineStore} from "pinia";
import api from "~/services/api";
import {downloadBlob} from "~/utils/functions";
import type {Employee, Meta, Task} from "~/types/server-response";

export const useEmployeesStore = defineStore('employees', {
    state: () => ({
        isLoading: {
            list: false,
            form: false,
            exportExcel: false,
            exportPdf: false,
            sync: false,
            detail: false,
            search: false,
        },
        listEmployee: [] as Employee[],
        detailTask: null as null | Task,
        meta: undefined as Meta | undefined
    }),
    actions: {
        async fetchListEmployee({
                                    query = '',
                                    field = 'name',
                                    sort = 'desc',
                                    from = '',
                                    to = '',
            page = 1, perpage = '15'
                                }) {
            this.isLoading.list = true

            const result = await api.get('/employee/get-employee', {
                queryParams: {
                    query,
                    field,
                    sort,
                    from,
                    to,
                    page: `${page}`,
                    perpage,
                }
            })

            if (result.status) {
                this.listEmployee = result.data
                this.meta = result.meta
            }

            this.isLoading.list = false
        },

        async fetchExportExcel() {
            this.isLoading.exportExcel = true

            const result = await api.get('/employee/export-excel')

            if (result.status) {
                window.open(result.url)
            }

            this.isLoading.exportExcel = false
        },

        async fetchExportPDF() {
            this.isLoading.exportPdf = true

            const result = await api.get('/employee/export-pdf')

            if (result.status) {
                window.open(result.url)
            }

            this.isLoading.exportPdf = false
        },

        async fetchSyncEmployee() {
            this.isLoading.sync = true

            const result = await api.get('/employee/sync-employee')

            if (result.status) {
                await this.fetchListEmployee({
                    query: '',
                    field: 'name',
                    sort: 'desc',
                    from: '',
                    to: ''
                })
            }

            this.isLoading.sync = false
        },
    }
})
