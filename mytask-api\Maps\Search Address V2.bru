meta {
  name: Search Address V2
  type: http
  seq: 2
}

get {
  url: {{base_url}}/v2/maps/search-place?text=transtrack
  body: multipartForm
  auth: bearer
}

query {
  text: transtrack
}

auth:bearer {
  token: ZjJFZzFHZW1nMnkyaEpOTXRJN3NIbUJtTEd0bHlSV3VhbXVUYXZ6SU9ETXpEWFZsZ2FJSWo1WDRpR25r646f012d7c643
}

tests {
  // if(pm.response.code == 200) {
  //     pm.test("Status code is 200");  
  // } else if(pm.response.code == 401) {
  //     pm.test("Status code is 401");
  // }
}
