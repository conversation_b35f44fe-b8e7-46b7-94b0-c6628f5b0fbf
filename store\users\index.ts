import { defineStore } from "pinia";
import { toast } from "vue3-toastify";
import type { ValidationError } from "yup";
import api from "~/services/api";
import type { User } from "~/types/server-response";

export const useUsersStore = defineStore("users", {
  state: () => ({
    isLoading: {
      list: false,
      form: false,
      detail: false,
      search: false,
      inputTypes: false,
    },
    users: [] as User[],
    detail: null as null | User,
    loading: false,
    filter_columns: '',
    filter_value: '',
    total: 0,
    page: 1,
    page_size: 10,
  }),

  actions: {
    async fetchUsers(
      params: {
        page?: number;
        page_size?: number;
        search_value?: string; // diubah dari search
        search_columns?: string; // ditambahkan untuk kolom pencarian
        filter_columns?: string;
        filter_value?: string;
        search?: string;
        from?: string;
        to?: string;
        company_id?: string;
        role_id?: string;
        industry_id?: string;
      } = {},
      useGo = true
    ) {
      this.loading = true;
      try {
        const { data } = await api.get("/api/v1/users", {
          queryParams: {
            page: params.page?.toString() || "",
            page_size: params.page_size?.toString() || "",
            search_columns: params.search_columns || 'name', // Default search di kolom 'name'
            search_value: params.search_value || '',
            from: params.from || "",
            to: params.to || "",
            filter_columns: params.filter_columns || (params.company_id ? 'company_id' : (params.role_id ? 'role_id' : '')),
            filter_value: params.filter_value || params.company_id || params.role_id || "",
            company_id: params.company_id || "",
            role_id: params.role_id || "",
            industry_id: params.industry_id || "",
          },
          useGo,
        });
        this.users = data.list;
        this.page = data.pagination;
      } catch (error: any) {
        toast.error(error.message || "Failed to fetch users");
      } finally {
        this.loading = false;
      }
    },

    async createUser(userData: any, useGo = true) {
      this.isLoading.form = true;
      try {
        const response = await api.post("/api/v1/users", {
          body: userData,
          useGo,
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (response.status) {
          toast.success("User berhasil dibuat");
          return response;
        }
        return null;
      } catch (error: any) {
        console.error("Error creating user:", error);
        toast.error(error.message || "Gagal membuat user");
        throw error;
      } finally {
        this.isLoading.form = false;
      }
    },
    async fetchDetail(id: string) {
      this.isLoading.detail = true;
      try {
        const result = await api.get(`/api/v1/users/${id}`, {
          useGo: true,
        });

        if (result.success) {
          this.detail = result.data;
        }
      } catch (error) {
        console.error("Error fetching user detail:", error);
        console.log(id, "id");
        toast.error("Gagal mengambil detail user");
      } finally {
        this.isLoading.detail = false;
      }
    },
    async updateUser(id: string, userData: any, useGo = true) {
      this.isLoading.form = true;
      try {
        const response = await api.put(`/api/v1/users/${id}`, {
          body: userData,
          useGo,
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (response.success) {
          toast.success("User berhasil diubah");
          return response;
        }
        return null;
      } catch (error: any) {
        console.error("Error updating user:", error);
        toast.error(error.message || "Gagal mengubah data user");
        throw error;
      } finally {
        this.isLoading.form = false;
      }
    },
    async deleteUser(id: string, password: string) {
      this.isLoading.form = true;
      try {
        // Encrypt the payload
        const keyHex = "7uCLJOFYs0gHTFJI5zgONR3niakarTxx";
        const jsonPayload = { password };
        const payloadString = JSON.stringify(jsonPayload);
        const encoder = new TextEncoder();
        const keyBytes = new Uint8Array(keyHex.split('').map(c => c.charCodeAt(0)));
        
        // Import the key
        const key = await crypto.subtle.importKey(
          'raw',
          keyBytes,
          { name: 'AES-GCM' },
          false,
          ['encrypt']
        );
        
        // Generate a random nonce
        const nonce = crypto.getRandomValues(new Uint8Array(12));
        
        // Encrypt the data
        const encryptedData = await crypto.subtle.encrypt(
          { name: 'AES-GCM', iv: nonce },
          key,
          encoder.encode(payloadString)
        );
        
        // Combine nonce and encrypted data
        const encryptedBytes = new Uint8Array(encryptedData);
        const combined = new Uint8Array(nonce.length + encryptedBytes.length);
        combined.set(nonce);
        combined.set(encryptedBytes, nonce.length);
        
        // Convert to base64
        const encryptedBase64 = btoa(String.fromCharCode(...combined));
    
        // Send the request with encrypted payload
        const result = await api.delete(`/api/v1/users/${id}`, {
          useGo: true,
          body: {
            payload: encryptedBase64
          }
        });
    
        if (result.success) {
          toast.success('User berhasil dihapus');
          return true;
        }
      } catch (e) {
        toast.error((e as ValidationError).message);
        console.error('Error deleting user:', e);
        return false;
      } finally {
        this.isLoading.form = false;
      }
    }
  },
});
