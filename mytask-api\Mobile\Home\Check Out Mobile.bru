meta {
  name: Check Out Mobile
  type: http
  seq: 3
}

post {
  url: {{mobile_url}}/home/<USER>
  body: multipartForm
  auth: bearer
}

headers {
  X-Mobile-App: Android
  X-Mobile-App-Version: 4.4
}

auth:bearer {
  token: {{mobileToken}}
}

body:multipart-form {
  lat: -8.086410
  long: 167.25456
  _method: PUT
}

script:pre-request {
  // 
}

tests {
  // if(pm.response.to.have.status(200)) {
  //         pm.test("Status code is 200", function () {
  //         pm.response.to.have.status(200);
  //     });
  // }
}
