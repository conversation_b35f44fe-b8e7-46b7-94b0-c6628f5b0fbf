import {defineStore} from "pinia";
import api from "~/services/api";
import type {Attendance, Meta} from "~/types/server-response";

export enum StatusAttendance {
    TODO = 'TO DO',
    DOING = 'DOING',
    DONE = 'DONE'
}

export const useAttendancesStore = defineStore('attendances', {
    state: () => ({
        isLoading: {
            list: false,
            form: false,
            exportExcel: false,
            exportPdf: false,
            detail: false,
            search: false,
        },
        listAttendance: [] as Attendance[],
        detailAttendance: null as null | Attendance,
        meta: undefined as Meta | undefined
    }),
    actions: {
        async fetchListAttendance({
            query = '',
            field = 'check_in_date',
            sort = 'desc',
            from = '',
            to = '',
            page = 1,
        }) {
            this.isLoading.list = true
    
            const queryParams: Record<string, string> = {
                query,
                field,
                sort,
                page: `${page}`,
                perpage: '15',
            }
    
            if (from) queryParams.from = from
            if (to) queryParams.to = to

            const result = await api.get('/employee/get-attendence', { queryParams })

            if (result.status) {
                this.listAttendance = result.data
                this.meta = result.meta
            }

            this.isLoading.list = false
        },



        async fetchExportExcel() {
            this.isLoading.exportExcel = true

            const result = await api.get('/employee/export-excel-attendence')

            if (result.status) {
                window.open(result.url)
            }

            this.isLoading.exportExcel = false
        },

        async fetchExportPDF() {
            this.isLoading.exportPdf = true

            const result = await api.get('/employee/export-pdf-attendence')

            if (result.status) {
                window.open(result.url)
            }

            this.isLoading.exportPdf = false
        },
    },
})
