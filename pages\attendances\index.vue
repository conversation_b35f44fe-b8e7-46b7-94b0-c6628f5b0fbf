<script setup lang="ts">
import { format, subYears } from 'date-fns'
import VueDatePicker from '@vuepic/vue-datepicker'
import type { DatePickerInstance } from '@vuepic/vue-datepicker'
import '@vuepic/vue-datepicker/dist/main.css'
import { usePageStore } from '~/store/page'
import { watchDebounced } from '@vueuse/shared'
import { useRouter } from 'vue-router'
import { useRoute } from '#app'
import { useAttendancesStore } from '~/store/attendances'
import { useEmployeesStore } from '~/store/employees'

const $page = usePageStore()
const $attendances = useAttendancesStore()
const $employees = useEmployeesStore()
const $router = useRouter()
const $route = useRoute()

const initialQuery = {
  query: '',
  field: 'check_in_date',
  sort: 'desc',
  status: '',
  labelFrom: '',
  labelTo: '',
  range: [],
  page: 1
}

const query = ref(initialQuery)

onMounted(() => {
  $page.setTitle('Daftar Absen')

  if (Object.keys($route.query).length > 0) {
    query.value = { ...$route.query } as any
  }

  // $router.push({ query: query.value })
  query.value = initialQuery
  fetchListAttendance()
})

watchDebounced(
  () => [query.value.query, query.value.range],
  () => {
    fetchListAttendance()
  },
  { debounce: 300 }
)

watch(
  () => query.value,
  (value) => {
    $router.push({ value })
  },
  { deep: true }
)

watch(() => query.value.page, () => {
  fetchListAttendance()
})

watch(
  () => query.value.range,
  (value) => {
    if (value[0]) {
      query.value.from = dateString(value[0])
      query.value.labelFrom = dateStringDMY(value[0])
    }

    if (value[1]) {
      query.value.to = dateString(value[1])
      query.value.labelTo = dateStringDMY(value[1])
    }
  },
  { deep: true }
)

watch(() => query.value.query, (newQuery) => {
  if (newQuery) {
    query.value.page = 1; 
  }
});

function dateString(dates) {
  // Y-M-D
  const dateString = dates
  const date = new Date(dateString)

  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')

  return `${year}-${month}-${day}`
}

function dateStringDMY(dates) {
  // D-M-Y
  const dateString = dates
  const date = new Date(dateString)

  const year = date.getFullYear()
  const month = date.toLocaleString('default', { month: 'short' }) // Get short month name
  const day = String(date.getDate()).padStart(2, '0')

  return `${day}/${month}/${year}`
}

function resetQuery() {
  query.value = { ...initialQuery }
}

function clearDate() {
  query.value.from = ''
  query.value.labelFrom = ''

  query.value.to = ''
  query.value.labelTo = ''

  fetchListAttendance()
}

function fetchListAttendance() {
  $attendances.fetchListAttendance({
    ...query.value,
  })
}

watch(() => query.value.labelFrom && query.value.labelTo, (newQuery) => {
  if (newQuery) {
    query.value.page = 1
  }
});


const getRowNumber = (index: number) => {
  const perPage = 15
  const currentPage = parseInt(query.value.page)
  const currentIndex = parseInt(index)
  const adjustedIndex = currentIndex % perPage
  
  return (perPage * (currentPage - 1)) + (adjustedIndex + 1)
}

</script>

<template>
  <div>
    <!--    <general-modal-->
    <!--        is-has-close-->
    <!--        id="modal-detail-task"-->
    <!--        @mounted="modalDetailLocation = $event"-->
    <!--        @modal-opened="dispatchResizeEvent()"-->
    <!--    >-->
    <!--      <template #icon>-->
    <!--        <icon-marker-pin class="stroke-primary-500"/>-->
    <!--      </template>-->
    <!--      <template #body>-->
    <!--        <detail-task-->
    <!--            :task="selectedTask"-->
    <!--            @on-click-update="isAddingLocation = false ;modalDetailLocation?.hide(); modalFormLocations?.show()"-->
    <!--            @on-click-delete="modalDetailLocation?.hide(); modalConfirmDelete?.show()"-->
    <!--        />-->
    <!--      </template>-->
    <!--    </general-modal>-->
    <div class="bg-white border rounded-lg shadow-sm overflow-hidden">
      <div class="flex justify-between items-center p-6">
        <div class="w-full flex space-x-3">
          <div>
            <general-text-input
              v-model="query.query"
              clearable
              id="inputSearchAttendance"
              placeholder="Search"
            >
              <template #prefix>
                <icon-search size="20" />
              </template>
            </general-text-input>
            <div
              v-show="query.labelFrom != '' && query.labelTo != ''"
              class="flex justify-center w-[250px] items-center mt-2 m-0 text-sm text-gray-700 border rounded-lg p-3"
              style="white-space: nowrap"
            >
              <div v-show="query.labelFrom != '' && query.labelTo != ''">
                {{ query.labelFrom }} - {{ query.labelTo }}
                <span @click="clearDate" style="cursor: pointer">
                  <icon-close
                  width="20"
                  height="20"  
                  style="
                      aspect-ratio: 210 / 210;
                      object-fit: cover;
                      display: inline-block;
                    " 
                  class="stroke-gray-700 ml-2" />
                </span>
              </div>

              <div v-show="query.labelFrom && query.labelTo == ''">
                {{ query.labelFrom }}
              </div>

              <div v-show="query.labelFrom == '' && query.labelTo">
                {{ query.labelTo }}
              </div>
            </div>
          </div>
          <general-dropdown
            id-activator="btnDropdownCalendar"
            id-dropdown="dropdownCalendar"
          >
            <template #activator>
              <general-icon-button
                id="btnDropdownCalendar"
                data-dropdown-toggle="dropdownCalendar"
                class="!h-11"
              >
                <template #icon>
                  <icon-calendar />
                </template>
              </general-icon-button>
            </template>

            <template #content>
              <general-range-datepicker
                v-model="query.range"
                inline
                :enable-time-picker="false"
              />
            </template>
          </general-dropdown>
        </div>
        <div class="flex space-x-3 w-full items-center justify-end">
          <app-export-button
            @on-click-export-excel="$attendances.fetchExportExcel()"
            @on-click-export-pdf="$attendances.fetchExportPDF()"
          />
          <general-button
            id="btnSyncAttendances"
            label="Sinkronisasi Data"
            :loading="$employees.isLoading.sync"
            @on-click="$employees.fetchSyncEmployee().then(() => fetchListAttendance())"
          >
            <template #prefix>
              <icon-sync size="20" />
            </template>
          </general-button>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="w-full text-sm text-left" style="empty-cells: show">
          <thead class="text-xs text-gray-700 bg-gray-50 border-b">
            <tr>
              <th scope="col" class="px-6 py-3 font-medium">No</th>
              <th scope="col" class="px-6 py-3 font-medium">Nama Karyawan</th>
              <th scope="col" class="px-6 py-3 font-medium">Lokasi Check In</th>
              <th scope="col" class="px-6 py-3 font-medium">
                Tanggal Check In
              </th>
              <th scope="col" class="px-6 py-3 font-medium">Waktu Check In</th>
              <th scope="col" class="px-6 py-3 font-medium">
                Lokasi Check Out
              </th>
              <th scope="col" class="px-6 py-3 font-medium">
                Tanggal Check Out
              </th>
              <th scope="col" class="px-6 py-3 font-medium">Waktu Check Out</th>
              <th scope="col" class="px-6 py-3 font-medium" />
            </tr>
          </thead>
          <tbody>
            <!--          <tr v-if="$tasks.$state.listTask.length === 0" class="flex items-center justify-center">-->
            <!--            <td></td>-->
            <!--            <td></td>-->
            <!--            <td></td>-->
            <!--            <td class="h-24 flex items-center justify-center">-->
            <!--              <p class="text-gray-500">Tidak ada data</p>-->
            <!--            </td>-->
            <!--            <td></td>-->
            <!--          </tr>-->
            <tr v-if="$attendances.$state.isLoading.list">
              <td></td>
              <td></td>
              <td></td>
              <td class="h-24 flex items-center justify-center">
                <icon-circle-loading />
              </td>
              <td></td>
            </tr>
            <tr
              v-else
              v-for="(attendance, i) in $attendances.listAttendance"
              :key="attendance.id"
              class="bg-white border-b"
            >
            <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
              {{ getRowNumber(i) }}
              </td>
              <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
                {{ attendance.name }}
              </td>
              <td
                class="px-6 py-4 font-normal text-gray-900 max-w-xs truncate overflow-hidden whitespace-wrap text-wrap"
              >
                <span v-if="attendance.lokasi_check_in">{{ attendance.lokasi_check_in }}</span>
              </td>
              <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
                <span v-if="attendance.check_in_date">{{ formatDate(attendance.check_in_date) }}</span>
              </td>
              <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
                <span v-if="attendance.check_in_time">{{ attendance.check_in_time }}</span>
              </td>
              <td
                class="px-6 py-4 font-normal text-gray-900 max-w-xs truncate overflow-hidden whitespace-wrap text-wrap"
              >
                <span v-if="attendance.lokasi_check_out">{{ attendance.lokasi_check_out }}</span>
              </td>
              <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
                <span v-if="attendance.check_out_date">{{ formatDate(attendance.check_out_date) }}</span>
              </td>
              <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
                <span v-if="attendance.check_out_time">{{ attendance.check_out_time }}</span>
              </td>
              <td
                class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap"
              ></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <general-pagination
        v-model="query.page"
        :active-page="$attendances.meta?.page"
        :total-page="Math.ceil($attendances.meta?.total/$attendances.meta?.perpage)"
    />
  </div>
</template>

<style scoped>
.nuxt-img:hover {
  opacity: 0.5;
}
</style>
