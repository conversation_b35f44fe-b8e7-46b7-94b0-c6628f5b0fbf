<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default: '24'
  }
})
</script>

<template>
  <svg xmlns="http://www.w3.org/2000/svg" :width="props.size" :height="props.size" viewBox="0 0 20 21" fill="none">
    <path
      d="M16.6667 12.1667V14.5C16.6667 15.4334 16.6667 15.9001 16.485 16.2567C16.3252 16.5703 16.0703 16.8252 15.7567 16.985C15.4001 17.1667 14.9334 17.1667 14 17.1667H11.6667M8.33333 3.83333H6C5.06658 3.83333 4.59987 3.83333 4.24335 4.01499C3.92975 4.17478 3.67478 4.42975 3.51499 4.74335C3.33333 5.09987 3.33333 5.56658 3.33333 6.5V8.83333M12.5 8L17.5 3M17.5 3H12.5M17.5 3V8M7.5 13L2.5 18M2.5 18H7.5M2.5 18L2.5 13"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>
</template>

<style scoped>

</style>