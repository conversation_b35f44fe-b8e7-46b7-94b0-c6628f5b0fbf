<script setup lang="ts">

const emit = defineEmits(['mounted', 'close'])
const props = defineProps({
  title: {
    type: String,
    default: 'Pemindahan Berhasil'
  },
  subtitle: {
    type: String,
    default: 'Pengguna berhasil dipindahkan ke role Role yang dituju'
  },
  buttonLabel: {
    type: String,
    default: 'Lanjutkan'
  }
})

</script>

<template>
  <general-modal id="modal-success" title="" @mounted="emit('mounted', $event)">
    <template #body>
      <div class="flex flex-col gap-4 items-center py-4">
        <!-- Success Icon -->
        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
          <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
        </div>

        <!-- Content -->
        <div class="space-y-2 mb-4 text-center">
          <h3 class="text-lg text-gray-900 font-semibold">{{ title }}</h3>
          <p class="text-sm text-gray-500 font-normal">{{ subtitle }}</p>
        </div>

        <!-- Action Button -->
        <div class="w-full">
          <general-button 
            :label="buttonLabel" 
            class="w-full bg-red-500 hover:bg-red-600" 
            @on-click="emit('close')"
          />
        </div>
      </div>
    </template>
  </general-modal>
</template>

<style scoped>

</style>