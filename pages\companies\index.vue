<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {usePageStore} from "~/store/page";
import { format, subDays, isAfter } from 'date-fns'
import { useCompaniesStore } from '~/store/companies'
import type { Company } from '~/types/company';
import { useRouter } from 'vue-router'
import type { ElementEvent } from '~/types/element';
import ModalConfirmation from '~/components/general/ModalConfirmation.vue';

const $companiesStore = useCompaniesStore()

const query = ref({
  search: '',
  page: 1,
  page_size: 10,
  range: [] as Date[],
  labelFrom: '',
  labelTo: '',
  filter_columns: '',
  filter_value: '',
  industry_id: '',
  from: '',
  to: ''
})

function formatDate(date: string) {
  return format(new Date(date), 'yyyy-MM-dd HH:mm')
}

const $page = usePageStore()
const $router = useRouter()

onMounted(async () => {
  $page.setTitle('Daftar Perusahaan')
})

const industries = computed(() => $companiesStore.industries.map(industry => ({
  text: industry.name,
  value: industry.id.toString()
})))

const pagedCompanies = computed(() => $companiesStore.companies)
const total = computed(() => $companiesStore.total)

onMounted(async () => {
  $companiesStore.getIndustries()
  $companiesStore.fetchCompanies({
    page: query.value.page,
    page_size: query.value.page_size,
    search: query.value.search
  })
})

const isCompanyNew = (createdAt: string) => {
  const oneDayAgo = subDays(new Date(), 1)
  const companyDate = new Date(createdAt)
  return isAfter(companyDate, oneDayAgo)
}

function applyFilter() {
  query.value.page = 1
  $companiesStore.fetchCompanies({
    page: query.value.page,
    page_size: query.value.page_size,
    search: query.value.search,
    from: query.value.from,
    to: query.value.to,
    filter_columns: 'industry_id',
    filter_value: query.value.industry_id
  })
}

function clearDate() {
  query.value.range = []
  query.value.from = ''
  query.value.to = ''
  query.value.labelFrom = ''
  query.value.labelTo = ''
}

const options = ref([
  { text: '10', value: '10' },
  { text: '20', value: '20' },
  { text: '30', value: '30' },
  { text: '40', value: '40' },
  { text: '50', value: '50' }
])

const selected = ref('10')



function resetFilter() {
  query.value = { ...query.value }
  query.value.industry_id = ''
  clearDate()
  $companiesStore.fetchCompanies({
    page: query.value.page,
    page_size: query.value.page_size,
    search: query.value.search
  })
}

function dateString(dates: Date) {
  const date = new Date(dates)
  return format(date, 'yyyy-MM-dd')
}

function dateStringDMY(dates: Date) {
  const date = new Date(dates)
  return format(date, 'dd/MM/yyyy')
}

function fetchCompanies() {
  $companiesStore.fetchCompanies({...query.value})
}

watch(() => selected.value, (newValue) => {
  query.value.page_size = Number(newValue)
  query.value.page = 1
  fetchCompanies()
})

watch(
  () => query.value.range,
  (value) => {
    if (value[0]) {
      const startDate = new Date(value[0])
      startDate.setHours(0, 0, 0, 0)
      query.value.from = format(startDate, 'yyyy-MM-dd HH:mm:ss')
      query.value.labelFrom = dateStringDMY(value[0])
    }

    if (value[1]) {
      const endDate = new Date(value[1])
      endDate.setHours(23, 59, 59, 999)
      query.value.to = format(endDate, 'yyyy-MM-dd HH:mm:ss')
      query.value.labelTo = dateStringDMY(value[1])
    }
  },
  { deep: true }
)

function goDetail(id: number) {
  $router.push(`/companies/${id}#detail`)
}

function goEdit(id: number) {
  $router.push(`/companies/${id}#edit`)
}

// Update query when store state changes
watch(query, (newQuery) => {
  $companiesStore.fetchCompanies({
    page: newQuery.page,
    page_size: newQuery.page_size,
    search: newQuery.search
  })
}, { deep: true })

const activeAction = ref<Company | null>(null)
let modalDeleteConfirm: ElementEvent | null = null

async function handleDeleteCompany(company: Company) {
  activeAction.value = company
  modalDeleteConfirm?.show()
}

async function confirmDelete() {
  if (!activeAction.value) return
  
  const success = await $companiesStore.deleteCompany(activeAction.value.id)
  if (success) {
    await fetchCompanies()
  }
  modalDeleteConfirm?.hide()
}

</script>

<template>
  <div class="bg-white rounded-xl space-y-4">
    <!-- Title and Subtitle -->
    <div class="p-6">
      <h2 class="font-bold text-lg md:text-xl">Perusahaan</h2>
      <p class="text-xs text-gray-500 mt-1">Lihat dan kelola perusahaan klien yang telah memiliki akses ke sistem Anda.</p>
    </div>

    <!-- Filter/Search/Date Bar -->
    <div class="p-6">
      <div class="w-full flex">
        <general-text-input v-model="query.search" clearable id="inputSearchCompany" placeholder="Search">
          <template #prefix>
            <icon-search size="20"/>
          </template>
        </general-text-input>
        <general-dropdown
          id-activator="btnDropdownFilter"
          id-dropdown="dropdownFilter"
        >
          <template #activator>
            <general-outlined-button
              label="Filters"
              id="btnDropdownFilter"
              data-dropdown-toggle="dropdownFilter"
              class="!h-11 ml-3"
            >
              <template #prefix>
                <icon-filter-lines size="20" />
              </template>
            </general-outlined-button>
          </template>

          <template #content>
            <label
              for="filter"
              class="mb-1.5 text-lg font-[600] text-gray-700 p-4"
            >
              Filter
            </label>
            <form>
              <div class="grid gap-6 md:grid-cols-2 p-4">
                <div>
                  <general-range-datepicker
                    v-model="query.range"
                    id="inputFilterDateRange"
                    label="Date Range"
                    placeholder="Select Date Range"
                    :enable-time-picker="false"
                    class="flex-1"
                  />
                </div>
              </div>
              <div class="grid gap-6 md:grid-cols-1 p-4">
                <div>
                  <general-combobox
                    id="selectFilterIndustry"
                    v-model="query.industry_id"
                    has-search-field
                    id-trigger="triggerDropdownIndustry"
                    id-target="dropdownIndustry"
                    label="Industri"
                    :items="industries"
                    plain
                    placeholder="Pilih Industri"
                  />
                </div>
                <div class="flex gap-4">
                  <general-button
                    id="btnFilterApply"
                    label="Terapkan Filter"
                    class="p-2"
                    @click="applyFilter"
                  >
                  </general-button>
                  <general-outlined-button
                    id="btnFilterReset"
                    label="Reset"
                    class="p-2"
                    @click="resetFilter"
                  >
                  </general-outlined-button>
                </div>
              </div>
            </form>
          </template>
        </general-dropdown>
        <div class="flex space-x-3 w-full items-center justify-end">
            <general-dropdown id-activator="btnExport" id-dropdown="dropdownExport">
              <template #activator>
                <general-outlined-button
                  id="btnExport"
                  data-dropdown-toggle="dropdownExport"
                  label="Export"
                  class="border-primary-500 border-2 text-primary-500"
                >
                  <template #prefix>
                    <icon-download-cloud size="20" class="stroke-primary-500" />
                  </template>
                </general-outlined-button>
              </template>
  
              <template #content>
                <div 
                  class="py-2.5 px-4 flex items-center cursor-pointer hover:bg-gray-100"
                  @click="handleExport('excel')"
                >
                  <p class="text-sm">Export Excel</p>
                </div>
                <div 
                  class="py-2.5 px-4 flex items-center cursor-pointer hover:bg-gray-100"
                  @click="handleExport('pdf')"
                >
                  <p class="text-sm">Export PDF</p>
                </div>
              </template>
            </general-dropdown>

            <general-button
              label="Tambah"
              @on-click="navigateTo('/companies/create')"
            >
            <template #prefix>
              <icon-plus size="20"/>
            </template>
          </general-button>
          </div>
      </div>
      <div class="w-full">
        <general-dropdown
        id-activator="btnDropdownCalendar"
        id-dropdown="dropdownCalendar"
      > 
        <template #activator>
          <div
            v-show="query.labelFrom != '' && query.labelTo != ''"
            class="flex justify-center w-[250px] items-center mt-5 m-0 text-sm text-gray-700 border rounded-lg p-3"
            style="white-space: nowrap"
          >
            <div v-show="query.labelFrom != '' && query.labelTo != ''">
              {{ query.labelFrom }} - {{ query.labelTo }}
              <span @click="clearDate" style="cursor: pointer">
              <icon-close
              width="20"
              height="20"  
              style="
                  aspect-ratio: 210 / 210;
                  object-fit: cover;
                  display: inline-block;
                " 
              class="stroke-gray-700 ml-2" />
            </span>
            </div>
          </div>
        </template>

        <template #content>
          <general-range-datepicker
            v-model="query.range"
            inline
            :enable-time-picker="false"
          />
        </template>
      </general-dropdown>
      </div>
      </div>

    <!-- Companies List -->
    <div class="rounded-xl overflow-hidden border border-gray-200">
      <table 
          class="w-full text-sm text-left" 
          style="empty-cells: show;" 
          aria-describedby="Tabel Daftar Perusahaan"
        >
        <thead class="text-xs text-gray-700 bg-gray-50 border-b">
          <tr>
            <th class="px-6 py-4 text-left text-xs font-bold text-gray-500">No</th>
            <th class="px-6 py-4 text-left text-xs font-bold text-gray-500">Perusahaan</th>
            <th class="px-6 py-4 text-left text-xs font-bold text-gray-500">Dibuat Pada</th>
            <th class="px-6 py-4 text-right text-xs font-bold text-gray-500"></th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(company, index) in pagedCompanies" :key="company.id">
            <td class="px-6 py-4 text-sm text-gray-900 align-middle">{{ ((query.page - 1) * query.page_size) + index + 1 }}</td>
            <td class="px-6 py-4">
              <div class="flex items-center gap-3">
                <div class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center overflow-hidden text-xs font-bold text-gray-600">
                  <img v-if="company.avatar" :src="company.avatar" :alt="company.name" class="w-full h-full object-cover" />
                  <span v-else>{{ company.name.charAt(0) }}</span>
                </div>
                <div>
                  <div class="flex items-center gap-2">
                    <span class="text-sm font-semibold text-gray-900">{{ company.name }}</span>
                    <span v-if="isCompanyNew(company.created_at)" class="ml-1 px-2 py-0.5 rounded bg-red-50 text-[10px] text-red-500 font-bold border border-red-200">Baru</span>
                  </div>
                  <div class="text-xs text-gray-400 mt-0.5">{{ company.industry?.name || '-' }}</div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 text-sm text-gray-500 align-middle">{{ formatDate(company.created_at) }}</td>
            <td class="px-1 py-4 font-normal text-gray-900 whitespace-nowrap">
              <general-dropdown :id-dropdown="`dropdownAction-${index}`" id-activator="btnDropdownAction" placement="right">
                <template #activator>
                  <general-icon-button
                      id="btnDropdownAction"
                      :data-dropdown-toggle="`dropdownAction-${index}`"
                      @click="'#'"
                  >
                    <template #icon> 
                      <icon-three-dot/>
                    </template>
                  </general-icon-button>
                </template>

                <template #content>
                  <div class="py-2.5 px-4 flex items-center cursor-pointer hover:bg-gray-100 group">
                    <p class="text-sm group-hover:text-red-500" @click="goEdit(company.id)">Edit</p>
                  </div>
                  <div class="py-2.5 px-4 flex items-center cursor-pointer hover:bg-gray-100 group">
                    <p class="text-sm group-hover:text-red-500" @click="goDetail(company.id)">Detail</p>
                  </div>
                  <div class="py-2.5 px-4 flex items-center cursor-pointer hover:bg-gray-100 group">
                    <p class="text-sm group-hover:text-red-500" @click="handleDeleteCompany(company)">Hapus</p>
                  </div>
                </template>
              </general-dropdown>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Delete Confirmation Modal -->
    <ModalConfirmation
      id="modal-confirm-delete"
      :is-loading="$companiesStore.isLoading.form"
      confirm-label="Konfirmasi"
      :subtitle="`Apakah Anda yakin ingin menghapus perusahaan ${activeAction?.name || ''} dari My Task?`"
      title="Hapus Perusahaan?"
      @mounted="modalDeleteConfirm = $event"
      @negative="modalDeleteConfirm?.hide()"
      @positive="confirmDelete()"
    />

    <!-- Pagination & Info -->
    <div class="flex justify-between mt-2 items-center">
        <div class="flex">
            <select v-model="selected" style="width: 80px;  border-radius: 8px">
            <option v-for="option in options" :value="option.value">
                {{ option.text }}
            </option>
            </select>
            <p class="flex items-center ml-2">{{ selected }} From <span class="ml-2">{{ $companiesStore.companies.length || 0 }} Data</span></p>
        </div>
        <div>
          <general-pagination
            v-model="query.page"
            :active-page="$companiesStore.page.page"
            :total-page="$companiesStore.page.total_pages"
            :is-prev-btn="false"
            />
        </div>
    </div>
  </div>
</template>
