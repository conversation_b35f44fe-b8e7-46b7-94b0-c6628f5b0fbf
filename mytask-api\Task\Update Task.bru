meta {
  name: Update Task
  type: http
  seq: 2
}

post {
  url: {{base_url}}/task/update-task/:taskId
  body: multipartForm
  auth: bearer
}

auth:bearer {
  token: {{token}}
}

body:multipart-form {
  lat_tujuan: -6.92633
  long_tujuan: 107.58744
  _method: PUT
  ~employee_id: 10
  ~title: Coba Task Bulan Mei 2
  ~no_referensi: 08LJGPT
  ~description: Task Dummy 8
  ~lat_asal: -6.92663985771417
  ~long_asal: 107.61825621128084
  ~deadline: 2022-05-28 10:00:00
}

tests {
  // if(pm.response.code == 200) {
  //     pm.test("Status code is 200");
  // } else if(pm.response.code == 401) {
  //     pm.test("Status code is 401");
  // }
  // 
  // var status = pm.response.json().data.status;
  // if(status != null) {
  //     pm.test("Status task is " + status);
  // }
}
