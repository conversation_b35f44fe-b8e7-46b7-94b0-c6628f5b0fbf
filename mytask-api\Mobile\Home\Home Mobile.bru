meta {
  name: Home Mobile
  type: http
  seq: 1
}

get {
  url: {{mobile_url}}/home/<USER>
  body: multipartForm
  auth: bearer
}

headers {
  X-Mobile-App: Android
  X-Mobile-App-Version: 7.0
}

auth:bearer {
  token: {{mobileToken}}
}

script:pre-request {
  // 
}

tests {
  // if(pm.response.to.have.status(200)) {
  //         pm.test("Status code is 200", function () {
  //         pm.response.to.have.status(200);
  //     });
  // }
}
