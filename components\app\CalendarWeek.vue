<script setup lang="ts">
import {addDays, isSameDay, startOfWeek} from "date-fns";

interface Day {
  date: Date
  isToday: boolean
}

const props = defineProps({
  modelValue: {
    type: Date,
    default: new Date()
  },
  events: {
    type: Array as () => any[],
    default: []
  }
})

const emit = defineEmits(['on-click-event'])

const buildCalendar = () => {
  const days = []

  const startDay = startOfWeek(props.modelValue, {weekStartsOn: 0})

  for (let i = 0; i < 7; i++) {
    const date = addDays(startDay, i)

    days.push({
      date,
      isToday: isSameDay(date, new Date())
    })
  }

  return days
}

const days = ref<Day[]>(buildCalendar())

watch(() => props.modelValue, () => {
  days.value = buildCalendar()
})

function onHover(id: string, status: 'ASSIGNED' | 'UNASSIGNED', type: 'OVER' | 'LEAVE') {
  const $itemsElement = document.querySelectorAll(`.schedule-item-${id}`)

  $itemsElement.forEach((item) => {
    type === 'OVER'
        ? item.classList.add(status === 'ASSIGNED' ? '!bg-success-200' : '!bg-primary-200')
        : item.classList.remove(status === 'ASSIGNED' ? '!bg-success-200' : '!bg-primary-200')
  })
}

const filteredEvents = (date: Date) => {
  return props.events?.filter(event => isSameDay(date, event.date))
}

const dateNumberClass = (date: Date) => {
  const baseClass = 'w-6 aspect-square text-sm flex items-center justify-center rounded-full cursor-default select-none'
  const background = isSameDay(date, props.modelValue)
      ? 'bg-primary-500 text-white'
      : 'text-gray-900'
  return `${baseClass} ${background}`
}

const eventClass = (status: 'ASSIGNED' | 'UNASSIGNED') => {
  const baseClass = 'p-2 w-full border rounded cursor-pointer select-none transition'
  const variant = status === 'ASSIGNED'
      ? 'border-success-500 bg-success-50 text-success-500'
      : 'border-primary-500 bg-primary-50 text-primary-500'
  return `${baseClass} ${variant}`
}
</script>

<template>
  <div class="grid grid-cols-7">
    <div
        v-for="day in ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']"
        :key="day"
        class="text-center text-xs py-1"
    >
      {{ day }}
    </div>

    <div
        v-for="day in days"
        class="p-1 min-h-screen space-y-1 flex flex-col items-center outline outline-1 outline-gray-200"
        :class="day.isToday ? 'bg-gray-50' : 'bg-white'"
    >
      <div :class="dateNumberClass(day.date)">{{ day.date.getDate() }}</div>

      <div
          v-for="event in filteredEvents(day.date)"
          :key="event.id"
          :class="`${eventClass(event.status)} schedule-item-${event.id}`"
          @mouseover="onHover(event.id, event.status, 'OVER')"
          @mouseleave="onHover(event.id, event.status, 'LEAVE')"
          @click="emit('on-click-event', event.id)"
      >
        <p class="text-xs font-semibold truncate">{{ event.title }}</p>

        <div v-if="event.status === 'ASSIGNED'">
          <hr class="my-1.5 border-success-500">

          <div class="space-y-0.5">
            <div class="flex items-start justify-between space-x-2">
              <p class="text-xs font-semibold flex-1">Vehicle</p>
              <div class="flex-1 overflow-x-hidden">
                <p v-for="assignment in event.assignments" class="text-xs truncate">
                  {{ assignment.vehicles.plat_number }}
                </p>
              </div>
            </div>
            <div class="flex items-start justify-between space-x-2">
              <p class="text-xs font-semibold flex-1">Driver</p>
              <div class="flex-1 overflow-x-hidden">
                <p v-for="assignment in event.assignments" class="text-xs truncate">
                  {{ assignment.driver.name }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
