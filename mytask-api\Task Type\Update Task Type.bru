meta {
  name: Update Task Type
  type: http
  seq: 3
}

post {
  url: {{base_url}}/type/update-type/1
  body: multipartForm
  auth: bearer
}

auth:bearer {
  token: {{token}}
}

body:multipart-form {
  type_identity: NTPO
  name: Natapo
  description: waw
  _method: PUT
}

script:pre-request {
  // 
}

tests {
  // if(pm.response.code == 200) {
  //     pm.test("Status code is 200");
  // } else if(pm.response.code == 401) {
  //     pm.test("Status code is 401");
  // }
  // 
  // var getParams = pm.request.url.query.all();
  // if(getParams) {
  //     getParams.forEach(function(getParam){
  //         pm.test("Param value = " + getParam.value);
  //     });   
  // }
  //  
}
