<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useRolesStore } from "~/store/roles";
import { toast } from "vue3-toastify";
import { usePermissionsStore } from "~/store/permissions";
import { useAuthStore } from "~/store/auth";
import { usePageStore } from "~/store/page";


const $page = usePageStore();

const props = defineProps<{
  id: string;
  isPublic?: boolean;
}>();

const $rolesStore = useRolesStore();
const $permissionsStore = usePermissionsStore();
const $auth = useAuthStore();

const router = useRouter();
const isEditMode = ref(false);
const isLoading = ref(false);

async function goToEdit() {
  isLoading.value = true;
  try {
    await new Promise((resolve) => setTimeout(resolve, 1000));
    isEditMode.value = true;
    window.location.hash = "edit";
  } finally {
    isLoading.value = false;
  }
}

onMounted(() => {
  if (window.location.hash === '#edit') {
    $page.setTitle("Edit Role");
  } else {
    $page.setTitle("Detail Role");
  }
})

function goBack() {
  navigateTo("/roles");
}

const role = ref({
  name: "",
  description: "",
});

const permissions = computed(() => $permissionsStore.permissions);

const featureSearch = ref("");

// Add this ref to track selected features
const selectedFeatures = ref<Set<string>>(new Set());

// Add computed property to get role permissions
const rolePermissions = computed(() => {
  return $rolesStore.detail?.permissions || [];
});

// Transform the permissions data for the table
const features = computed(() => {
  if (!permissions.value) return [];

  return permissions.value.map((group: any) => {
    const permissionsMap: Record<string, { id: string; checked: boolean }> = {};
    group.permissions.forEach((permission: any) => {
      const permissionType = permission.name.toLowerCase();
      // Check if this permission exists in role's permissions
      const hasPermission = rolePermissions.value.some(
        (rp) => rp.id === permission.id
      );

      permissionsMap[permissionType] = {
        id: permission.id,
        checked: hasPermission,
      };
    });

    const featureId = group.group;
    // Check if feature is selected based on any permission being checked
    const isSelected = Object.values(permissionsMap).some((p) => p.checked);
    if (isSelected) {
      selectedFeatures.value.add(featureId);
    }

    return {
      id: featureId,
      name: group.group
        .split("_")
        .map((word: any) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" "),
      selected: isSelected,
      permissions: {
        view: permissionsMap.view || { id: "", checked: false },
        create: permissionsMap.create || { id: "", checked: false },
        edit: permissionsMap.edit || { id: "", checked: false },
        delete: permissionsMap.delete || { id: "", checked: false },
      },
    };
  });
});

// Computed property for filtered features based on search
const filteredFeatures = computed(() => {
  if (!featureSearch.value) return features.value;
  const searchTerm = featureSearch.value.toLowerCase();
  return features.value.filter((feature: any) =>
    feature.name.toLowerCase().includes(searchTerm)
  );
});

// Add a computed property to check if a feature is selected
const isFeatureSelected = computed(() => (featureId: string) => {
  return selectedFeatures.value.has(featureId);
});

// Toggle all features
const areAllFeaturesSelected = computed(() => {
  return (
    filteredFeatures.value.length > 0 &&
    filteredFeatures.value.every((feature: any) =>
      isFeatureSelected.value(feature.id)
    )
  );
});

onMounted(async () => {
  await $rolesStore.fetchDetail(props.id);
  await $permissionsStore.fetchPermissions();
  if (window.location.hash === "#edit") {
    goToEdit();
  }
});

watch(
  () => window.location.hash,
  (newHash) => {
    if (newHash !== "#edit") {
      isEditMode.value = false;
    }
  }
);
</script>

<template>
  <div v-if="!isEditMode">
    <p class="text-gray-500 mb-10">detail dari role data</p>

    <div class="mt-4">
      <button
        class="flex mb-5 items-center space-x-2 px-3 py-2 text-black rounded-md"
        @click="goBack"
      >
        <svg
          id="back-arrow"
          class="-ml-4"
          height="24"
          viewBox="0 0 24 24"
          width="24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M0 0h24v24H0V0z" fill="none" opacity=".87"></path>
          <path
            d="M16.62 2.99c-.49-.49-1.28-.49-1.77 0L6.54 11.3c-.39.39-.39 1.02 0 1.41l8.31 8.31c.49.49 1.28.49 1.77 0s.49-1.28 0-1.77L9.38 12l7.25-7.25c.48-.48.48-1.28-.01-1.76z"
          ></path>
        </svg>
        <span class="font-bold text-xl">Kembali</span>
      </button>
    </div>

    <div v-if="$rolesStore.$state.isLoading.detail" class="flex justify-center">
      <general-loading />
    </div>

    <div v-else class="bg-white rounded-lg shadow p-8 mb-6">
      <h2 class="text-xl font-semibold text-gray-900 mb-4">Informasi Role</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div
          v-if="
            $auth.session?.role?.name === 'Super Admin' ||
            $auth.session?.session?.role?.name === 'Super Admin'
          "
          class="flex flex-col col-span-1"
        >
          <p class="font-semibold">Perusahaan</p>
          <p class="mt-2">{{ $rolesStore.detail?.company?.name ?? "-" }}</p>
        </div>
        <div class="flex flex-col col-span-1">
          <p class="font-semibold">Nama Role</p>
          <p class="mt-2">{{ $rolesStore.detail?.name ?? "-" }}</p>
        </div>
        <div class="flex flex-col col-span-2">
          <p class="font-semibold">Deskripsi</p>
          <p class="mt-2">
            {{
              $rolesStore.detail?.description ||
              $rolesStore.detail?.description !== ""
                ? $rolesStore.detail?.description
                : "-"
            }}
          </p>
        </div>
      </div>
      <div class="bg-white rounded-lg shadow p-6 my-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-semibold mb-2">
            Informasi Perizinan Perusahaan<span class="text-red-500">*</span>
          </h2>
          <div class="text-sm font-semibold">
            <div class="flex items-center">
              <p>Total Fitur</p>
              (
              <p class="text-red-500">{{ selectedFeatures.size }}</p>
              )
            </div>
          </div>
        </div>
        <div class="mb-4">
          <div class="relative">
            <input
              v-model="featureSearch"
              type="text"
              class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Cari fitur..."
            />
            <div
              class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
            >
              <svg
                class="h-5 w-5 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                ></path>
              </svg>
            </div>
          </div>
        </div>

        <div class="overflow-x-auto">
          <table
            class="min-w-full divide-y divide-gray-200"
            aria-describedby="Tabel Daftar Fitur"
          >
            <thead>
              <tr>
                <th
                  class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  <div class="flex items-center">
                    <input
                      type="checkbox"
                      :checked="areAllFeaturesSelected"
                      disabled
                      class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded mr-2"
                    />
                    Fitur
                  </div>
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  View
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Create
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Edit
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Delete
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="feature in filteredFeatures" :key="feature.id">
                <td
                  class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"
                >
                  <div class="flex items-center">
                    <input
                      type="checkbox"
                      :checked="isFeatureSelected(feature.id)"
                      disabled
                      class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded mr-2"
                    />
                    {{ feature.name }}
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-center">
                  <input
                    v-model="feature.permissions.view.checked"
                    type="checkbox"
                    class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                    disabled
                  />
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-center">
                  <input
                    v-model="feature.permissions.create.checked"
                    type="checkbox"
                    class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                    disabled
                  />
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-center">
                  <input
                    v-model="feature.permissions.edit.checked"
                    type="checkbox"
                    disabled
                    class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                  />
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-center">
                  <input
                    v-model="feature.permissions.delete.checked"
                    type="checkbox"
                    disabled
                    class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                  />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <div class="flex">
      <button
        type="button"
        @click="goToEdit"
        class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Edit
      </button>
    </div>
  </div>
  <div v-else>
    <forms-form-create-role :role-id="id" :is-edit="true" />
  </div>
</template>
