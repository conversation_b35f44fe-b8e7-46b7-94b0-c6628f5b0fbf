<script setup lang="ts">
const props = defineProps({
  isLoading: {
    type: Boolean,
    default: false
  },
  headers: {
    type: Array as () => { text: string, value: string }[],
    default: []
  },
  items: {
    type: Array as () => any[],
    default: []
  }
})
</script>

<template>
  <div v-if="!props.isLoading" class="w-full">
    <div v-if="props.items.length > 0" class="overflow-x-auto">
      <table class="w-full text-sm text-left">
        <thead class="text-xs text-gray-700 bg-gray-50 border-b">
        <tr>
          <th v-for="header in props.headers" scope="col" class="px-6 py-3 font-medium">
            {{ header.text }}
          </th>
        </tr>
        </thead>
        <tbody>
        <tr v-for="item in props.items" class="bg-white, border-b">
          <td v-for="header in headers" class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
            <p v-if="!$slots[`item.${header.value}`]">{{ item[header.value] }}</p>
            <slot :name="`item.${header.value}`" :item="item"/>
          </td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<style scoped>

</style>