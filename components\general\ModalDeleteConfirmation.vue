<script setup lang="ts">

const emit = defineEmits(['mounted', 'negative', 'positive'])
const props = defineProps({
  isLoading: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: ''
  },
  subtitle: {
    type: String,
    default: ''
  },
  confirmLabel: {
    type: String,
    default: ''
  }
})

</script>

<template>
  <general-modal id="modal-confirmation" title="" @mounted="emit('mounted', $event)" class-modal="max-w-sm">
    <template #body>
      <div class="flex flex-col items-center px-6 py-8">
        <!-- Icon Container -->
        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-6">
          <div class="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center">
            <slot name="icon">
              <icon-trash size="24" color="white"/>
            </slot>
          </div>
        </div>

        <!-- Content -->
        <div class="text-center mb-8">
          <h3 class="text-xl font-semibold text-gray-900 mb-3">{{ title }}</h3>
          <p class="text-sm text-gray-600 leading-relaxed">{{ subtitle }}</p>
        </div>

        <!-- Action Buttons -->
        <div class="flex w-full gap-3">
          <button
            @click="emit('negative')"
            class="flex-1 px-6 py-3 border border-gray-300 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
          >
            Batal
          </button>
          <button
            @click="emit('positive')"
            :disabled="props.isLoading"
            class="flex-1 px-6 py-3 bg-red-500 text-white text-sm font-medium rounded-lg hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <span v-if="!props.isLoading">Ya</span>
            <span v-else class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Loading...
            </span>
          </button>
        </div>
      </div>
    </template>
  </general-modal>
</template>

<style scoped>

</style>
