<script setup lang="ts">
import VueDatePicker from "@vuepic/vue-datepicker";
import "@vuepic/vue-datepicker/dist/main.css"
import type {DatePickerInstance} from "@vuepic/vue-datepicker";
import {addDays, format, isSameDay, subDays} from "date-fns";

const props = defineProps({
  modelValue: {
    type: Date,
    default: new Date()
  },
  minDate: {
    type: Date,
    default: subDays(new Date(), 1)
  },
  maxDate: {
    type: Date,
    default: new Date()
  }
})

const datePicker = ref<DatePickerInstance | null>(null)

const emit = defineEmits(['update:model-value'])

const formatDate = (date: Date) => {
  const formattedDate = format(date, 'dd MMMM yyyy')
  const isToday = isSameDay(date, new Date())

  return `${isToday ? 'Today, ' : ''}${formattedDate}`
}
</script>

<template>
  <div class="flex items-center">
    <general-icon-button
      :disabled="isSameDay(props.modelValue, props.minDate)"
      class="!h-10 bg-white rounded-r-none border-r-transparent"
      @on-click="emit('update:model-value', subDays(props.modelValue, 1))"
    >
      <template #icon>
        <icon-chevron-left size="20"/>
      </template>
    </general-icon-button>

    <vue-date-picker
      ref="datePicker"
      input-class-name="!h-10 !py-2 !rounded-none focus:!ring-primary-500"
      calendar-cell-class-name="!rounded-full"
      :model-value="props.modelValue"
      :clearable="false"
      :min-date="props.minDate"
      :max-date="props.maxDate"
      :enable-time-picker="false"
      :format="formatDate"
      class="custom-date-picker"
      @update:model-value="emit('update:model-value', $event)"
    >
      <template #action-row="{ modelValue, selectDate }">
        <div class="!w-full flex space-x-3">
          <general-outlined-button label="Cancel" class="w-full" @on-click="datePicker?.closeMenu()"/>
          <general-button label="Confirm" class="w-full" @on-click="selectDate"/>
        </div>
      </template>
    </vue-date-picker>

    <general-icon-button
      :disabled="isSameDay(props.modelValue, props.maxDate)"
      class="!h-10 bg-white rounded-l-none border-l-transparent"
      @on-click="emit('update:model-value', addDays(props.modelValue, 1))"
    >
      <template #icon>
        <icon-chevron-right size="20"/>
      </template>
    </general-icon-button>
  </div>
</template>

<style scoped>
.custom-date-picker :deep(input) {
  font-weight: 400;
}

.custom-date-picker :deep(.dp__month_year_row) {
  font-weight: 400;
}

.custom-date-picker :deep(.dp__calendar_header) {
  font-weight: 500 !important;
}

.custom-date-picker :deep(.dp__calendar) {
  font-weight: 400 !important;
}

.custom-date-picker :deep(.dp__theme_light) {
  --dp-text-color: #344054;
  --dp-primary-color: #EF3434;
  --dp-range-between-dates-background-color: #FFF5F5;
  --dp-range-between-dates-text-color: #D72F2F;
}
</style>