<script setup lang="ts">
import type { User, Role } from '~/types/server-response'

const emit = defineEmits(['mounted', 'close', 'transfer'])
const props = defineProps({
  users: {
    type: Array as PropType<User[]>,
    default: () => []
  },
  roles: {
    type: Array as PropType<Role[]>,
    default: () => []
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  companyName: {
    type: String,
    default: ''
  }
})

const selectedRole = ref('')

function handleTransfer() {
  if (!selectedRole.value) {
    return
  }
  
  emit('transfer', {
    roleId: selectedRole.value,
  })
}

function handleClose() {
  selectedRole.value = ''
  emit('close')
}
</script>

<template>
  <general-modal id="modal-transfer-users" title="" @mounted="emit('mounted', $event)" class-modal="max-w-2xl">
    <template #body>
      <div class="px-6 py-6">
        <!-- Header -->
         <div class="justify-between items-center flex">
           <div class="mb-6">
             <h2 class="text-xl font-semibold text-gray-900 mb-2">Pindahkan Pengguna</h2>
             <p class="text-sm text-gray-600">{{ companyName }}</p>
           </div>
   
           <!-- User Count -->
           <div class="mb-4">
             <span class="text-sm font-medium text-gray-700">Jumlah Pengguna (<span class="text-red-500">{{ users.length }}</span>)</span>
           </div>
         </div>

        <!-- Users List -->
        <div class="bg-gray-50 rounded-lg p-4 mb-6 max-h-64 overflow-y-auto">
          <div class="space-y-3">
            <div class="grid grid-cols-12 gap-4 text-sm font-medium text-gray-700 border-b border-gray-200 pb-2">
              <div class="col-span-1">No</div>
              <div class="col-span-5">Nama</div>
              <div class="col-span-6">Email</div>
            </div>
            
            <div v-for="(user, index) in users" :key="user.id" class="grid grid-cols-12 gap-4 text-sm text-gray-900">
              <div class="col-span-1">{{ index + 1 }}</div>
              <div class="col-span-5">{{ user.name }}</div>
              <div class="col-span-6">{{ user.email }}</div>
            </div>
          </div>
        </div>

        <!-- Role Selection -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Role <span class="text-red-500">*</span>
          </label>
          <select 
            v-model="selectedRole"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="" disabled>Pilih Role</option>
            <option v-for="role in roles" :key="role.id" :value="role.id">
              {{ role.name }}
            </option>
          </select>
        </div>

        <!-- Action Buttons -->
        <div class="flex gap-3">
          <button
            @click="handleTransfer"
            :disabled="!selectedRole || isLoading"
            class="flex-1 px-6 py-3 bg-red-500 text-white text-sm font-medium rounded-lg hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span v-if="isLoading" class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Loading...
            </span>
            <span v-else>Pindahkan</span>
          </button>
          
          <button
            @click="handleClose"
            :disabled="isLoading"
            class="flex-1 px-6 py-3 border border-gray-300 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Batal
          </button>
        </div>
      </div>
    </template>
  </general-modal>
</template>

<style scoped>

</style>