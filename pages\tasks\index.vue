<script setup lang="ts">
import {usePageStore} from '~/store/page'
import {watchDebounced} from '@vueuse/shared'
import {useTasksStore} from '~/store/tasks'
import {useEmployeesStore} from '~/store/employees'
import type {ItemCombobox} from '~/types/element'
import {useRouter} from 'vue-router'
import type {Task} from "~/types/server-response";

const $router = useRouter()
const $page = usePageStore()
const $tasks = useTasksStore()
const $employees = useEmployeesStore()

const initialQuery = {
  query: '',
  selectedField: 'created_at' as any,
  sort: 'desc',
  status: [] as string[],
  from: undefined,
  to: undefined,
  selectedEmployee: '',
  page: 1,
}

const initialQueryEmployees = ref({
  query: '',
  field: 'name',
  sort: 'desc',
  status: [],
  from: undefined,
  to: undefined,
  selectedEmployee: '',
})

const query = ref(initialQuery)
const listEmployees = ref<ItemCombobox[]>([])
onMounted(async () => {
  $page.setTitle('Daftar Tugas')

  // if (Object.keys($route.query).length > 0) {
  //   query.value = { ...$route.query } as any;
  // }
  //
  // $router.push({ query: query.value });
  try {
    await Promise.all([fetchListTask(), fetchListEmployee()])
  } catch (error) {
    console.error('An error occurred:', error)
  }
})

watchDebounced(
  () => query.value.query,
  () => {
    fetchListTask()
  },
  { debounce: 300 }
)

// watch(
//   () => query,
//   (value) => {
//     $router.push({ value });
//   },
//   { deep: true }
// );

watch(() => query.value.page, () => {
  fetchListTask()
})

watch(() => query.value.query, (newQuery) => {
  if (newQuery) {
    query.value.page = 1; 
  }
});

watch(() => query.value.from && query.value.to, (newQuery) => {
  if (newQuery) {
    query.value.page = 1
  }
});

watch(() => query.value.selectedEmployee, (newQuery) => {
  if (newQuery) {
    query.value.page = 1
  }
});

watch(() => query.value.status, (newStatus, oldStatus) => {
  const statusToCheck = ['CANCEL', 'DONE', 'DOING', 'TO DO'];
  const hasChanged = !statusToCheck.some(status => 
    newStatus?.includes(status) !== oldStatus?.includes(status)
  )
  if (hasChanged) {
    query.value.page = 1;
  }
}, { deep: true, immediate: true });

function resetQuery() {
  query.value = {
    query: '',
    selectedField: 'created_at',
    sort: 'desc',
    status: [] as string[],
    from: undefined,
    to: undefined,
    selectedEmployee: '',
    page: 1,
  }
}

async function resetData() {
  resetQuery();
  await $tasks.fetchListTasks({
    ...query.value,
    status: query.value.status.join('&&').toString(),
    employee_id: query.value?.selectedEmployee,
    field: query.value?.selectedField,
  });
}

async function fetchListTask() {
  await $tasks.fetchListTasks({
    ...query.value,
    status: query.value.status.join('&&').toString(),
    employee_id: query.value?.selectedEmployee,
    field: query.value?.selectedField,
  })
}

const fetchListEmployee = async () => {
  await $employees.fetchListEmployee({
    ...initialQueryEmployees.value, perpage: '1000'
  })

  $employees.$state.listEmployee.forEach((el) => {
    const item: ItemCombobox = {
      text: el.name,
      value: `${el.id}`,
      detail: undefined,
    }

    listEmployees.value.push(item)
  })
}

const setStatus = async (val: string) => {
  const index = query.value.status.indexOf(val)
  if (index !== -1) {
    query.value.status.splice(index, 1)
  } else {
    query.value.status.push(val)
  }
}

function labelClassColorStatus(
  status: string,
  isLabel: boolean
): 'primary' | 'error' | 'warning' | 'success' | 'info' | 'blue-gray' {
  if (status === 'DOING') {
    return 'error'
  } else if (status === 'TO DO') {
    return 'warning'
  } else if (status === 'DONE') {
    return 'success'
  }
  return 'warning'
}

function bgClassColorStatus(
  status: string,
  isLabel: boolean
): 'bg-error-500' | 'bg-warning-500' | 'bg-success-500' {
  if (status === 'DOING') {
    return 'bg-error-500'
  } else if (status === 'TO DO') {
    return 'bg-warning-500'
  } else if (status === 'DONE') {
    return 'bg-success-500'
  }

  return 'bg-warning-500'
}

function convertStatusToBahasa(status: string) {
  if (status === 'DOING') {
    return 'Dalam Proses'
  } else if (status === 'TO DO') {
    return 'Belum Dikerjakan'
  } else if (status === 'DONE') {
    return 'Selesai'
  } else if (status === 'CANCEL') {
    return 'Cancel'
  }

  return 'Belum Dikerjakan'
}

const goDetail = (id: number) => {
  $router.push(`/tasks/${id}#detail`)
}

function toggleAccordion(index: number) {
  $tasks.listTask[index].isOpen = !$tasks.listTask[index].isOpen
}

function hasChildTasks(task: Task) {
  return (
      task.hasOwnProperty('sub_tasks') &&
      Array.isArray(task.sub_tasks) &&
      task.sub_tasks.length > 0
  )
}
</script>

<template>
  <div>
    <div class="bg-white border rounded-lg shadow-sm overflow-hidden">
      <div class="pt-5 px-6 flex items-center justify-between">
        <div class="space-y-1">
          <!--          <div class="flex items-center">-->
          <!--            <p class="text-lg text-gray-900 font-medium mr-2">Total Task</p>-->
          <!--            <general-chip :label="`${$tasks.meta?.total} task`" />-->
          <!--          </div>-->
          <p class="text-gray-500">Untuk meninjau tugas yang sudah dibuat</p>
        </div>

        <div class="flex space-x-3">
          <app-export-button
            @on-click-export-excel="$tasks.fetchExportExcel()"
            @on-click-export-pdf="$tasks.fetchExportPDF()"
          />

          <general-button
            label="Tambah Tugas"
            @on-click="navigateTo('/tasks/create')"
          >
            <template #prefix>
              <icon-plus size="20" />
            </template>
          </general-button>
        </div>
      </div>

      <div class="w-full p-6 flex space-x-3">
        <general-text-input
          v-model="query.query"
          clearable
          id="inputSearchTask"
          placeholder="Search"
        >
          <template #prefix>
            <icon-search size="20" />
          </template>
        </general-text-input>
        <general-dropdown
          id-activator="btnDropdownFilter"
          id-dropdown="dropdownFilter"
        >
          <template #activator>
            <general-outlined-button
              label="Filters"
              id="btnDropdownFilter"
              data-dropdown-toggle="dropdownFilter"
              class="!h-11"
            >
              <template #prefix>
                <icon-filter-lines size="20" />
              </template>
            </general-outlined-button>
          </template>

          <template #content>
            <label
              for="status"
              class="mb-1.5 text-lg font-[600] text-gray-700 p-4"
            >
              Filter
            </label>
            <form @submit.prevent="fetchListTask">
              <div class="mt-3">
                  <label
                    for="status"
                    class="mb-1.5 mt-5 p-4 text-sm font-[600] text-gray-700"
                  >
                    Sort BY
                  </label>
                  <div class="grid gap-6 md:grid-cols-4 p-4">
                    <general-radio
                      id="radioFilterLastDeadline"
                      v-model="query.selectedField"
                      name="filter"
                      label="Last Deadline"
                      value="deadline"
                      placeholder="Pilih status"
                    />
                    <general-radio
                      id="radioFilterLastCreated"
                      v-model="query.selectedField"
                      name="filter"
                      label="Last Created"
                      value="created_at"
                      placeholder="Pilih status"
                    />
                  </div>
                </div>
              <div class="grid gap-6 md:grid-cols-2 p-4">
                <div>
                  <general-datepicker
                    v-model="query.from"
                    id="inputFilterStartDate"
                    label="Start Date"
                    placeholder="dd/mm/yyyy"
                    format="yyyy-MM-dd"
                    class="flex-1"
                  />
                </div>
                <div>
                  <general-datepicker
                    v-model="query.to"
                    id="inputFilterEndDate"
                    label="End Date"
                    placeholder="dd/mm/yyyy"
                    format="yyyy-MM-dd"
                    class="flex-1"
                  />
                </div>
              </div>
              <div class="grid gap-6 md:grid-cols-1 p-4">
                <div>
                  <general-combobox
                    id="selectFilterEmployees"
                    v-model="query.selectedEmployee"
                    has-search-field
                    id-trigger="triggerDropdownEmployee"
                    id-target="dropdownEmployee"
                    label="Karyawan"
                    plain
                    placeholder="Pilih Karyawan"
                    :items="listEmployees"
                  />
                </div>
                <div>
                  <label
                    for="status"
                    class="mb-1.5 text-sm font-[600] text-gray-700"
                  >
                    Status
                  </label>
                  <div class="grid gap-6 md:grid-cols-4 p-4">
                    <general-checkbox
                      id="cboxFilterTodo"
                      :checked="query.status.some((el) => el === 'TO DO')"
                      name="todo"
                      label="Dalam Antrian"
                      value="TO DO"
                      @change="setStatus(`TO DO`)"
                      placeholder="Pilih status"
                    />
                    <general-checkbox
                      id="cboxFilterDoing"
                      :checked="query.status.some((el) => el === 'DOING')"
                      name="doing"
                      label="Dikerjakan"
                      value="DOING"
                      @change="setStatus(`DOING`)"
                      placeholder="Pilih status"
                    />
                    <general-checkbox
                      id="cboxFilterDone"
                      :checked="query.status.some((el) => el === 'DONE')"
                      name="done"
                      label="Selesai"
                      value="DONE"
                      @change="setStatus(`DONE`)"
                      placeholder="Pilih status"
                    />
                    <general-checkbox
                      id="cboxFilterCancel"
                      :checked="query.status.some((el) => el === 'CANCEL')"
                      name="cancel"
                      label="Batal"
                      value="CANCEL"
                      @change="setStatus(`CANCEL`)"
                      placeholder="Pilih status"
                    />
                  </div>
                </div>
                <div class="flex gap-4">
                  <general-button
                    id="btnFilterApply"
                    @click="fetchListTask()"
                    label="Terapkan Filter"
                    class="p-2"
                  >
                  </general-button>
                  <general-outlined-button
                    id="btnFilterReset"
                    @click="resetQuery();resetData()"
                    label="Reset"
                    class="p-2"
                  >
                  </general-outlined-button>
                </div>
              </div>
            </form>
          </template>
        </general-dropdown>
      </div>
      <div class="overflow-x-auto">
        <table
          class="w-full text-sm text-left bg-gray-100"
          style="empty-cells: show"
        >
          <thead class="text-xs text-gray-700 bg-gray-50 border-b">
            <tr>
              <th scope="col" class="px-6 py-3 font-medium">No</th>
              <th scope="col" class="px-6 py-3 font-medium">Judul Penugasan</th>
              <th class="px-6 py-3 font-medium" scope="col">Tipe Penugasan</th>
              <th scope="col" class="px-6 py-3 font-medium">Karyawan</th>
              <th scope="col" class="px-6 py-3 font-medium">Deadline</th>
              <th scope="col" class="px-6 py-3 font-medium">Alamat Asal</th>
              <th scope="col" class="px-6 py-3 font-medium">Alamat Tujuan</th>
              <th scope="col" class="px-6 py-3 font-medium">Total Tujuan</th>
              <th scope="col" class="px-6 py-3 font-medium">Status</th>
              <th scope="col" class="px-6 py-3 font-medium" />
            </tr>
          </thead>
          <tbody>
            <tr v-if="$tasks.$state.isLoading.list">
              <td></td>
              <td></td>
              <td></td>
              <td class="h-24 flex items-center justify-center">
                <icon-circle-loading />
              </td>
              <td></td>
            </tr>
            <template
              v-else
              v-for="(task, i) in $tasks.listTask"
              :key="task.id"
            >
              <tr
                class="bg-white"
                :class="
                  hasChildTasks(task) ? 'cursor-pointer hover:bg-gray-300' : ''
                "
                @click="toggleAccordion(i)"
              >
                <td
                  class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap"
                >
                  {{ getDataTableNumber(i, query.page) }}
                </td>
                <td
                  class="px-6 py-4 font-normal text-gray-900 whitespace-wrap text-wrap"
                >
                  {{ task.title }}
                </td>
                <td
                    class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap"
                >
                  {{ task.type?.name ?? '' }}
                </td>
                <td
                  class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap"
                >
                  {{ task.penerima }}
                </td>
                <td
                  class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap"
                >
                  {{ task.deadline }}
                </td>
                <td
                  class="px-6 py-4 font-normal text-gray-900 max-w-xs truncate overflow-hidden whitespace-wrap text-wrap"
                >
                  <span class="">{{ task.alamat_asal }}</span>
                </td>
                <td
                  class="px-6 py-4 font-normal text-gray-900 max-w-xs truncate overflow-hidden whitespace-wrap text-wrap"
                >
                  <span class="">{{ task.alamat_tujuan }}</span>
                </td>
                <td
                    class="px-6 py-4 font-normal text-gray-900 max-w-xs truncate overflow-hidden whitespace-wrap text-wrap"
                >
                  <span v-if="task.sub_tasks?.length > 0" class="">{{ task.sub_tasks?.length }} Tujuan</span>
                </td>
                <td
                  class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap"
                >
                  <div
                    class="font-medium text-gray-500 dark:text-gray-400 w-1/12"
                  >
                    <general-chip
                      :variant="labelClassColorStatus(task.status, true)"
                      :label="convertStatusToBahasa(task.status)"
                      size="text-md"
                    >
                      <template #prefix>
                        <span
                          class="w-1.5 aspect-square rounded-full mr-1.5"
                          :class="bgClassColorStatus(task.status, false)"
                        />
                      </template>
                    </general-chip>
                  </div>
                </td>
                <td>
                  <div
                    class="font-medium text-gray-500 dark:text-gray-400 w-1/12"
                  >
                    <svg
                      v-if="!task.isOpen"
                      class="w-4 h-4 inline-block"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M10 5.586L16.707 12.293a1 1 0 01-1.414 1.414L10 8.414l-5.293 5.293a1 1 0 01-1.414-1.414L10 5.586z"
                        clip-rule="evenodd"
                      />
                    </svg>
                    <svg
                      v-else
                      class="w-4 h-4 inline-block"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M3 7l7 7 7-7a1 1 0 10-1.414-1.414L10 12.586 4.414 7.414A1 1 0 003 7z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </div>
                </td>
                <td
                  class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap"
                >
                  <general-text-button
                    variant="text"
                    label="Detail"
                    class="text-info-500 hover:bg-info-50"
                    @on-click="goDetail(task.id)"
                  />
                </td>
              </tr>

              <template v-if="task.isOpen && hasChildTasks(task)">
                <tr class="border bg-gray-100">
                  <th scope="col" class="px-6 py-3 font-medium">No</th>
                  <th scope="col" class="px-6 py-3 font-medium">
                    Judul Penugasan
                  </th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th scope="col" class="px-6 py-3 font-medium">
                    Alamat Tujuan
                  </th>
                  <th></th>
                  <th scope="col" class="px-6 py-3 font-medium">Status</th>
                </tr>
                <tr
                    v-for="(child, iChild) in task.sub_tasks"
                  :key="child.id"
                  class="px-4 py-2 bg-gray-100"
                >
                  <td
                      colspan="2"
                      class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap"
                  >
                    {{ getDataTableNumber(iChild, 1) }}
                  </td>
                  <template v-if="iChild === 0">
                    <td
                        :rowspan="task.sub_tasks?.length"
                        colspan="2"
                      class="px-6 py-4 font-normal text-gray-900 whitespace text-wrap text-xl"
                    >
                      {{ child.title }}
                    </td>
                  </template>
                  <td></td>
                  <td></td>
                  <td
                    class="px-6 py-4 font-normal max-w-xs text-wrap text-gray-900 whitespace-nowrap"
                  >
                    {{ child.alamat_tujuan }}
                  </td>
                  <td></td>
                  <td
                    class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap"
                  >
                    <div
                      class="font-medium text-gray-500 dark:text-gray-400 w-1/12"
                    >
                      <general-chip
                        :variant="labelClassColorStatus(child.status, true)"
                        :label="convertStatusToBahasa(child.status)"
                        size="text-md"
                      >
                        <template #prefix>
                          <span
                            class="w-1.5 aspect-square rounded-full mr-1.5"
                            :class="bgClassColorStatus(child.status, false)"
                          />
                        </template>
                      </general-chip>
                    </div>
                  </td>
                </tr>
                <tr colspan="8" class="border-t bg-gray-100"></tr>
              </template>
            </template>
          </tbody>
        </table>
      </div>
    </div>
    <general-pagination
        v-model="query.page"
        :active-page="$tasks.meta?.page"
        :total-page="Math.ceil($tasks.meta?.total/$tasks.meta?.perpage)"
    />
  </div>
</template>

<style scoped></style>
