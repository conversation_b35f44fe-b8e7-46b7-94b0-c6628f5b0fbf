<script setup lang="ts">
import { computed } from "vue";

const props = defineProps({
    modelValue: {
        type: Number,
        default: 1
    },
    totalPage: {
        type: Number,
        default: 0
    },
    activePage: {
        type: Number,
        default: 1
    },
    isPrevBtn: {
        type: Boolean,
        default: true
    }
})

const emit = defineEmits(['update:model-value'])

const page = ref<number>(1)

watch(() => props.modelValue, (value) => {
    page.value = value
})

watch(page, (value) => {
    emit('update:model-value', value)
})

const visiblePage = computed(() => {
    if (props.totalPage <= 0) return [1]

    const pages = Array.from({ length: props.totalPage }, (_, index) => index + 1)
    if (pages.length > 6) {
        let middlePages: number[] = [];
        if (props.activePage <= 2) {
            return [
                ...pages.slice(0, 3),
                -1,
                ...pages.slice(pages.length - 3, pages.length),
            ]
        } else if (props.activePage >= pages.length - 1) {
          return [
            ...pages.slice(0, 3),
            -1,
            ...pages.slice(pages.length - 3, pages.length),
        ]
        } else {
          return [
                  1,
                  -1,
                  ...pages.slice(props.activePage - 2, props.activePage + 1),
                  -1,
                  pages.length,
              ]
        }
    }
    return pages
})
</script>

<template>
    <nav class="pt-3 px-6 pb-4 w-full flex items-center justify-between">
        <general-outlined-button v-if="props.isPrevBtn" id="prevBtn" label="Previous" class="font-medium" :disabled="page === 1"
            @on-click="page -= 1" />

        <ul class="inline-flex h-10 text-gray-500 gap-2">
            <li v-for="i in visiblePage"
                :class="[activePage === i ? 'bg-[#FEF5F5] text-primary-500 hover:text-gray-500' : '', 'h-10 aspect-square rounded-lg flex items-center justify-center cursor-pointer transition hover:bg-gray-100']"
                @click="() => { if (i != -1) page = i }">
                <span>{{ i == -1 ? "..." : i }}</span>
            </li>
        </ul>

        <general-outlined-button v-if="props.isPrevBtn" id="prevBtn" label="Next" class="font-medium" :disabled="page === totalPage"
            @on-click="page += 1" />
    </nav>
</template>

<style scoped>
</style>
