<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: Object as () => any,
    default: null
  },
  items: {
    type: Array as () => any[],
    default: []
  }
})

const emit = defineEmits(['update:model-value'])

function onClickItem(item: any) {
  JSON.stringify(item) === JSON.stringify(props.modelValue)
    ? emit('update:model-value', null)
    : emit('update:model-value', item)
}
</script>

<template>
  <div>
    <div v-if="props.items.length > 0" class="flex flex-col space-y-4">
      <div v-for="item in props.items" @click.stop="onClickItem(item)">
        <slot name="item" :item="item"/>
      </div>
    </div>

    <p v-else class="text-center text-gray-500 select-none">No Items</p>
  </div>
</template>

<style scoped>

</style>