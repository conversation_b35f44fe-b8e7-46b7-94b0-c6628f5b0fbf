import { log } from "console";

const api = {
  tempAccessToken: '' as string,

  provider: (
    url: string,
    queryParams?: Record<string, string> | undefined,
    body?: Record<string, string | "" | string[] | undefined | null | object[]> | FormData | undefined,
    headers?: Record<string, string> | undefined,
    isByte?: boolean,
    useGo?: boolean
  ): { newUrl: string; newHeaders: Record<string, string> } => {
    const {token} = useAuth();
    const config = useRuntimeConfig()
    const baseUrl = useGo ? config.public.apiGoBaseUrl : config.public.baseAPI;

    if (token) {
      headers = {
        Authorization: `${token.value}`,
        ...headers,
      };
    }

    let newHeaders = {...headers}

    if (!(body instanceof FormData)) {
      newHeaders = {
        'Content-Type': isByte ? 'application/octret-stream' : 'application/json',
        ...headers,
      }
    }

    if(api.tempAccessToken) {
      newHeaders = {
        'Authorization': api.tempAccessToken
      }
    }
    const timezoneOffsetInMinutes = new Date().getTimezoneOffset();
    newHeaders = {
      'Accept': isByte ? 'application/octret-stream' : 'application/json',
      'X-Timezone-Offset': `${timezoneOffsetInMinutes}`,
      ...newHeaders
    }

    const newUrl = queryParams
      ? `${baseUrl}${url}?${new URLSearchParams(queryParams)}`
      : `${baseUrl}${url}`;

    return {newUrl, newHeaders};
  },

  afterResponse: async <T>(response: Response, isByte: boolean) => {
    const data = isByte ? await response.blob() : await response.json();

    if (response.status >= 400) {
      throw new Error(data.message)
    }

    return data;
  },

  get: async (
    url: string,
    option?: {
      queryParams?: Record<string, string>,
      headers?: Record<string, string>,
      isByte?: boolean,
      useGo?: boolean
    }
  ) => {
    const {newUrl, newHeaders} = api.provider(url, option?.queryParams, undefined, option?.headers, option?.isByte, option?.useGo)

    const isByte = option?.isByte || false
    const response = await fetch(newUrl, {
      method: 'GET',
      headers: newHeaders,
    });

    return await api.afterResponse(response, isByte)
  },

  post: async (
    url: string,
    option?: {
      queryParams?: Record<string, string>,
      body?: Record<string, string | '' | string[] | undefined | null | object[]> | FormData,
      headers?: Record<string, string>,
      isByte?: boolean,
      useGo?: boolean
    }
  ) => {
    const {newUrl, newHeaders} = api.provider(url, option?.queryParams, option?.body, option?.headers, option?.isByte, option?.useGo)

    const isByte = option?.isByte || false
    const response = await fetch(newUrl, {
      method: 'POST',
      headers: newHeaders,
      body: option?.body instanceof FormData ? option.body : JSON.stringify(option?.body)
    });

    return await api.afterResponse(response, isByte)
  },

  put: async (
    url: string,
    option?: {
      queryParams?: Record<string, string>,
      body?: Record<any, any>,
      headers?: Record<string, string>,
      isByte?: boolean,
      useGo?: boolean
    }
  ) => {
    const {newUrl, newHeaders} = api.provider(url, option?.queryParams, option?.body, option?.headers, option?.isByte, option?.useGo)

    const isByte = option?.isByte || false
    const response = await fetch(newUrl, {
      method: 'PUT',
      headers: newHeaders,
      body: option?.body instanceof FormData ? option.body : JSON.stringify(option?.body)
    });

    return await api.afterResponse(response, isByte)
  },

  delete: async (
    url: string,
    option?: {
      queryParams?: Record<string, string>,
      body?: Record<any, any>,
      headers?: Record<string, string>,
      isByte?: boolean
      useGo?: boolean
    }
  ) => {
    const { newUrl, newHeaders } = api.provider(
      url, 
      option?.queryParams, 
      option?.body, 
      {
        ...option?.headers,
        'Content-Type': 'application/json'
      }, 
      option?.isByte, 
      option?.useGo
    );
  
    const isByte = option?.isByte || false;
    const response = await fetch(newUrl, {
      method: 'DELETE',
      headers: newHeaders,
      body: option?.body ? JSON.stringify(option.body) : undefined,
    });
  
    return await api.afterResponse(response, isByte);
  },
}

export default api;
