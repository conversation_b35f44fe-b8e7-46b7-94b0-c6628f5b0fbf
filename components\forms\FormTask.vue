<script setup lang="ts">
import {toast} from 'vue3-toastify'
import type {Employee, FormLocation, GeoLocation, Task} from '~/types/server-response'
import {watchDebounced} from '@vueuse/shared'
import {useGeoLocationStore} from '~/store/geo-transtrack'
import {useTasksStore} from '~/store/tasks'
import {useEmployeesStore} from '~/store/employees'
import {useTaskTypeStore} from '~/store/task-type'
import type {ItemCombobox, ElementEvent} from '~/types/element'
import {array, object, string} from "yup";
import {format} from "date-fns";
import ModalConfirmation from "~/components/general/ModalConfirmation.vue";

const props = defineProps({
  task: {
    type: Object as () => Task | null,
    default: null,
  },
})

let modalDeleteConfirm: ElementEvent | null = null

const selectedTask = reactive({
  task: undefined as Task | undefined,
  index: undefined as number | undefined,
})
const deadline = ref<Date | string>('')
const tempSearchOrigin = ref<string>('')
let previousSearchKey = ref<string>('') //kebutuhan watchDebound
let previousDestinationKey = ref<string>('') //kebutuhan watchDebound
let previousDetailDestinationKey = ref<string>('') //kebutuhan watchDebound
let isEdit = ref<Boolean>(false)
let indexEdit = ref<any>('')

const router = useRouter()
const $tasks = useTasksStore()
const $taskType = useTaskTypeStore()

const onMarkerDragEnd = async (type: string, event: any) => {
  // await $geoLocation.openMapsLocation(
  //   event.target.getLatLng().lat,
  //   event.target.getLatLng().lng
  // )

  if (type === 'origin') {
    // $geoLocation.listLocations = []
    // $geoLocation.listLocations.push($geoLocation.resOpenMaps)
    // searchKeyLocation.value = `${$geoLocation.listLocations[0].address}`
    // setMaps()

    form.origin.lat =  event.target.getLatLng().lat
    form.origin.long = event.target.getLatLng().lng

  } else if (type === 'destination') {
    // $geoLocation.listLocations = []
    // $geoLocation.listLocations.push($geoLocation.resOpenMaps)
    // activeForm.address = `${$geoLocation.listLocations[0].address}`
    // setMapsDestination()

    form.destination[indexEdit.value].lat =  event.target.getLatLng().lat
    form.destination[indexEdit.value].long = event.target.getLatLng().lng

  }
}

interface ActiveForm {
  lat: number | null
  long: number | null
  name: string
  address: string
  detail: any
}

const goBack = () => {
  navigateTo('/tasks')
}

const triggerModal = reactive({
  showPickupCard: false,
  showDeliveryCard: false,
  showDetailDeliveryCard: false,
})


const detailDestination = (index: number | null, type: string) => {

  if (type === 'edit') {
    isEdit.value = true
    indexEdit.value = index
    triggerModal.showDeliveryCard = true
    activeForm.name = form.destination[index].name
    activeForm.address = form.destination[index].address
    activeForm.lat = form.destination[index].lat
    activeForm.long = form.destination[index].long
    return
  } else if (type === 'create') {
    activeForm.name = null as unknown as string
    activeForm.address = null as unknown as string
    triggerModal.showDeliveryCard = true
}

  isEdit.value = false
  triggerModal.showDeliveryCard = true
}

const detailOriginDestination = (index: number | null, type: string) => {
  activeForm.name = ''
  searchKeyLocation.value = ''
  triggerModal.showPickupCard = true

}

const originalOrigin = reactive({
  name: '',
  address: '',
  searchKey: '',
  lat: 0 as any,
  long: 0 as any
});

const handleEdit = () => {

  // Simpan nilai asli sebelum pengeditan
  originalOrigin.name = form.origin.name;
  originalOrigin.address = form.origin.address;
  originalOrigin.searchKey = searchKeyLocation.value;
  originalOrigin.lat = form.origin.lat;
  originalOrigin.long = form.origin.long;

  tempForm.value.name = form.origin.name;
  searchKeyLocation.value = form.origin.address;

  tempSearchKeyLocation.value = searchKeyLocation.value.trim() !== '' 
    ? searchKeyLocation.value 
    : form.origin.address;

  searchKeyLocation.value = tempSearchKeyLocation.value;

  triggerModal.showPickupCard = true;

  if (tempSearchKeyLocation.value.trim() !== '' && tempSearchKeyLocation.value !== undefined) {
    $geoLocation.searchLocation(tempSearchKeyLocation.value);
  }
};

const handleCancelOrigin = () => {
  
  // Kembalikan nilai asli jika user membatalkan
  form.origin.name = originalOrigin.name;
  form.origin.address = originalOrigin.address;
  form.origin.lat = originalOrigin.lat;
  form.origin.long = originalOrigin.long;
  searchKeyLocation.value = originalOrigin.address;
  
  triggerModal.showPickupCard = false;
};

const deleteDestination = (indexRemove: number) => {
  selectedTask.task = props.task?.childTasks?.[indexRemove]
  selectedTask.index = indexRemove
  modalDeleteConfirm?.show()
}

const $task = useTasksStore()
const $employee = useEmployeesStore()
const $geoLocation = useGeoLocationStore()

const searchKeyLocation = ref('')
const activeForm = reactive<ActiveForm>({
  lat: -6.914744,
  long: 107.60981,
  name: '',
  address: '',
  detail: {
    text: '',
    value: 1,
  },
})

const initialForm = ref({
  taskType: '',
  title: '',
  editLocation: '',
  employee: [] as any,
  referenceNumber: '',
  deadline: '',
  origin: {} as FormLocation,
  destination: [] as FormLocation[],
  notes: '',
  defaultLocation: {
    lat: -6.914744,
    long: 107.60981,
  },
  task_work_type: '',
})

const form = reactive({ ...initialForm.value })
const isLoading = ref(false)
const isProcessing = ref(false);

const isAddingDestination = ref(false)

const handleSaveDestination = () => {
  if (isAddingDestination.value) return
  
  isAddingDestination.value = true
  
  const newDestination = {
    id: undefined,
    lat: activeForm.lat,
    long: activeForm.long,
    name: activeForm.name as string,
    address: activeForm.address as string,
  }

  const isDuplicate = form.destination.some(
    dest => dest.lat === newDestination.lat && 
            dest.long === newDestination.long && 
            dest.name === newDestination.name && 
            dest.address === newDestination.address
  )

  if (!isDuplicate) {
    form.destination.push(newDestination)
    toast.success('Berhasil tambah data lokasi tujuan')
  }

  triggerModal.showDeliveryCard = false
  
  setTimeout(() => {
    isAddingDestination.value = false
  }, 500)
}

const isChangingDestination = ref(false);

const changeDestination = () => {
  if (isChangingDestination.value) return;

  isChangingDestination.value = true;

  form.destination[indexEdit.value].name = activeForm?.name;
  form.destination[indexEdit.value].address = activeForm?.address;
  form.destination[indexEdit.value].lat = activeForm?.lat;
  form.destination[indexEdit.value].long = activeForm?.long;

  toast.success('Berhasil ubah data lokasi tujuan');
  triggerModal.showDeliveryCard = false;

  setTimeout(() => {
    isChangingDestination.value = false;
  }, 1000);
};

const handleSaveOrigin = () => {
  if (!triggerModal.showPickupCard) return;
  
  triggerModal.showPickupCard = false;
  toast.success('Berhasil simpan data lokasi asal');
};

const dropdownEmployees = computed<ItemCombobox[]>(() =>
  $employee.listEmployee.map(
    (e): ItemCombobox => ({
      text: e.name,
      value: `${e.id}`,
      detail: undefined,
    })
  )
)

const dropdownTaskType = computed<ItemCombobox[]>(() =>
  $taskType.listTaskType.map(
    (e): ItemCombobox => ({
      text: e.name,
      value: `${e.id}`,
      detail: undefined,
    })
  )
)

const dropdownSearchMaps = computed<ItemCombobox[]>(() => {
  if (!$geoLocation.listLocations) {
    return []
  }
  return $geoLocation.listLocations.map(
    (e): ItemCombobox => ({
      text: e.address,
      value: e.address,
      detail: e as any,
    })
  )
})

watch(() => triggerModal.showPickupCard, (newValue) => {
  if (!newValue) {
    $geoLocation.listLocations = [];
  }
});

watch(() => triggerModal.showDeliveryCard, (newValue) => {
  if (newValue) {
    $geoLocation.listLocations = [];
  }
});

onMounted(() => {
  $employee.fetchListEmployee({perpage: '1000'})
  $taskType.fetchListTaskType({})

  initialTask(props.task)

})

const setMaps = () => {
  let val = searchKeyLocation.value as string
  let maps = dropdownSearchMaps.value.filter((item) => item.value === val)
  const temp = (maps[0]?.detail as GeoLocation) || undefined
  form.origin.address = temp?.address
  form.origin.lat = isNaN(+temp?.lat) ? 0 : +temp?.lat
  form.origin.long = isNaN(+temp?.long) ? 0 : +temp?.long
}

const setMapsDestination = () => {
  let val = activeForm.address as string
  let maps = dropdownSearchMaps.value.filter((item) => item.value === val)
  const temp = (maps[0]?.detail as GeoLocation) || undefined
  activeForm.detail.text = maps[0]?.value
  activeForm.detail.value = maps[0]?.value
  activeForm.lat = isNaN(+temp?.lat) ? 0 : +temp?.lat
  activeForm.long = isNaN(+temp?.long) ? 0 : +temp?.long
}

watchDebounced(
  () => [
    String(searchKeyLocation.value),
    String(activeForm.address),
    String(activeForm.detail.value),
  ],
  (newValue) => {
    // DETAIL ORIGIN LOCATION
    if (
      searchKeyLocation.value.trim() !== '' &&
      (searchKeyLocation.value !== previousSearchKey.value ||
        searchKeyLocation.value !== undefined)
    ) {
      previousSearchKey.value = searchKeyLocation.value
      $geoLocation.searchLocation(newValue[0])
    }

    // DETAIL ADD DESTINATION LOCATION
    if (
      activeForm.address &&
      activeForm.address !== previousDestinationKey.value
    ) {
      previousDestinationKey.value = activeForm.address
      $geoLocation.searchLocation(newValue[1])
    }

    // DETAIL DESTINATION LOCATION
    if (
      activeForm.detail.value &&
      activeForm.detail.value !== previousDetailDestinationKey.value
    ) {
      previousDetailDestinationKey.value = activeForm.detail.value
      $geoLocation.searchLocation(newValue[2])
    }
  },
  { debounce: 200 }
)

watchDebounced(
  () => form.editLocation,
  (searchKey) => {
    $geoLocation.searchLocation(searchKey)
  },
  { debounce: 200 }
)

watch(() => props.task, (newValue, oldValue) => {
  if ((newValue && newValue.title) && (newValue.id !== oldValue?.id)) {
    initialTask(newValue)
  }
})

function initialTask(newValue: any) {
  form.taskType = `${newValue?.type?.id ?? ''}`
  form.title = `${newValue?.title ?? ''}`
  form.employee = `${newValue?.employee_id?.id ?? ''}`
  if (newValue?.deadline) {
    form.deadline = format(new Date(newValue?.deadline), "yyyy-MM-dd")
    deadline.value = format(new Date(newValue?.deadline), "yyyy-MM-dd")
  }
  form.origin.address = newValue?.alamat_asal
  form.origin.lat = +(newValue?.lat_asal ?? 0)
  form.origin.long = +(newValue?.long_asal ?? 0)
  form.origin.name = newValue?.nama_lokasi_asal ?? ''
  form.notes = newValue?.description ?? ''
  form.referenceNumber = newValue?.no_referensi ?? ''
  form.task_work_type = newValue?.tipe_penugasan ?? ''

  if (newValue && newValue.childTasks?.length === 0) {
    form.destination.push({
      id: undefined,
      lat: +(newValue.lat_tujuan ?? 0),
      long: +(newValue.long_tujuan ?? 0),
      name: newValue.nama_lokasi_tujuan || newValue.destination_name,
      address: newValue.alamat_tujuan || newValue.alamat_tujuan,
    })
  } else if (newValue) {
    for (const newValueElement of newValue.childTasks ?? []) {
      form.destination.push({
        id: newValueElement.id,
        lat: +(newValueElement.lat_tujuan ?? 0),
        long: +(newValueElement.long_tujuan ?? 0),
        name: newValueElement.nama_lokasi_tujuan || newValueElement.destination_name,
        address: newValueElement.alamat_tujuan || newValueElement.alamat_tujuan,
      })
    }
  }
}

const handleSearch = (key: string) => {
  searchKeyLocation.value = key
}

const schema = object({
  taskType: string().required('Tipe task wajib diisi'),
  workType: string().required('Tipe pengerjaan tugas wajib diisi'),
  title: string()
    .required('Judul tugas wajib diisi')
    .max(500, 'Judul tugas tidak boleh lebih dari 500 karakter'),
  employee: array().required('Karyawan wajib diisi'),
  deadline: string().required('Deadline wajib diisi'),
  notes: string()
    .max(2048, 'Catatan tidak boleh lebih dari 2048 karakter')
    .required('Catatan wajib diisi'),
  origin: object()
    .shape({
      lat: string().required('Latitude lokasi asal wajib diisi'),
      long: string().required('Longitude lokasi asal wajib diisi'),
      name: string().required('Nama lokasi asal wajib diisi'),
    })
    .required(),
  destination: array()
    .of(
      object()
        .shape({
          lat: string().required('Latitude lokasi tujuan wajib diisi'),
          long: string().required('Longitude lokasi tujuan wajib diisi'),
          name: string().required('Nama lokasi tujuan wajib diisi'),
        })
        .required()
    )
    .min(1, 'Minimal terdapat satu tujuan')
    .required(),
})

const isValid = () => {
  try {
    return schema.validateSync({
      taskType: form.taskType,
      title: form.title,
      deadline: form.deadline,
      employee: [String(form.employee)],
      origin: form.origin,
      destination: form.destination,
      notes: form.notes,
      workType: form.task_work_type,
    })
  } catch (e) {
    const message = (e as ValidationError).message
    toast.error(message)
    return false
  }
}

async function onSubmit() {
  
  if (isLoading.value == true) return
  // isLoading.value = true

  if (isValid()) {
    if (!form.employee) {
      toast.error('Karyawan wajib diisi')
      isLoading.value = false
      return
    }

    if (props.task) {
      const result = await $task.updateTask(
        props.task.id,
        form.taskType,
        form.employee,
        form.title,
        form.referenceNumber,
        form.notes,
        form.origin.lat,
        form.origin.long,
        form.destination,
        form.deadline,
        form.task_work_type,
        form.origin.name,
        props.task.childTasks?.length > 0
      )

      if (result) {
        navigateTo(`/tasks/${props.task.id}`, {external: true, replace: true})
      }
    } else {
      const result = await $task.createTask(
        form.taskType,
        form.employee,
        form.title,
        form.referenceNumber,
        form.notes,
        form.origin.lat,
        form.origin.long,
        form.destination,
        form.deadline,
        form.task_work_type,
        form.origin.name
      )

      console.log(result)

      if (result) {
        // location.replace('/tasks')
        navigateTo('/tasks')
      }
    }
  }


  isLoading.value = false
}

const onAddTaskType = async (res: string) => {
  let hasSpace = hasWhiteSpace(res)
  let name = res
  let type_identity = res
  let description = ''

  if (hasSpace) {
    type_identity = removeSpaces(res)
  }

  const result = await $taskType.createTaskType(
    type_identity,
    name,
    description
  )

  form.taskType = result.id
  await $taskType.fetchListTaskType({})
}

const setDeadline = () => {
  if (deadline.value) {
    const dateValue = new Date(deadline.value)
    form.deadline = formatDate(dateValue)
  } else {
    form.deadline = ''
  }
}

const formatDate = (dt: Date): string => {
  const date = new Date(dt)
  const year = date.getFullYear()
  const month = ('0' + (date.getMonth() + 1)).slice(-2)
  const day = ('0' + date.getDate()).slice(-2)
  const hours = ('0' + date.getHours()).slice(-2)
  const minutes = ('0' + date.getMinutes()).slice(-2)
  const seconds = ('0' + date.getSeconds()).slice(-2)
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

const tempForm = ref({ name: '', address: '' });
const tempSearchKeyLocation = ref('');


</script>

<template>
  <div>
    <modal-confirmation
      id="modal-confirm-delete"
      :is-loading="$tasks.isLoading.form"
      :subtitle="`Kamu yakin ingin menghapus ${selectedTask?.task?.destination_name}?`"
      confirm-label="Konfirmasi"
      title="Konfirmasi Hapus"
      @mounted="modalDeleteConfirm = $event"
      @negative="modalDeleteConfirm?.hide()"
      @positive="() => {
        if (!selectedTask?.task) {
          form.destination.splice(selectedTask.index!, 1);
          modalDeleteConfirm?.hide();
        } else {
          $tasks.deleteTask(`${selectedTask.task.id}`).then(() => {
            form.destination.splice(selectedTask.index!, 1);
            $tasks.fetchDetailTask(`${props.task?.id}`);
            modalDeleteConfirm?.hide();
          });
        }
      }"
    />


    <p class="text-gray-500 -mt-14 mb-10">
      Lengkapi data berikut Untuk membuat tugas baru
    </p>
    <div class="mt-4">
      <button
        class="flex mb-5 items-center space-x-2 px-3 py-2 text-black rounded-md"
        @click="goBack"
      >
        <svg
          id="back-arrow"
          class="-ml-4"
          height="24"
          viewBox="0 0 24 24"
          width="24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M0 0h24v24H0V0z" fill="none" opacity=".87"></path>
          <path
            d="M16.62 2.99c-.49-.49-1.28-.49-1.77 0L6.54 11.3c-.39.39-.39 1.02 0 1.41l8.31 8.31c.49.49 1.28.49 1.77 0s.49-1.28 0-1.77L9.38 12l7.25-7.25c.48-.48.48-1.28-.01-1.76z"
          ></path>
        </svg>
        <span class="font-bold text-xl">Kembali</span>
      </button>
    </div>
    <div
      class="bg-white mb-6 border px-10 pt-12 rounded-lg shadow-sm"
    >
      <div class="grid gap-3 grid-cols-2 mb-6">
        <general-combobox
          v-model="form.taskType"
          :items="dropdownTaskType"
          has-search-field
          id-target="dropdownTaskType"
          id-trigger="triggerDropdownTaskType"
          is-inline-add
          label="Tipe Penugasan"
          :is-name="true"
          placeholder="Pilih Tipe Tugas"
          @on-click-add="onAddTaskType"
          required
        />

        <general-text-input
          id="title"
          v-model="form.title"
          label="Judul Penugasan"
          placeholder="Ex: Pengiriman Device"
          required
        />
      </div>
      <div class="grid gap-3 grid-cols-2 mb-6">
        <general-combobox
          v-model="form.employee"
          :items="dropdownEmployees"
          has-search-field
          id-target="dropdownEmployee"
          id-trigger="triggerDropdownEmployee"
          is-inline-add
          :is-multi-select="task === null"
          :is-has-create="false"
          :close-on-select="false"
          :is-name="true"
          label="Karyawan"
          plain
          placeholder="Pilih Karyawan"
          required
        />
        <general-datepicker
          id="deadline"
          v-model="deadline"
          :min-date="new Date()"
          format="yyyy-MM-dd H:mm"
          label="Tenggat Waktu"
          placeholder="Pilih Waktu Pengambilan"
          required
          @update:model-value="setDeadline"
        />
      </div>
      <div
        class="relative h-[300px] w-full bg-gray-100 rounded-lg overflow-hidden border mb-6"
      >
        <div class="absolute top-0 bottom-0 left-0 right-0 z-10">
          <general-leaflet-map
            :center="[
              Number(form.defaultLocation.lat),
              Number(form.defaultLocation.long),
            ]"
            :zoom="12"
            is-clickable
          >
            <template #marker>
              <l-marker
                v-if="form.origin.lat && form.origin.long"
                :draggable="true"
                :lat-lng="[Number(form.origin.lat), Number(form.origin.long)]"
              >
                <l-icon :icon-anchor="[14, 28]" icon-size="28">
                  <icon-marker class="stroke-error-500" size="28" />
                </l-icon>
              </l-marker>

              <l-marker
                v-for="(to, i) in form.destination"
                v-if="form.destination.length > 0"
                :key="i"
                :draggable="true"
                :lat-lng="[Number(to.lat), Number(to.long)]"
              >
                <l-icon :icon-anchor="[14, 28]" icon-size="28">
                  <icon-marker
                    class="stroke-error-500"
                    color="#3498DB"
                    size="28"
                  />
                </l-icon>
              </l-marker>
            </template>
          </general-leaflet-map>
        </div>
      </div>

      <div class="w-full gap-3 mb-6">
        <div class="flex flex-col items-start w-full mb-4">
          <div class="flex justify-between w-full">
            <div>
              <label
                class="mb-1.5 text-sm font-[600] text-gray-700"
                for="pickup-position"
              >
                Lokasi Awal
                <span class="text-primary-500">*</span>
              </label>
            </div>
            <div>
              <label
                v-if="form.origin.name || form.origin.address"
                class="mb-1.5 text-sm font-[600] text-gray-700 flex items-center space-x-2"
                for="pickup-position"
              >
                <span @click="handleEdit">
                  <icon-edit
                    class="mr-4"
                    color="#F36A6A"
                    size="16"
                    style="display: inline-block; vertical-align: middle"
                  />
                </span>
              </label>
            </div>
          </div>
          <!--  -->

          <div
            v-if="form.origin.name || form.origin.address"
            class="grid gap-6 md:grid-cols-2 p-2 border rounded-lg mb-4 bg-gray-100 w-full"
          >
            <div class="col-span-2 md:col-span-1">
              <div class="flex flex-col bg-gray-100 p-4 rounded-lg mb-4 w-full">
                <div>
                  <p class="text-lg font-semibold text-gray-700">
                    Nama Tempat Pengambilan
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">{{ form.origin.name }}</p>
                </div>
              </div>
            </div>
            <div class="col-span-2 md:col-span-1">
              <div class="flex flex-col bg-gray-100 p-4 rounded-lg mb-4">
                <div>
                  <p class="text-lg font-semibold text-gray-700">
                    Alamat Pengambilan
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">{{ form.origin.address }}</p>
                </div>
              </div>
            </div>
          </div>
          <div
            v-if="!form.origin.name && !form.origin.address"
            class="p-6 border rounded-lg w-full"
          >
            <general-button
              label="Tambah Lokasi Awal"
              type="button"
              @click="detailOriginDestination"
            >
              <template #prefix>
                <icon-plus />
              </template>
            </general-button>
          </div>
        </div>
        <div class="flex flex-col items-start w-full">
          <label
            v-if="form.destination.length < 1"
            class="mb-1.5 text-sm font-[600] text-gray-700"
            for="pickup-position"
          >
            Lokasi Tujuan
            <span class="text-primary-500">*</span>
          </label>
          <div
            v-for="(to, i) in form.destination"
            v-if="form.destination.length > 0"
            :key="i"
            class="w-full"
          >
            <div class="flex justify-between">
              <label
                class="mb-1.5 text-sm font-[600] text-gray-700"
                for="pickup-position"
              >
                Lokasi Tujuan {{ i + 1 }}
                <span class="text-primary-500">*</span>
              </label>
              <label
                  v-if="!props.task?.childTasks?.[i]?.sign_url"
                class="mb-1.5 text-sm font-[600] text-gray-700"
                for="pickup-position"
              >
                <icon-edit
                  class="mr-4"
                  color="#F36A6A"
                  size="16"
                  style="display: inline-block; vertical-align: middle"
                  @click="detailDestination(i, 'edit')"
                />
                <icon-trash
                  color="#F36A6A"
                  size="16"
                  style="display: inline-block; vertical-align: middle"
                  @click="deleteDestination(i)"
                />
              </label>
            </div>

            <div
              class="grid gap-12 md:grid-cols-2 p-2 border rounded-lg mb-4 bg-gray-100 w-full"
            >
              <div class="col-span-2 md:col-span-1 w-full">
                <div
                  class="flex flex-col bg-gray-100 p-4 rounded-lg mb-4 w-full"
                >
                  <div>
                    <p class="text-lg font-semibold text-gray-700">
                      Nama Tempat Pengambilan
                    </p>
                  </div>
                  <div>
                    <p class="text-sm text-gray-500">{{ to.name }}</p>
                  </div>
                </div>
              </div>
              <div class="col-span-2 md:col-span-1 w-full">
                <div
                  class="flex flex-col bg-gray-100 p-4 rounded-lg mb-4 w-full"
                >
                  <div>
                    <p class="text-lg font-semibold text-gray-700">
                      Alamat Pengambilan
                    </p>
                  </div>
                  <div>
                    <p class="text-sm text-gray-500">{{ to.address }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="p-6 border rounded-lg w-full">
            <general-button
              label="Tambah Lokasi Tujuan"
              type="button"
              @click="detailDestination(null, 'create')"
            >
              <template #prefix>
                <icon-plus />
              </template>
            </general-button>
          </div>
        </div>

        <transition name="fade">
          <div
            v-if="triggerModal.showPickupCard"
            class="fixed top-0 left-0 w-full h-full flex items-center justify-center z-50"
          >
            <div
              class="bg-black bg-opacity-50 absolute top-0 left-0 w-full h-full"
            ></div>
            <div
              class="p-4 bg-white rounded-lg shadow-lg relative w-[500px] h-[570]"
            >
              <div class="p-6">
                <h2 class="text-xl -ml-5 font-bold mb-2">Lokasi Awal</h2>
                <p class="text-gray-400 -ml-5 text-sm">
                  Pastikan Lokasi yang di inputkan sudah sesuai
                </p>
              </div>
              <div
                class="relative h-[230px] w-full bg-gray-100 rounded-lg overflow-hidden border mb-6"
              >
                <div class="absolute top-0 bottom-0 left-0 right-0 z-10">
                  <general-leaflet-map
                    :center="
                      form.origin.lat && form.origin.long
                        ? [Number(form.origin.lat), Number(form.origin.long)]
                        : [
                            Number(form.defaultLocation.lat),
                            Number(form.defaultLocation.long),
                          ]
                    "
                    :zoom="14"
                    is-clickable
                  >
                    <template #marker>
                      <l-marker
                        :draggable="true"
                        :lat-lng="
                          form.origin.lat && form.origin.long
                            ? [
                                Number(form.origin.lat),
                                Number(form.origin.long),
                              ]
                            : [Number(activeForm.lat), Number(activeForm.long)]
                        "
                        @dragend="onMarkerDragEnd('origin', $event)"
                      >
                        <l-icon :icon-anchor="[14, 28]" icon-size="28">
                          <icon-marker class="stroke-error-500" size="28" />
                        </l-icon>
                      </l-marker>
                    </template>
                  </general-leaflet-map>
                </div>
              </div>
              <general-text-input
                id="nameLocation"
                v-model="form.origin.name"
                class="mt-5"
                label="Nama Tempat Awal Pickup"
                placeholder="Ex: Transtrack Bandung"
                required
              />
              <general-combobox
                v-model="searchKeyLocation"
                :is-inline-add="false"
                :is-searchable="false"
                :items="dropdownSearchMaps"
                :update:model-value="handleSearch"
                :clearable="true"
                class="mt-2"
                has-search-field
                id-target="dropdownTujuan"
                id-trigger="triggerDropdownTujuan"
                label="Alamat Lengkap"
                placeholder="Ex: Alamat Tujuan"
                plain
                required
                @on-input-search="handleSearch"
                @update:model-value="setMaps"
              />
              <div class="flex w-2/3 space-x-3 mt-8">
                <button
                  id="saveButton"
                  class="border border-red-500 text-white bg-red-500 px-4 py-2 rounded-md hover:bg-red-600 focus:outline-none focus:bg-red-600 transition duration-300"
                  :disabled="!form.origin.name || !searchKeyLocation"
                  @click="handleSaveOrigin"
                >
                  Simpan
                </button>
                <button
                  id="hideLokasiAsal"
                  class="border border-red-500 text-red-500 px-4 py-2 rounded-md hover:bg-red-500 hover:text-white focus:outline-none focus:bg-red-500 focus:text-white transition duration-300"
                  @click="handleCancelOrigin"
                >
                  Batal
                </button>
              </div>
            </div>
          </div>
        </transition>

        <transition name="fade">
          <div
            v-if="triggerModal.showDeliveryCard"
            class="fixed top-0 left-0 w-full h-full flex items-center justify-center z-50"
          >
            <div
              class="bg-black bg-opacity-50 absolute top-0 left-0 w-full h-full"
            ></div>
            <div
              class="p-4 bg-white rounded-lg shadow-lg relative w-[500px] h-[570px]"
            >
              <div class="p-6">
                <h2 class="text-xl -ml-5 font-bold mb-2">Lokasi Tujuan</h2>
                <p class="text-gray-400 -ml-5 text-sm">
                  Pastikan Lokasi yang di inputkan sudah sesuai
                </p>
              </div>
              <div
                class="relative h-[200px] w-full bg-gray-100 rounded-lg mb-4 overflow-hidden border mb-6"
              >
                <div class="absolute top-0 bottom-0 left-0 right-0 z-10">
                  <general-leaflet-map
                    :center="[
                      Number(activeForm?.lat ?? 0),
                      Number(activeForm?.long ?? 0),
                    ]"
                    :zoom="14"
                    is-clickable
                  >
                    <template #marker>
                      <l-marker
                        :draggable="true"
                        :lat-lng="[
                          Number(activeForm?.lat ?? 0),
                          Number(activeForm?.long ?? 0),
                        ]"
                        @dragend="onMarkerDragEnd('destination', $event)"
                      >
                        <l-icon :icon-anchor="[14, 28]" icon-size="28">
                          <icon-marker
                            class="stroke-error-500"
                            color="#3498DB"
                            size="28"
                          />
                        </l-icon>
                      </l-marker>
                    </template>
                  </general-leaflet-map>
                </div>
              </div>
              <general-text-input
                id="location"
                v-model="activeForm.name"
                class="mt-5"
                label="Nama Tujuan"
                placeholder="Ex: Transtrack Bandung"
                required
              />
              <general-combobox
                v-model="activeForm.address"
                :items="dropdownSearchMaps"
                :is-searchable="false"
                :update:model-value="handleSearch"
                has-search-field
                :clearable="true"
                class="mt-2"
                id-target="dropdownTujuan"
                id-trigger="triggerDropdownTujuan"
                label="Alamat Lengkap"
                placeholder="Ex: Alamat Tujuan"
                plain
                required
                @on-input-search="handleSearch"
                @update:model-value="setMapsDestination"
              />
              <div class="flex w-2/3 space-x-3 mt-6">
                <button
                  v-show="isEdit"
                  class="border border-red-500 text-white bg-red-500 px-4 py-2 rounded-md hover:bg-red-600 focus:outline-none focus:bg-red-600 transition duration-300"
                  :disabled="!activeForm.name || !activeForm.address"
                  @click="changeDestination"
                >
                  Simpan
                </button>
                <button
                  v-show="!isEdit"
                  class="border border-red-500 text-white bg-red-500 px-4 py-2 rounded-md hover:bg-red-600 focus:outline-none focus:bg-red-600 transition duration-300"
                  :disabled="!activeForm.name || !activeForm.address"
                  @click="handleSaveDestination"
                >
                  Simpan
                </button>
                <button
                  id="hideDestination"
                  class="border border-red-500 text-red-500 px-4 py-2 rounded-md hover:bg-red-500 hover:text-white focus:outline-none focus:bg-red-500 focus:text-white transition duration-300"
                  @click="triggerModal.showDeliveryCard = false"
                >
                  Batal
                </button>
              </div>
            </div>
          </div>
        </transition>

        <div class="p-6"></div>
      </div>

      <div class="grid gap-3 grid-cols-2 mb-4">
        <general-textarea
          id="note"
          v-model="form.notes"
          class="mb-8"
          label="Deskripsi Penugasan"
          placeholder="Ex:Pengiriman"
          required
        />
        <general-text-input
          id="referenceNumber"
          v-model="form.referenceNumber"
          label="No Referensi"
          placeholder="Masukan No Referensi"
        />
      </div>
      <label
        class="mb-3 text-sm font-[600] text-gray-700"
        for="pickup-position"
      >
        Tipe Pengerjaan Tugas
        <span class="text-primary-500">*</span>
      </label>
      <div class="flex mt-4">
        <div class="flex items-center me-4">
          <general-radio
            id="workTypeTersusun"
            v-model="form.task_work_type"
            class="mb-8"
            label="Tersusun"
            name="worktype"
            required
            value="TERSUSUN"
          />
        </div>
        <div class="flex items-center me-4">
          <general-radio
            id="workTypeBebas"
            v-model="form.task_work_type"
            class="mb-8"
            label="Bebas"
            name="worktype"
            required
            value="BEBAS"
          />
        </div>
      </div>
    </div>
    <div class="flex w-2/3 space-x-3">
      <general-button
        :label="task ? 'Update' : 'Buat Tugas'"
        class="w-52"
        :loading="isLoading"
        type="Buat Tugas"
        @click="onSubmit"
      />
      <general-outlined-button class="w-44" label="Batal " @click="goBack" />
      <!-- @on-click="
          form = { ...initialForm }
          emit('on-click-close')
        " -->
    </div>
  </div>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
