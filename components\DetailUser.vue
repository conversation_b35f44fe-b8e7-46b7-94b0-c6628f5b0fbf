<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useUsersStore } from "~/store/users";
import { useRolesStore } from "~/store/roles";
import { useCompaniesStore } from "~/store/companies";
import { toast } from "vue3-toastify";
import { useAuthStore } from "~/store/auth";

const props = defineProps<{
  id: string;
  isPublic?: boolean;
}>();

const $usersStore = useUsersStore();
const $auth = useAuthStore();

const router = useRouter();
const isEditMode = ref(false);
const isLoading = ref(false);

async function goToEdit() {
  isLoading.value = true;
  try {
    await new Promise((resolve) => setTimeout(resolve, 1000));
    isEditMode.value = true;
    window.location.hash = "edit";
  } finally {
    isLoading.value = false;
  }
}

function goBack() {
  navigateTo("/users");
}

onMounted(async () => {
  await $usersStore.fetchDetail(props.id);
  if (window.location.hash === "#edit") {
    goToEdit();
  }
});

watch(
  () => window.location.hash,
  (newHash) => {
    if (newHash !== "#edit") {
      isEditMode.value = false;
    }
  }
);
</script>

<template>
  <div v-if="isLoading" class="flex justify-center items-center min-h-screen">
    <general-loading />
  </div>
  <div v-else-if="!isEditMode">
    <p class="text-gray-500 mb-10">detail dari akun pengguna</p>

    <div class="mt-4">
      <button
        class="flex mb-5 items-center space-x-2 px-3 py-2 text-black rounded-md"
        @click="goBack"
      >
        <svg
          id="back-arrow"
          class="-ml-4"
          height="24"
          viewBox="0 0 24 24"
          width="24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M0 0h24v24H0V0z" fill="none" opacity=".87"></path>
          <path
            d="M16.62 2.99c-.49-.49-1.28-.49-1.77 0L6.54 11.3c-.39.39-.39 1.02 0 1.41l8.31 8.31c.49.49 1.28.49 1.77 0s.49-1.28 0-1.77L9.38 12l7.25-7.25c.48-.48.48-1.28-.01-1.76z"
          ></path>
        </svg>
        <span class="font-bold text-xl">Kembali</span>
      </button>
    </div>

    <div v-if="$usersStore.$state.isLoading.detail" class="flex justify-center">
      <general-loading />
    </div>

    <div v-else class="bg-white rounded-lg shadow p-8 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="flex flex-col col-span-2">
          <p class="font-semibold">Tipe Pengguna</p>
          <p class="mt-2">{{ $usersStore.detail?.user_type ?? "-" }}</p>
        </div>
        <div
           v-if="$auth.session?.role?.name === 'Super Admin' || $auth.session?.session?.role?.name === 'Super Admin'"
          class="flex flex-col"
        >
          <p class="font-semibold">Nama Perusahaan</p>
          <p class="mt-2">{{ $usersStore.detail?.company?.name ?? "-" }}</p>
        </div>
        <div class="flex flex-col">
          <p class="font-semibold">Role</p>
          <p class="mt-2">{{ $usersStore.detail?.role?.name ?? "-" }}</p>
        </div>
        <div class="flex flex-col">
          <p class="font-semibold">Nama Pengguna</p>
          <p class="mt-2">{{ $usersStore.detail?.name ?? "-" }}</p>
        </div>
        <div class="flex flex-col">
          <p class="font-semibold">Email</p>
          <p class="mt-2">{{ $usersStore.detail?.email ?? "-" }}</p>
        </div>
        <div class="flex flex-col">
          <p class="font-semibold">No Telepon</p>
          <p class="mt-2">{{ $usersStore.detail?.phone ?? "-" }}</p>
        </div>
        <div class="flex flex-col col-span-2">
          <p class="font-semibold">PIN Akun</p>
          <p class="mt-2">{{ $usersStore.detail?.pin ?? "-" }}</p>
        </div>
      </div>
    </div>
    <div class="flex">
      <button
        type="button"
        @click="goToEdit"
        class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Edit
      </button>
    </div>
  </div>
  <div v-else>
    <forms-form-create-user :user-id="id" :is-edit="true" />
  </div>
</template>

<style scoped>
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
</style>
