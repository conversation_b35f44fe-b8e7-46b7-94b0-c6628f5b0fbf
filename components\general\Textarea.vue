<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: String || Number || null,
  },
  id: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'text'
  },
  label: {
    type: String || null,
    default: null
  },
  placeholder: {
    type: String,
    default: ''
  },
  required: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['on-input', 'update:modelValue'])
</script>

<template>
  <div>
    <label v-if="props.label" :for="id" class="block mb-1.5 text-sm font-[600] text-gray-700">
      {{ props.label }}
      <span v-if="props.required" class="text-primary-500">*</span>
    </label>
    <div class="relative stroke-gray-500">
      <div class="absolute pl-3 inset-y-0 left-0 flex items-center pointer-events-none">
        <slot name="prefix"/>
      </div>
      <textarea
        :id="id"
        :value="props.modelValue"
        :type="props.type"
        :disabled="props.disabled"
        :placeholder="props.placeholder"
        rows="3"
        class="py-2.5 px-3.5 block w-full border border-gray-300 text-gray-900 rounded-lg focus:ring-primary-500 focus:border-primary-500 resize-none"
        :class="$slots.prefix ? 'pl-10' : ''"
        @input="emit('update:modelValue', $event.target.value)"
      />
    </div>
  </div>
</template>

<style scoped> </style>
