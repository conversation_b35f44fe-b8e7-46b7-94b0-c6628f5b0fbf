meta {
  name: Get Employee
  type: http
  seq: 2
}

get {
  url: https://mytask-api.transtrack.id/employee/get-attendence?query&perpage=10&page&field=name&sort=asc
  body: none
  auth: bearer
}

query {
  query: 
  perpage: 10
  page: 
  field: name
  sort: asc
}

auth:bearer {
  token: {{token}}
}

script:pre-request {
  // 
}

tests {
  // if(pm.response.code == 200) {
  //     pm.test("Status code is 200");
  // } else if(pm.response.code == 401) {
  //     pm.test("Status code is 401");
  // }
  // 
  // var getParams = pm.request.url.query.all();
  // if(getParams) {
  //     getParams.forEach(function(getParam){
  //         pm.test("Param value = " + getParam.value);
  //     });   
  // }
  //  
}
