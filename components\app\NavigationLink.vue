<script setup lang="ts">
import {useRoute} from "#app";

const props = defineProps({
  id: {
    type: String,
    default: ''
  },
  destination: {
    type: String,
    default: '#'
  },
  name: {
    type: String,
    default: ''
  },
  target: {
    type: String,
    default: '_self'
  }
})

const linkClass = computed((): string => {
  const isActive = useRoute().path.startsWith(props.destination)

  const hover = isActive ? 'bg-[#FDEBEB] text-[#EF3434]' : 'hover:bg-gray-100'
  return `py-2 px-3 space-x-3 flex items-center rounded-md transition ${hover}`
})
</script>

<template>
  <nuxt-link
    :id="props.id"
    :to="props.destination"
    :target="props.target"
    :class="linkClass"
  >
    <slot name="icon"/>
    <span class="font-medium text-base">{{props.name}}</span>
  </nuxt-link>
</template>

