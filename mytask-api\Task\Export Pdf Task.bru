meta {
  name: Export Pdf Task
  type: http
  seq: 8
}

get {
  url: {{base_url}}/task/export-pdf
  body: none
  auth: bearer
}

auth:bearer {
  token: {{token}}
}

script:pre-request {
  // 
}

tests {
  // if(pm.response.code == 200) {
  //     pm.test("Status code is 200");
  // } else if(pm.response.code == 401) {
  //     pm.test("Status code is 401");
  // }
  // 
  // var getParams = pm.request.url.query.all();
  // if(getParams) {
  //     getParams.forEach(function(getParam){
  //         pm.test("Param value = " + getParam.value);
  //     });   
  // }
  //  
}
