<script setup lang="ts">
const props = defineProps({
  id: {
    type: String,
    default: ''
  },
  isChecked: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['on-change'])

function onChange (isChecked: boolean) {
  emit('on-change', isChecked)
}
</script>

<template>
  <label class="relative inline-flex items-center" :class="props.disabled ? 'cursor-not-allowed' : 'cursor-pointer'">
    <input :disabled="props.disabled" :checked="props.isChecked" :id="props.id" type="checkbox" class="sr-only peer"
           @change="onChange($event.target.checked)">
    <span
      class="w-11 h-6 bg-gray-200 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600"
      :class="props.disabled ? 'peer-checked:bg-primary-300' : 'peer-checked:bg-primary-500'"
    />
  </label>
</template>

<style scoped>

</style>