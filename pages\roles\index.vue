<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {usePageStore} from "~/store/page";
import { useRolesStore } from '~/store/roles'
import { useUsersStore } from '~/store/users'
import type { Role, User } from '~/types/server-response';
import type { ElementEvent } from '~/types/element';
import ModalDeleteConfirmation from '~/components/general/ModalDeleteConfirmation.vue';
import ModalWarning from '~/components/general/ModalWarning.vue';
import ModalTransferUsers from '~/components/general/ModalTransferUsers.vue';
import ModalSuccess from '~/components/general/ModalSuccess.vue';

const $rolesStore = useRolesStore()
const $usersStore = useUsersStore()

const query = ref({
  search_value: '',
  search_columns: 'name',
  page: 1,
  page_size: 10,
})

const $page = usePageStore()
const $router = useRouter()

onMounted(async () => {
  $page.setTitle('Manajemen Role')
})


onMounted(async () => {
  $rolesStore.fetchRoles()
})

const options = ref([
  { text: '10', value: '10' },
  { text: '20', value: '20' },
  { text: '30', value: '30' },
  { text: '40', value: '40' },
  { text: '50', value: '50' }
])

const selected = ref('10')

function fetchRoles() {
  $rolesStore.fetchRoles({...query.value})
}

watch(() => selected.value, (newValue) => {
  query.value.page_size = Number(newValue)
  query.value.page = 1
  fetchRoles()
})

// Watch for search value changes with debouncing
let searchTimeout: NodeJS.Timeout | null = null

onUnmounted(() => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
    searchTimeout = null
  }
})

watch(() => query.value.search_value, () => {
  // Clear previous timeout
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  // Reset page to 1 when search value changes
  query.value.page = 1
  
  searchTimeout = setTimeout(() => {
    fetchRoles()
  }, 300) // 300ms delay
})

// Update query when store state changes (excluding search to avoid conflicts)
watch([() => query.value.page, () => query.value.page_size], () => {
  fetchRoles()
})

const activeAction = ref<Role | null>(null)
const warningData = ref<{ userCount: number; message: string } | null>(null)
const transferUsers = ref<User[]>([])
const isTransferModalOpen = ref(false)

// Watch for transfer modal state to fetch roles filtered by company_id
watch(isTransferModalOpen, (isOpen) => {
  if (isOpen && activeAction.value?.company?.id) {
    $rolesStore.fetchRoles({
      page_size: 1000,
      company_id: activeAction.value.company.id.toString()
    })
  }
})
let modalDeleteConfirm: ElementEvent | null = null
let modalWarning: ElementEvent | null = null
let modalTransferUsers: ElementEvent | null = null
let modalSuccess: ElementEvent | null = null

function goDetail(id: string) {
  $router.push(`/roles/${id}#detail`)
}

function goToEdit(id: string) {
  $router.push(`/roles/${id}#edit`)
}

async function handleDeleteRole(role: Role) {
  activeAction.value = role
  modalDeleteConfirm?.show()
}

async function confirmDelete() {
  if (activeAction.value) {
    const result = await $rolesStore.deleteRole(activeAction.value.id)
    if (result.success) {
      modalWarning?.hide()
      await fetchRoles()
      modalDeleteConfirm?.hide()
    } else if (result.error === 'ROLE_HAS_USERS' || result.error === 'UNKNOWN_ERROR') {
      warningData.value = {
        userCount: result.userCount || 0,
        message: result.message || ''
      }
      modalDeleteConfirm?.hide()
      modalWarning?.show()
    }
    // For other errors, the store already shows toast messages
  }
}

async function handleTransferUsers() {
  if (activeAction.value) {
    // Fetch users with the role_id filter
    await $usersStore.fetchUsers({
      filter_columns: 'role_id',
      filter_value: activeAction.value.id.toString()
    })
    
    transferUsers.value = $usersStore.users
  }
}

async function handleTransferUsersClick() {
  modalWarning?.hide()
  isTransferModalOpen.value = true
  modalTransferUsers?.show()
}

async function handleUserTransfer(data: { roleId: string; password: string }) {
  try {
    // Prepare payload for bulk update
    const payload = {
      role_id: data.roleId,
      user_ids: transferUsers.value.map(user => user.id)
    }
    
    // Call the bulk update API
    const result = await $rolesStore.bulkUpdateUsers(payload)
    
    if (result.success) {
      // Close the transfer modal
      modalTransferUsers?.hide()
      transferUsers.value = []
      isTransferModalOpen.value = false
      
      // Show success dialog
      showSuccessDialog()
      
      // Refresh the roles list to update counts
      await $rolesStore.fetchRoles()
    }
  } catch (error) {
    console.error('Error transferring users:', error)
  }
}

function handleTransferCancel() {
  modalTransferUsers?.hide()
  transferUsers.value = []
  isTransferModalOpen.value = false
}

function showSuccessDialog() {
  modalSuccess?.show()
}

const groupPermissions = (permissions: any[]) => {
  if (!permissions?.length) return [];
  
  const groups = permissions.reduce((acc, permission) => {
    if (!acc[permission.group]) {
      acc[permission.group] = [];
    }
    acc[permission.group].push(permission.name);
    return acc;
  }, {});
  
  return Object.entries(groups).map(([group, permissions]) => ({
    group: group.charAt(0).toUpperCase() + group.slice(1).replace(/_/g, ' '),
    permissions
  }));
};
</script>

<template>
  <div class="bg-white rounded-xl space-y-4">
    <!-- Title and Subtitle -->
    <div class="p-6">
      <p class="text-xs text-gray-500 mt-1">Atur peran dan izin akses pengguna sesuai tanggung jawabnya</p>
    </div>

    <!-- Filter/Search/Date Bar -->
    <div class="p-6">
      <div class="w-full flex">
        <general-text-input v-model="query.search_value" clearable id="inputSearchUser" placeholder="Search">
          <template #prefix>
            <icon-search size="20"/>
          </template>
        </general-text-input>
        <div class="flex space-x-3 w-full items-center justify-end">
            <general-button
              label="Tambah Role"
              @on-click="navigateTo('/roles/create')"
            >
            <template #prefix>
              <icon-plus size="20"/>
            </template>
          </general-button>
          </div>
      </div>
    </div>

    <!-- Companies List -->
    <div class="rounded-xl overflow-x-auto border border-gray-200">
      <table 
          class="w-full text-sm text-left" 
          style="empty-cells: show;" 
          aria-describedby="Tabel Daftar Perusahaan"
        >
        <thead class="text-xs text-gray-700 bg-gray-50 border-b">
          <tr>
            <th class="px-6 py-4 text-left text-xs font-bold text-gray-500">No</th>
            <th class="px-6 py-4 text-left text-xs font-bold text-gray-500">Perusahaan</th>
            <th class="px-6 py-4 text-left text-xs font-bold text-gray-500">Nama Role</th>
            <th class="px-6 py-4 text-left text-xs font-bold text-gray-500">Fitur</th>
            <th class="px-6 py-4 text-left text-xs font-bold text-gray-500">Jumlah Pengguna</th>
            <th class="px-6 py-4 text-right text-xs font-bold text-gray-500"></th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="$rolesStore.$state.isLoading.list">
            <td></td>
            <td></td>
            <td></td>
            <td class="h-24 flex items-center justify-center">
              <icon-circle-loading/>
            </td>
            <td></td>
          </tr>
          <tr v-else v-for="(role, index) in $rolesStore?.roles" :key="role.id">
            <td class="px-6 py-4 text-sm text-gray-900 align-middle">{{ ((query?.page - 1) * query?.page_size) + index + 1 }}</td>
            <td class="px-6 py-4 text-sm text-gray-500 font-semibold align-middle">{{ role?.company?.name || '-' }}</td>
            <td class="px-6 py-4">
              <div class="flex items-center gap-3">
                <div>
                  <div class="flex items-center gap-2">
                    <span class="text-sm font-semibold text-gray-500">{{ role?.name || '-' }}</span>
                  </div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 text-sm text-gray-500 font-semibold align-middle">
              <div class="relative group inline-block">
                <div class="flex items-center gap-1 cursor-pointer">
                  <icon-info size="20" class="stroke-gray-500"/>
                  <span>
                    {{ (role?.permissions?.length ?? 0) + ' Fitur' }}
                  </span>
                </div>
                <div class="absolute left-1/2 transform -translate-x-1/2 z-50 hidden group-hover:block w-64 bg-white rounded-lg shadow-2xl border border-gray-200 overflow-hidden">
                  <div class="p-4 border-b border-gray-200 bg-gray-50">
                    <p class="text-sm font-medium text-gray-900">Fitur <span class="text-xs text-red-500">({{ (role?.permissions?.length ?? 0) }})</span></p>
                  </div>
                  <div class="max-h-80 overflow-y-auto">
                    <div v-if="groupPermissions(role?.permissions)?.length">
                      <div v-for="(group, idx) in groupPermissions(role?.permissions)" :key="idx" class="p-2 m-2">
                        <p class="text-xs font-medium text-gray-500">{{ idx + 1 }}. {{ group.group }}</p>
                      </div>
                    </div>
                    <div v-else class="p-4 text-center text-sm text-gray-500">
                      Tidak ada izin yang ditetapkan
                    </div>
                  </div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 text-sm text-gray-500 font-semibold align-middle">{{ role?.user_counts + ' Pengguna' || '-' }}</td>
            <td class="px-1 py-4 font-normal text-gray-900 whitespace-nowrap">
              <general-dropdown :id-dropdown="`dropdownAction-${index}`" id-activator="btnDropdownAction" placement="right">
                <template #activator>
                  <general-icon-button
                      id="btnDropdownAction"
                      :data-dropdown-toggle="`dropdownAction-${index}`"
                      @click="'#'"
                  >
                    <template #icon> 
                      <icon-three-dot/>
                    </template>
                  </general-icon-button>
                </template>

                <template #content>
                  <div class="py-2.5 px-4 flex items-center cursor-pointer hover:bg-gray-100 group" @click="goToEdit(role.id.toString())">
                    <p class="text-sm group-hover:text-red-500">Edit</p>
                  </div>
                  <div class="py-2.5 px-4 flex items-center cursor-pointer hover:bg-gray-100 group" @click="goDetail(role.id.toString())">
                    <p class="text-sm group-hover:text-red-500">Detail</p>
                  </div>
                  <div class="py-2.5 px-4 flex items-center cursor-pointer hover:bg-gray-100 group">
                    <p class="text-sm group-hover:text-red-500" @click="handleDeleteRole(role)">Hapus</p>
                  </div>
                </template>
              </general-dropdown>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Delete Confirmation Modal -->
    <ModalDeleteConfirmation
      id="modal-confirm-delete"
      :is-loading="$rolesStore.isLoading.form"
      confirm-label="Konfirmasi"
      :subtitle="`Apakah Anda yakin ingin menghapus role ${activeAction?.name || ''} dari My Task?`"
      title="Hapus Role?"
      @mounted="modalDeleteConfirm = $event"
      @negative="modalDeleteConfirm?.hide()"
      @positive="confirmDelete(); handleTransferUsers()"
    />

    <!-- Warning Modal for Role with Users -->
    <ModalWarning
      id="modal-warning"
      title="Role Masih Memiliki Pengguna !"
      :subtitle="`Jika ingin menghapus role ini, silakan pindahkan ${transferUsers?.length || 5} pengguna ke role lain terlebih dahulu.`"
      @mounted="modalWarning = $event"
      @close="modalWarning?.hide()"
      @transfer="handleTransferUsersClick()"
    />

    <!-- Transfer Users Modal -->
    <ModalTransferUsers
      id="modal-transfer-users"
      :users="transferUsers"
      :roles="$rolesStore.roles.filter(role => role.id !== activeAction?.id)"
      :is-loading="$usersStore.loading"
      :company-name="activeAction?.company?.name || ''"
      @mounted="modalTransferUsers = $event"
      @close="handleTransferCancel"
      @transfer="handleUserTransfer"
    />

    <!-- Success Modal -->
    <ModalSuccess
      id="modal-success"
      title="Pemindahan Berhasil"
      subtitle="Pengguna berhasil dipindahkan ke role yang dituju"
      button-label="Lanjutkan"
      @mounted="modalSuccess = $event"
      @close="modalSuccess?.hide(); confirmDelete()"
    />

    <!-- Pagination & Info -->
    <div class="flex justify-between mt-2 items-center">
        <div class="flex">
            <select v-model="selected" style="width: 80px;  border-radius: 8px">
            <option v-for="option in options" :value="option.value">
                {{ option.text }}
            </option>
            </select>
            <p class="flex items-center ml-2">{{ selected }} From <span class="ml-2">{{ $rolesStore.roles.length || 0 }} Data</span></p>
        </div>
        <div>
          <general-pagination
            v-model="query.page"
            :active-page="$rolesStore?.page?.page"
            :total-page="$rolesStore?.page?.total_pages"
            :is-prev-btn="false"
            />
        </div>
    </div>
  </div>
</template>
