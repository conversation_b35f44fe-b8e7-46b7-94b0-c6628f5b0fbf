meta {
  name: Check In Mobile
  type: http
  seq: 2
}

post {
  url: {{mobile_url}}/home/<USER>
  body: multipartForm
  auth: bearer
}

headers {
  X-Mobile-App: Android
  X-Mobile-App-Version: 7.0
}

auth:bearer {
  token: {{mobileToken}}
}

body:multipart-form {
  lat: -6.92633
  long: 107.58744
}

script:pre-request {
  // 
}

tests {
  // if(pm.response.to.have.status(200)) {
  //         pm.test("Status code is 200", function () {
  //         pm.response.to.have.status(200);
  //     });
  // }
}
