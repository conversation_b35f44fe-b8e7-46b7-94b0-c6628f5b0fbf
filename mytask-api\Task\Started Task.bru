meta {
  name: Started Task
  type: http
  seq: 5
}

post {
  url: {{base_url}}/task/started-task/:taskId
  body: multipartForm
  auth: bearer
}

auth:bearer {
  token: {{token}}
}

body:multipart-form {
  _method: PUT
}

tests {
  // if(pm.response.code == 200) {
  //     pm.test("Status code is 200");
  // } else if(pm.response.code == 401) {
  //     pm.test("Status code is 401");
  // }
  // 
  // var status = pm.response.json().data.status;
  // if(status != null) {
  //     pm.test("Status task is " + status);
  // }
}
