{"version": "0.3.27", "name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "devDependencies": {"@nuxt/devtools": "latest", "@nuxtjs/tailwindcss": "^6.9.4", "@pinia-plugin-persistedstate/nuxt": "^1.2.0", "@sidebase/nuxt-auth": "^0.6.2", "@vite-pwa/assets-generator": "^0.2.3", "@vite-pwa/nuxt": "^0.4.0", "nuxt": "^3.11.2", "nuxt3-leaflet": "^1.0.12", "vue": "^3.3.8", "vue-router": "^4.2.5"}, "dependencies": {"@nuxt/image": "^1.2.0", "@pinia/nuxt": "^0.5.1", "@vuepic/vue-datepicker": "^7.3.0", "chart.js": "^4.3.0", "date-fns": "^2.30.0", "flowbite": "^2.1.1", "gsap": "^3.12.5", "jose": "^5.2.4", "leaflet": "^1.9.4", "pinia": "^2.1.7", "vue-chartjs": "^5.2.0", "vue3-toastify": "^0.1.14", "vuedraggable": "^4.1.0", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.1/xlsx-0.20.1.tgz", "yup": "^1.3.2"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5"}, "resolutions": {"sharp": "^0.29.0"}}