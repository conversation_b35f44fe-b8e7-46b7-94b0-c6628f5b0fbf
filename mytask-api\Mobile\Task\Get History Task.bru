meta {
  name: Get History Task
  type: http
  seq: 4
}

get {
  url: {{mobile_url}}/task/task-history
  body: multipartForm
  auth: bearer
}

query {
  ~query: 2
  ~from: 2021-09-01
  ~to: 2021-09-03
  ~field: title
  ~sort: desc
  ~status: DONE
}

headers {
  X-Mobile-App: Android
  X-Mobile-App-Version: 4.4
}

auth:bearer {
  token: {{mobileToken}}
}

tests {
  // if(pm.response.code == 200) {
  //     pm.test("Status code is 200");
  // } else if(pm.response.code == 401) {
  //     pm.test("Status code is 401");
  // }
}
