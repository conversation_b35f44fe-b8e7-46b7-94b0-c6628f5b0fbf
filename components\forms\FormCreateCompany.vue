<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { toast } from 'vue3-toastify';
import { useCompaniesStore } from '~/store/companies';
import { usePermissionsStore } from '~/store/permissions';

const props = defineProps({
  company: {
    type: Object,
    default: null
  }
})

const DEFAULT_MAP_ENGINE = 'google';
const emit = defineEmits(['on-success', 'on-cancel'])

const $companiesStore = useCompaniesStore()
const $permissionsStore = usePermissionsStore()

const router = useRouter();

// Form data structure
const form = reactive({
  name: '',
  avatar: '',
  industry_id: '',
  email: '',
  phone: '',
  address: '',
  description: '',
  total_working_hour: 0,
  late_tolerance: 0,
  map_engine: DEFAULT_MAP_ENGINE,
  logo: null as File | null,
  logoPreviewUrl: '',
  permissions: [] as string[],
  admin: {
    name: '',
    email: '',
    phone: '',
    password: ''
  }
})

const company = ref({
  logo: null as File | null,
  name: '',
  industry: '',
  email: '',
  phone: '',
  description: '',
  total_working_hour: '',
  late_tolerance: '',
  mapEngine: 'google',
  logoPreviewUrl: null as string | null,
});

const admin = ref({
  name: '',
  email: '',
  phone: '',
  password: ''
});

const isLoading = ref(false)
const isSubmitting = ref(false)

// Initialize form with company data if in edit mode
function initializeForm() {
  if (props.company) {
    const { 
      name,
      avatar,
      industry, 
      email, 
      phone, 
      address, 
      description, 
      total_working_hour,
      late_tolerance,
      map_engine,
      logo_url,
      permissions,
      first_user
    } = props.company

    // Map company data to form
    form.name = name || ''
    form.avatar = avatar || ''
    form.industry_id = industry?.id?.toString() || ''
    form.email = email || ''
    form.phone = phone || ''
    form.address = address || ''
    form.description = description || ''
    form.total_working_hour = total_working_hour || ''
    form.late_tolerance = late_tolerance || ''
    form.map_engine = map_engine || ''
    form.logoPreviewUrl = logo_url || ''
    form.admin.name = first_user?.name || ''
    form.admin.email = first_user?.email || ''
    form.admin.phone = first_user?.phone || ''
    form.admin.password = first_user?.password || ''

    // Map permissions
    if (permissions && Array.isArray(permissions)) {
      form.permissions = permissions.map(p => p.key)
    }
  }
}

const isCompanyFormInvalid = computed(() => {
  // Hanya validasi field wajib utama: name, industry_id, dan map_engine
  // Field email, phone, dan description sekarang opsional berdasarkan requirements terbaru
  return !form.name || 
         !form.industry_id ||
         !form.map_engine;
});

const isAdminFormInvalid = computed(() => {
  // Validasi admin hanya dijalankan saat membuat perusahaan baru (mode create).
  if (!props.company) {
    return !form.admin.name || 
           !form.admin.email || 
           !form.admin.phone || 
           !form.admin.password;
  }
  
  // Saat mode edit, field admin tidak bisa diubah (readonly di UI),
  // sehingga kita tidak perlu menjalankan validasi untuk field tersebut.
  // Mengembalikan `false` berarti "bagian form admin dianggap valid".
  return false; 
});


const isPermissionsInvalid = computed(() => {
  return getSelectedPermissions().length === 0;
});

const isFormInvalid = computed(() => {
  return isAdminFormInvalid.value || isCompanyFormInvalid.value || isPermissionsInvalid.value;
});


onMounted(async () => {
  try {
    isLoading.value = true
    await Promise.all([
      $companiesStore.getIndustries(),
      $permissionsStore.fetchPermissions()
    ])
    initializeForm()
  } catch (error) {
    console.error('Error initializing form:', error)
    toast.error('Gagal memuat data formulir')
  } finally {
    isLoading.value = false
  }
})

const permissions = computed(() => $permissionsStore.permissions)

const industries = computed(() => $companiesStore.industries.map(industry => ({
  text: industry.name,
  value: industry.id.toString()
})))

// Check if component is in edit mode using URL query or hash or props
const route = useRoute()
const isEditMode = computed(() => {
  const hasQuery = !!route.query && Object.keys(route.query).length > 0
  const hasEditHash = route.hash === '#edit'
  const hasCompanyProp = !!props.company
  return hasQuery || hasEditHash || hasCompanyProp
})

const featureSearch = ref('');

// Add this ref to track selected features
const selectedFeatures = ref<Set<string>>(new Set());

// Create a reactive state for permission selections that can be modified
const permissionSelections = ref<Map<string, boolean>>(new Map());

// Pre-selected permission ids when editing
const preselectedPermissionIds = computed<Set<string>>(() => {
  const ids = new Set<string>();
  if (props.company && Array.isArray(props.company.permissions)) {
    props.company.permissions.forEach((p: any) => {
      // id or key might be provided depending on backend response
      if (p?.id) ids.add(p.id.toString());
      if (p?.key) ids.add(p.key.toString());
    });
  }
  return ids;
});

// Transform the permissions data for the table – ensure checked state reflects edit mode
const features = computed(() => {
  if (!permissions.value) return [];

  // Local helper to know if a permission should be checked
  const isChecked = (perm: any) => {
    const permId = perm.id?.toString() || perm.key?.toString();
    // Check reactive selections first, then fall back to preselected
    if (permissionSelections.value.has(permId)) {
      return permissionSelections.value.get(permId);
    }
    return preselectedPermissionIds.value.has(perm.id?.toString()) ||
           preselectedPermissionIds.value.has(perm.key?.toString());
  };

  // Build feature rows
  return permissions.value.map(group => {
    const permissionsMap: Record<string, { id: string; checked: boolean }> = {};
    let anyChecked = false;
    group.permissions.forEach((permission: any) => {
      const permissionType = permission.name.toLowerCase();
      const checked = isChecked(permission);
      if (checked) anyChecked = true;
      permissionsMap[permissionType] = {
        id: permission.id,
        checked
      };
    });

    // If any permission in the feature is checked, mark the feature as selected
    if (anyChecked) {
      selectedFeatures.value.add(group.group);
    }

    return {
      id: group.group,
      name: group.group.split('_').map((word: string) => word.charAt(0).toUpperCase() + word.slice(1)).join(' '),
      selected: anyChecked,
      permissions: {
        view: permissionsMap.view || { id: '', checked: false },
        create: permissionsMap.create || { id: '', checked: false },
        edit: permissionsMap.edit || { id: '', checked: false },
        delete: permissionsMap.delete || { id: '', checked: false },
      }
    };
  });
});

// Computed property for filtered features based on search
const filteredFeatures = computed(() => {
  if (!featureSearch.value) return features.value;
  const searchTerm = featureSearch.value.toLowerCase();
  return features.value.filter(feature => 
    feature.name.toLowerCase().includes(searchTerm)
  );
});

// Add a computed property to check if a feature is selected
const isFeatureSelected = computed(() => (featureId: string) => {
  return selectedFeatures.value.has(featureId);
});

// Toggle all features
const areAllFeaturesSelected = computed(() => {
  return filteredFeatures.value.length > 0 && 
         filteredFeatures.value.every(feature => isFeatureSelected.value(feature.id));
});

const toggleAllFeatures = (checked: boolean) => {
  if (checked) {
    // Add all filtered feature IDs to the selected set and select permissions
    filteredFeatures.value.forEach(feature => {
      selectedFeatures.value.add(feature.id);
      
      if (isEditMode.value) {
        // In edit mode, only auto-select view permission
        permissionSelections.value.set(feature.permissions.view.id, true);
        permissionSelections.value.set(feature.permissions.create.id, false);
        permissionSelections.value.set(feature.permissions.edit.id, false);
        permissionSelections.value.set(feature.permissions.delete.id, false);
      } else {
        // In create mode, auto-select all permissions for each feature
        permissionSelections.value.set(feature.permissions.view.id, true);
        permissionSelections.value.set(feature.permissions.create.id, true);
        permissionSelections.value.set(feature.permissions.edit.id, true);
        permissionSelections.value.set(feature.permissions.delete.id, true);
      }
    });
  } else {
    // Remove all filtered feature IDs from the selected set and deselect all permissions
    filteredFeatures.value.forEach(feature => {
      selectedFeatures.value.delete(feature.id);
      // Auto-deselect all permissions for each feature
      permissionSelections.value.set(feature.permissions.view.id, false);
      permissionSelections.value.set(feature.permissions.create.id, false);
      permissionSelections.value.set(feature.permissions.edit.id, false);
      permissionSelections.value.set(feature.permissions.delete.id, false);
    });
  }
  // Create a new Set to trigger reactivity
  selectedFeatures.value = new Set(selectedFeatures.value);
};

const toggleFeatureSelection = (featureId: string, checked: boolean) => {
  if (checked) {
    selectedFeatures.value.add(featureId);
    // When feature is selected, automatically check permissions for this feature
    const feature = features.value.find(f => f.id === featureId);
    if (feature) {
      // Auto-select all permissions
      permissionSelections.value.set(feature.permissions.view.id, true);
      permissionSelections.value.set(feature.permissions.create.id, true);
      permissionSelections.value.set(feature.permissions.edit.id, true);
      permissionSelections.value.set(feature.permissions.delete.id, true);
    }
  } else {
    selectedFeatures.value.delete(featureId);
    // When feature is deselected, automatically uncheck all permissions for this feature
    const feature = features.value.find(f => f.id === featureId);
    if (feature) {
      permissionSelections.value.set(feature.permissions.view.id, false);
      permissionSelections.value.set(feature.permissions.create.id, false);
      permissionSelections.value.set(feature.permissions.edit.id, false);
      permissionSelections.value.set(feature.permissions.delete.id, false);
    }
  }
  // Create a new Set to trigger reactivity
  selectedFeatures.value = new Set(selectedFeatures.value);
};

// Get selected permissions
const getSelectedPermissions = () => {
  const selected = [];
  features.value.forEach(feature => {
    Object.values(feature.permissions).forEach(permission => {
      if (permission.checked && permission.id) {
        selected.push(permission.id);
      }
    });
  });
  return selected;
};

// Handle permission checkbox changes with auto-selection logic
const handlePermissionChange = (featureId: string, permissionType: string, checked: boolean) => {
  const feature = features.value.find(f => f.id === featureId);
  if (!feature) return;

  // Update the specific permission using permissionSelections
  const permissionId = feature.permissions[permissionType].id;
  permissionSelections.value.set(permissionId, checked);

  if (checked) {
    // If any permission is checked, auto-select the feature
    selectedFeatures.value.add(featureId);
    
    // If create, edit, or delete is checked, auto-select view permission
    if (permissionType === 'create' || permissionType === 'edit' || permissionType === 'delete') {
      const viewPermissionId = feature.permissions.view.id;
      permissionSelections.value.set(viewPermissionId, true);
    }
  } else {
    // If unchecking, check if any other permissions are still checked
    const hasAnyPermission = feature.permissions.view.checked || 
                           feature.permissions.create.checked || 
                           feature.permissions.edit.checked || 
                           feature.permissions.delete.checked;
    
    // If no permissions are checked, unselect the feature
    // if (!hasAnyPermission) {
    //   selectedFeatures.value.delete(featureId);
    // }
  }

  // Create a new Set to trigger reactivity
  selectedFeatures.value = new Set(selectedFeatures.value);
};

const fileInput = ref<HTMLInputElement | null>(null);

const uploadLogo = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files[0]) {
    company.value.logo = target.files[0];
    company.value.logoPreviewUrl = URL.createObjectURL(target.files[0]);
  }
};
const removeLogo = () => {
  company.value.logo = null;
  if (company.value.logoPreviewUrl) { URL.revokeObjectURL(company.value.logoPreviewUrl); company.value.logoPreviewUrl = null; }
};

const handleLogoUpload = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files[0]) {
    company.value.logo = target.files[0];
    company.value.logoPreviewUrl = URL.createObjectURL(target.files[0]);
  }
};

function goBack() {
  navigateTo('/companies')
}

const showPassword = ref(false);

const limitDescription = (e) => {
  if (company.value.description.length > 200) {
    company.value.description = company.value.description.slice(0, 200);
  }
};

function isNumber(evt) {
  evt = evt || window.event;
  const charCode = (evt.which) ? evt.which : evt.keyCode;
  if (charCode > 31 && (charCode < 48 || charCode > 57)) {
    evt.preventDefault();
  }
  return true;
}

async function handleSubmit() {
  
  try {
    // Get selected permission IDs
    const selectedPermissionIds = getSelectedPermissions();
    
    // Create FormData object
    const formData = new FormData();
    
    // Append company data
    formData.append('name', form.name);
    formData.append('industry_id', form.industry_id);
    formData.append('email', form.email);
    formData.append('phone', form.phone);
    formData.append('description', form.description);
    formData.append('total_working_hour', form.total_working_hour);
    formData.append('late_tolerance', form.late_tolerance);
    formData.append('map_engine', form.map_engine.toUpperCase());
    
    // For update, we don't need to send admin data
    if (!props.company) {
      // Only append admin data for new company creation
      formData.append('user_name', form.admin.name);
      formData.append('user_phone', form.admin.phone);
      formData.append('user_email', form.admin.email);
      formData.append('user_password', form.admin.password);
    }
    
    // Append logo if exists
    const logoToUpload = company.value.logo || form.logo;

    if (logoToUpload) {
      formData.append('avatar', logoToUpload);
    }
    
    // Append permissions
    selectedPermissionIds.forEach(permissionId => {
      formData.append('permissions[]', permissionId);
    });

    let response;
    if (props.company) {
      // Update existing company
      response = await $companiesStore.updateCompany(props.company.id, formData);
      toast.success('Perusahaan berhasil diperbarui');
      emit('on-success');
    } else {
      // Create new company
      response = await $companiesStore.createCompany(formData);
      toast.success('Perusahaan berhasil dibuat');
      router.push('/companies');
    }
    
    return response;
  } catch (error) {
    console.error('Error saving company:', error);
    const errorMessage = props.company 
      ? 'Gagal memperbarui perusahaan' 
      : 'Gagal membuat perusahaan';
    toast.error(errorMessage);
    throw error;
  }
};
</script>

<template>
    <p class="text-gray-500 mb-10">
     {{ props.company ? 'Ubah Data Perusahaan' : 'Tambah Data Perusahaan' }}
    </p>
    
    <div class="mt-4">
      <button
        class="flex mb-5 items-center space-x-2 px-3 py-2 text-black rounded-md"
        @click="goBack"
      >
        <svg
          id="back-arrow"
          class="-ml-4"           
          height="24"
          viewBox="0 0 24 24"
          width="24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M0 0h24v24H0V0z" fill="none" opacity=".87"></path>
          <path
            d="M16.62 2.99c-.49-.49-1.28-.49-1.77 0L6.54 11.3c-.39.39-.39 1.02 0 1.41l8.31 8.31c.49.49 1.28.49 1.77 0s.49-1.28 0-1.77L9.38 12l7.25-7.25c.48-.48.48-1.28-.01-1.76z"
          ></path>
        </svg>
        <span class="font-bold text-xl">Kembali</span>
      </button>
    </div>
    <div class="p-6">
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h2 class="text-lg font-semibold mb-4">Informasi Perusahaan <span class="text-red-500">*</span></h2>
        <div class="flex flex-col gap-4">
              <div class="flex items-center gap-4 mb-4">
                <div class="relative w-24 h-24 border-gray-300 rounded-lg overflow-hidden">
                  <template  v-if="form.avatar || company?.logoPreviewUrl">
                    <img :src="company?.logoPreviewUrl || form.avatar" class="w-full h-full object-cover" alt="Company Logo" />
                  </template>
                  <div v-else class="w-full h-full flex items-center justify-center text-gray-400">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <input 
                    type="file" 
                    ref="fileInput" 
                    @change="handleLogoUpload" 
                    accept="image/*" 
                    class="hidden"
                    id="logo-upload"
                  >
                </div>
                <div class="flex gap-2">
                <button 
                  type="button"
                  @click="$refs.fileInput.click()"
                  class="px-4 py-2 bg-red-500 text-xs text-white rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  Unggah Gambar
                </button>
                <button 
                  v-if="company.logoPreviewUrl"
                  type="button"
                  @click="removeLogo"
                  class="px-3 py-1.5 text-xs text-red-600 border-red-300 rounded-md hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                >
                  Hapus
                </button>
              </div>
              </div>
            </div>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="col-span-4 space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Nama Perusahaan <span class="text-red-500">*</span></label>
                <input 
                  v-model="form.name" 
                  type="text" 
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Masukkan nama perusahaan"
                >
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Industri <span class="text-red-500">*</span></label>
                <general-combobox
                  v-model="form.industry_id"
                  id="selectIndustry"
                  :items="industries"
                  id-trigger="triggerDropdownIndustry"
                  id-target="dropdownIndustry"
                  plain
                  placeholder="Pilih Industri"
                />
              </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Email Perusahaan</label>
                <input 
                  v-model="form.email" 
                  type="email" 
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="<EMAIL>"
                >
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">No Telepon Perusahaan</label>
                <input
                  id="phone"
                  v-model="form.phone"
                  type="tel"
                  pattern="[0-9]*"
                  inputmode="numeric"
                  class="block w-full px-3 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500"
                  placeholder="Ex : 08123456***"
                  @keypress="isNumber($event)"
                >
              </div>
            </div>
            
            <div>
              <div class="flex justify-between items-center mb-1">
                <label class="block text-sm font-medium text-gray-700">Deskripsi</label>
                <span class="text-xs text-gray-500">
                  {{ 200 - (form.description?.length || 0) }} / 200
                </span>
              </div>
              <textarea 
                v-model="form.description" 
                rows="3" 
                maxlength="200"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-5 00"
                placeholder="Masukkan deskripsi perusahaan"
                @input="limitDescription"
              ></textarea>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Total Jam Kerja</label>
                <input 
                  v-model="form.total_working_hour" 
                  type="number" 
                  min="0" 
                  max="24"
                  placeholder="Masukkan total jam kerja"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  @wheel.prevent
                >
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Toleransi Keterlambatan(%)</label>
                <input
                  v-model="form.late_tolerance"
                  type="number" 
                  min="0" 
                  max="100"
                  placeholder="Masukkan toleransi keterlambatan"
                  class="block w-full px-3 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500"
                  @wheel.prevent
                  @input="if (form.late_tolerance > 100) form.late_tolerance = 100; if (form.late_tolerance < 0) form.late_tolerance = 0"
                >
              </div>
            </div>
            
            <div class="col-span-4">
              <label class="block text-sm font-medium text-gray-700 mb-1">Map Engine <span class="text-red-500">*</span></label>
              <select 
                v-model="form.map_engine" 
                class="w-1/4 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="google">Google Maps</option>
                <option value="openstreetmap">OpenStreetMap</option>
              </select>
            </div>
            
           
          </div>
        </div>
      </div>
  
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h2 class="text-lg font-semibold mb-4">Informasi Admin Pengguna</h2>
        
        <div class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Nama <span class="text-red-500">*</span></label>
              <input 
                v-model="form.admin.name" 
                type="text"
                :readonly="props.company"
                :class="[
                  'w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2',
                  props.company 
                    ? 'bg-gray-100 border-gray-300 text-gray-500 cursor-not-allowed' 
                    : 'border-gray-300 focus:ring-blue-500'
                ]"
                placeholder="Masukkan nama admin"
              >
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Email <span class="text-red-500">*</span></label>
              <input 
                v-model="form.admin.email" 
                type="email"
                :readonly="props.company"
                :class="[
                  'w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2',
                  props.company 
                    ? 'bg-gray-100 border-gray-300 text-gray-500 cursor-not-allowed' 
                    : 'border-gray-300 focus:ring-blue-500'
                ]"
                placeholder="<EMAIL>"
              >
            </div>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">No Telepon <span class="text-red-500">*</span></label>
              <input
                id="phone"
                v-model="form.admin.phone" 
                type="tel"
                pattern="[0-9]*"
                inputmode="numeric"
                :readonly="props.company"
                :class="[
                  'w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2',
                  props.company 
                    ? 'bg-gray-100 border-gray-300 text-gray-500 cursor-not-allowed' 
                    : 'border-gray-300 focus:ring-blue-500'
                ]"
                placeholder="081234567890"
                @keypress="isNumber($event)"
              >
            </div>
            
            <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Kata Sandi <span class="text-red-500">*</span></label>
            <div class="relative">
              <input 
                v-model="form.admin.password" 
                :type="showPassword ? 'text' : 'password'" 
                :readonly="props.company"
                :class="[
                  'w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2',
                  props.company 
                    ? 'bg-gray-100 border-gray-300 text-gray-500 cursor-not-allowed' 
                    : 'border-gray-300 focus:ring-blue-500'
                ]"
                placeholder="Masukkan kata sandi"
              >
              <button 
                type="button" 
                class="absolute inset-y-0 right-0 pr-3 flex items-center"
                @click="showPassword = !showPassword"
              >
                <svg 
                  v-if="!showPassword" 
                  class="h-5 w-5 text-gray-400" 
                  fill="none" 
                  viewBox="0 0 24 24" 
                  stroke="currentColor"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                <svg 
                  v-else 
                  class="h-5 w-5 text-gray-400" 
                  fill="none" 
                  viewBox="0 0 24 24" 
                  stroke="currentColor"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                </svg>
              </button>
            </div>
          </div>
          </div>
        </div>
      </div>
  
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-semibold mb-2">Informasi Perizinan Perusahaan<span class="text-red-500">*</span></h2>
          <div class="text-sm font-semibold">
            <div class="flex items-center">
              <p>Total Fitur</p>
              (<p class="text-red-500"> {{ selectedFeatures.size }} </p>)
            </div>
          </div>
        </div>
        <div class="mb-4">
          <div class="relative">
            <input 
              v-model="featureSearch" 
              type="text" 
              class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Cari fitur..."
            >
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>
          </div>
        </div>
        
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200" aria-describedby="Tabel Daftar Fitur">
            <thead>
              <tr>
                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <div class="flex items-center">
                    <input 
                      type="checkbox" 
                      :checked="areAllFeaturesSelected"
                      @change="toggleAllFeatures($event.target.checked)"
                      class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded mr-2"
                    >
                    Fitur
                  </div>
                </th>
                <th class="px-6 py-3 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">View</th>
                <th class="px-6 py-3 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Create</th>
                <th class="px-6 py-3 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Edit</th>
                <th class="px-6 py-3 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Delete</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="feature in filteredFeatures" :key="feature.id">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  <div class="flex items-center">
                    <input 
                      type="checkbox" 
                      :checked="isFeatureSelected(feature.id)"
                      @change="toggleFeatureSelection(feature.id, $event.target.checked)"
                      class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded mr-2"
                    >
                    {{ feature.name }}
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-center">
                  <input 
                    :checked="feature.permissions.view.checked"
                    @change="handlePermissionChange(feature.id, 'view', $event.target.checked)"
                    type="checkbox" 
                    class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                  >
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-center">
                  <input 
                    :checked="feature.permissions.create.checked"
                    @change="handlePermissionChange(feature.id, 'create', $event.target.checked)"
                    type="checkbox" 
                    class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                  >
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-center">
                  <input 
                    :checked="feature.permissions.edit.checked"
                    @change="handlePermissionChange(feature.id, 'edit', $event.target.checked)"
                    type="checkbox" 
                    class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                  >
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-center">
                  <input 
                    :checked="feature.permissions.delete.checked"
                    @change="handlePermissionChange(feature.id, 'delete', $event.target.checked)"
                    type="checkbox" 
                    class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                  >
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      
      <div class="flex justify-end space-x-4">
        <button 
          @click="goBack()" 
          class="px-4 py-2 border border-red-500 text-red-500 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
        >
          Batal
        </button>
        <general-button
          :label="props.company ? 'Edit' : 'Tambah'"
          class="w-31"
          @click="handleSubmit"
          :loading="$companiesStore.loading"
          :disabled="isFormInvalid || $companiesStore.loading"
        />
      </div>
    </div>
  </template>


<style scoped>
input[type="checkbox"]:checked {
  @apply bg-red-500 border-red-500;
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

input[type="checkbox"]:indeterminate {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M4 8h8v1H4z'/%3e%3c/svg%3e");
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}
</style>