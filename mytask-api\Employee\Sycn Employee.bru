meta {
  name: Sycn Employee
  type: http
  seq: 1
}

get {
  url: {{base_url}}/employee/sync-employee
  body: multipartForm
  auth: bearer
}

auth:bearer {
  token: {{token}}
}

tests {
  // if(pm.response.code == 200) {
  //     pm.test("Status code is 200");
  // } else if(pm.response.code == 401) {
  //     pm.test("Status code is 401");
  // }
  // 
  // var dataRes = pm.response.json().data;
  // if(dataRes != null) {
  //     pm.test("Data Length is " + dataRes.length);
  //     pm.test("Data Type is " + typeof(dataRes));
  // }
}
