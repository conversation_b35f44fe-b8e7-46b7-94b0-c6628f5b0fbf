import { defineStore } from 'pinia'
import { toast } from 'vue3-toastify'
import api from '~/services/api'
import type { GeoLocation } from '~/types/server-response'

export const useGeoLocationStore = defineStore('geo-location', {
  state: () => ({
    isLoading: false as boolean,
    listLocations: [] as GeoLocation[],
    resOpenMaps: {} as GeoLocation,
  }),
  actions: {
    async searchLocation(key: string) {
      this.isLoading = true

      try {
        const res = await api.get(`/v2/maps/search-place?text=${key}, Indonesia`)

        this.listLocations = res.data
      } catch (e) {
        toast.error('Error')
        throw e
      } finally {
        this.isLoading = false
      }
    },

    async openMapsLocation(lat: number, long: number) {
      this.isLoading = true

      try {
        const res = await fetch(
          `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${long}`
        )
        this.resOpenMaps = await res.json()
      } catch (e) {
        toast.error('Error')
        throw e
      } finally {
        this.isLoading = false
      }
    },
  },
})
