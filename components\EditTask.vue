<script setup lang="ts">
import { toast } from 'vue3-toastify'
import { useRoute } from 'vue-router'
import { usePageStore } from '~/store/page'
import { useTasksStore } from '~/store/tasks'
import { watchDebounced } from '@vueuse/shared'
import { useTaskTypeStore } from '~/store/task-type'
import { useEmployeesStore } from '~/store/employees'
import { useGeoLocationStore } from '~/store/geo-transtrack'
import { encodeJson, onClickImage } from '~/utils/functions'
import type {ItemCombobox} from "~/types/element";

const $route = useRoute()
const $page = usePageStore()
const $tasks = useTasksStore()
const config = useRuntimeConfig()
const $taskType = useTaskTypeStore()
const $employee = useEmployeesStore()
const $geoLocation = useGeoLocationStore()

const props = defineProps({
  isPublic: {
    type: Boolean,
    required: true,
  },
})

const pickupSearch = ref('')
const pickupLocationCard = ref(false)
const isPickupSearchValueChangedByDebounced = ref(false)

const destinationSearch = ref('')
const indxActiveDestinationModal = ref('')
const destinationLocationCard = ref(false)
const isDestinationSearchValueChangedByDebounced = ref(false)

const detailTask = computed<null | Task>(() => $tasks.$state.detailTask)
const dropdownEmployees = computed<ItemCombobox[]>(() =>
  $employee.listEmployee.map(
    (e): ItemCombobox => ({
      text: e.name,
      value: e.id,
      detail: undefined,
    })
  )
)

const dropdownTaskType = computed<ItemCombobox[]>(() =>
  $taskType.listTaskType.map(
    (e): ItemCombobox => ({
      text: e.name,
      value: e.id,
      detail: undefined,
    })
  )
)

const dropdownSearchMaps = computed<ItemCombobox[]>(() =>
  $geoLocation.listLocations.map(
    (e): ItemCombobox => ({
      text: e.address,
      value: e.address,
      detail: e as any,
    })
  )
)

const obj = reactive({
  title: '',
  employee_id: '',
  deadline: '',
  description: '',
  no_referensi: '',
  typeTaskId: '',
  typeId: '',
  tipe_penugasan: '',
  lat_asal: '',
  long_asal: '',
  destination: [],
})

const pickup = reactive({
  nama: '',
  address: '',
})

watch(detailTask, async (newDetailTask) => {
  obj.title = newDetailTask?.title || ''
  obj.employee_id = newDetailTask?.employee_id?.id || ''
  obj.deadline = newDetailTask?.deadline || ''
  obj.tipe_penugasan = newDetailTask?.tipe_penugasan || ''
  obj.description = newDetailTask?.description || ''
  obj.no_referensi = newDetailTask?.no_referensi || ''
  obj.lat_asal = newDetailTask?.lat_asal || ''
  obj.long_asal = newDetailTask?.long_asal || ''
  obj.typeId = newDetailTask?.type?.id || ''
  pickup.nama = newDetailTask?.alamat_asal || ''
  pickup.address = newDetailTask?.alamat_asal || ''

  if (newDetailTask.childTasks.length > 0) {
    let child = []
    obj.destination = []
    newDetailTask.childTasks.forEach((el) => {
      child = {
        lat_tujuan: el.lat_tujuan,
        long_tujuan: el.long_tujuan,
        nama: el.alamat_tujuan,
        address: el.alamat_tujuan,
      }
      obj.destination.push(child)
    })
  } else {
    let child = []
    obj.destination = []
    child = {
      lat_tujuan: newDetailTask?.lat_tujuan,
      long_tujuan: newDetailTask?.long_tujuan,
      nama: newDetailTask?.alamat_tujuan,
      address: newDetailTask?.alamat_tujuan,
    }
    obj.destination.push(child)
  }
})

const updateForm = async () => {
  if (detailTask?.value.childTasks.length < 1) {
    for (const [key] of obj.destination.entries()) {
      const result = await $tasks.updateTask(
        detailTask.value.id,
        obj.employee_id,
        obj.title,
        obj.no_referensi,
        obj.description,
        null,
        null,
        key.lat_tujuan,
        key.long_tujuan,
        obj.deadline
      )
    }

    $tasks.fetchDetailTask(detailTask.value.id)
  } else {
    // PARENT TASKS
    let params = [
      {
        taskId: detailTask.value.id,
        employee_id: obj.employee_id,
        title: obj.title,
        no_referensi: obj.no_referensi,
        description: obj.description,
        lat_asal: obj.lat_asal,
        long_asal: obj.long_asal,
        lat_tujuan: null,
        long_tujuan: null,
        deadline: obj.deadline,
        task_work_type: obj.tipe_penugasan,
        type_id: obj.typeId,
      },
    ]

    obj.destination.forEach((el, index) => {
      let param = {
        taskId: detailTask?.value.childTasks[index].id,
        employee_id: obj.employee_id,
        title: obj.title,
        no_referensi: obj.no_referensi,
        description: obj.description,
        lat_asal: null,
        long_asal: null,
        lat_tujuan: el.lat_tujuan,
        long_tujuan: el.long_tujuan,
        deadline: obj.deadline,
        task_work_type: obj.tipe_penugasan,
        type_id: obj.typeId,
      }

      params.push(param)
    })

    for (const [key] of params.entries()) {
      const result = await $tasks.updateTask(
        key.taskId,
        key.employee_id,
        key.title,
        key.no_referensi,
        key.description,
        key.lat_asal,
        key.long_asal,
        key.lat_tujuan,
        key.long_tujuan,
        key.deadline,
        key.task_work_type,
        key.type_id
      )

      if (!result) {
        break
      }
    }
    $tasks.fetchDetailTask(detailTask.value.id)
  }
}

const setMaps = async () => {
  if (pickupSearch.value.trim() !== '' && pickupSearch.value !== undefined) {
    let val = pickupSearch.value as string
    let maps = dropdownSearchMaps.value.filter((item) => item.value === val)
    const temp = (maps[0]?.detail as GeoLocation) || undefined
    pickup.address = temp?.address
    obj.lat_asal = isNaN(+temp?.lat) ? 0 : +temp?.lat
    obj.long_asal = isNaN(+temp?.long) ? 0 : +temp?.long
  }
}

const setMapsDestination = async () => {
  if (
    destinationSearch.value.trim() !== '' &&
    destinationSearch.value !== undefined
  ) {
    let val = destinationSearch.value as string
    let maps = dropdownSearchMaps.value.filter((item) => item.value === val)
    const temp = (maps[0]?.detail as GeoLocation) || undefined

    obj.destination[indxActiveDestinationModal.value].lat_tujuan = temp.lat
    obj.destination[indxActiveDestinationModal.value].long_tujuan = temp.long
    obj.destination[indxActiveDestinationModal.value].address = temp.address
  }
}

watchDebounced(
  () => pickupSearch.value,
  (searchKey) => {
    if (isPickupSearchValueChangedByDebounced.value) {
      if (searchKey.trim() !== '' && searchKey !== undefined) {
        $geoLocation.searchLocation(searchKey)
      }
    }
  },
  { debounce: 200 }
)

watchDebounced(
  () => destinationSearch.value,
  (searchKey) => {
    if (isDestinationSearchValueChangedByDebounced.value) {
      if (searchKey.trim() !== '' && searchKey !== undefined) {
        $geoLocation.searchLocation(searchKey)
      }
    }
  },
  { debounce: 200 }
)

const pickupSearchLocation = (key: string) => {
  isPickupSearchValueChangedByDebounced.value = true
  pickupSearch.value = key
}

const destinationSearchLocation = (key: string) => {
  isDestinationSearchValueChangedByDebounced.value = true
  destinationSearch.value = key
}

const openModalPickup = async () => {
  dropdownSearchMaps.value = []
  dropdownSearchMaps.value.push({
    text: detailTask.value?.alamat_asal,
    value: detailTask.value?.alamat_asal,
    detail: {
      address: detailTask.value?.alamat_asal,
      lat: detailTask.value?.lat_asal,
      long: detailTask.value?.long_asal,
      name: detailTask.value?.nama_lokasi_asal,
    },
  })
  isPickupSearchValueChangedByDebounced.value = false
  pickupSearch.value = detailTask.value?.alamat_asal
  pickupLocationCard.value = !pickupLocationCard.value
}

const openModalDestination = async (index: string) => {
  indxActiveDestinationModal.value = index
  dropdownSearchMaps.value = []
  dropdownSearchMaps.value.push({
    text: detailTask.value?.childTasks[index].alamat_tujuan,
    value: detailTask.value?.childTasks[index].alamat_tujuan,
    detail: {
      address: detailTask.value?.childTasks[index].alamat_tujuan,
      lat: detailTask.value?.childTasks[index].lat_tujuan,
      long: detailTask.value?.childTasks[index].long_tujuan,
      name: detailTask.value?.childTasks[index].destination_name,
    },
  })

  isDestinationSearchValueChangedByDebounced.value = false
  destinationSearch.value = detailTask.value?.childTasks[index].alamat_tujuan
  destinationLocationCard.value = !destinationLocationCard.value
}

const onMarkerDragEnd = async (type: string, event: any) => {
  await $geoLocation.openMapsLocation(
    event.target.getLatLng().lat,
    event.target.getLatLng().lng
  )

  if (type === 'origin') {
    dropdownSearchMaps.value = []
    dropdownSearchMaps.value.push({
      text: $geoLocation?.resOpenMaps?.display_name,
      value: $geoLocation?.resOpenMaps?.display_name,
      detail: {
        address: $geoLocation?.resOpenMaps?.display_name,
        lat: $geoLocation?.resOpenMaps?.lat,
        long: $geoLocation?.resOpenMaps?.lon,
        name: $geoLocation?.resOpenMaps?.name,
      },
    })

    isPickupSearchValueChangedByDebounced.value = false
    pickupSearch.value = $geoLocation?.resOpenMaps?.display_name
    setMaps()
  } else if (type === 'destination') {
    dropdownSearchMaps.value = []
    dropdownSearchMaps.value.push({
      text: $geoLocation?.resOpenMaps?.display_name,
      value: $geoLocation?.resOpenMaps?.display_name,
      detail: {
        address: $geoLocation?.resOpenMaps?.display_name,
        lat: $geoLocation?.resOpenMaps?.lat,
        long: $geoLocation?.resOpenMaps?.lon,
        name: $geoLocation?.resOpenMaps?.name,
      },
    })

    isDestinationSearchValueChangedByDebounced.value = false
    destinationSearch.value = $geoLocation?.resOpenMaps?.display_name
    setMapsDestination()
  }
}

async function encrypt(
  payload: any,
  secretKey: string
): Promise<{ iv: string; base64EncryptedData: string }> {
  const alg = {
    name: 'AES-GCM',
    length: 256,
  }

  const base64Decode = (str: string) =>
    Uint8Array.from(atob(str), (c) => c.charCodeAt(0))

  const iv = base64Decode(secretKey)
  const key = await crypto.subtle.importKey(
    'raw',
    new TextEncoder().encode(secretKey),
    alg,
    false,
    ['encrypt']
  )

  const encodedPayload = new TextEncoder().encode(JSON.stringify(payload))

  const encryptedData = await crypto.subtle.encrypt(
    {
      name: alg.name,
      iv: iv,
    },
    key,
    encodedPayload
  )

  const base64EncryptedData = btoa(
    String.fromCharCode(...new Uint8Array(encryptedData))
  )

  const base64IV = btoa(String.fromCharCode(...iv))

  return {
    iv: base64IV,
    base64EncryptedData: base64EncryptedData,
  }
}

async function onCopyLink() {
  let currentLink = window.location.origin + $route.fullPath

  if (!props.isPublic) {
    const secretKey = config.public.secretKey

    const { token } = useAuth()
    const payload = {
      fullPath: $route.fullPath,
      accessToken: token.value,
      taskId: props.id,
    }

    const { base64EncryptedData } = await encrypt(payload, secretKey)

    currentLink =
      window.location.origin +
      '/t' +
      `?d=${encodeURIComponent(base64EncryptedData)}`

    navigator.clipboard.writeText(currentLink)
  } else {
    navigator.clipboard.writeText(currentLink)
  }

  toast.success('URL Disalin')
}

const onAddTaskType = async (res: string) => {
  let hasSpace = hasWhiteSpace(res)
  let name = res
  let type_identity = res
  let description = ''

  if (hasSpace) {
    type_identity = removeSpaces(res)
  }

  const result = await $taskType.createTaskType(
    type_identity,
    name,
    description
  )

  obj.typeId = result.id
  $taskType.fetchListTaskType({})
}

onMounted(() => {
  obj.destination = []
  $taskType.fetchListTaskType({})
  $employee.fetchListEmployee({})
  $page.setTitle('Ubah Tugas')
})
</script>

<template>
  <div>
    <p class="text-gray-500 -mt-14 mb-10">
      Lengkapi data berikut Untuk membuat tugas baru
    </p>
    <div class="flex justify-between mt-4">
      <button
        class="flex mb-5 items-center space-x-2 px-3 py-2 text-black rounded-md"
      >
        <svg
          class="-ml-4"
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          id="back-arrow"
        >
          <path fill="none" d="M0 0h24v24H0V0z" opacity=".87"></path>
          <path
            d="M16.62 2.99c-.49-.49-1.28-.49-1.77 0L6.54 11.3c-.39.39-.39 1.02 0 1.41l8.31 8.31c.49.49 1.28.49 1.77 0s.49-1.28 0-1.77L9.38 12l7.25-7.25c.48-.48.48-1.28-.01-1.76z"
          ></path>
        </svg>
        <span class="font-bold text-xl">Kembali</span>
      </button>

      <button
        @click="onCopyLink"
        class="flex mb-5 items-center space-x-2 px-3 py-2 text-black rounded-md"
      >
        <svg
          fill="none"
          height="24"
          viewBox="0 0 24 24"
          width="24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12.7651 15.8261L9.70425 18.887C9.70425 18.887 9.70425 18.8871 9.70418 18.8871C9.70418 18.8871 9.70418 18.8872 9.70411 18.8872C8.43835 20.153 6.37868 20.1531 5.11277 18.8872C4.49952 18.2739 4.16187 17.4586 4.16187 16.5915C4.16187 15.7244 4.49952 14.9092 5.11256 14.2959C5.11263 14.2959 5.1127 14.2958 5.11277 14.2957L8.17364 11.2348C8.59621 10.8121 8.59621 10.1269 8.17357 9.7043C7.751 9.28173 7.06574 9.28173 6.6431 9.7043L3.58223 12.7652C3.58202 12.7655 3.5818 12.7657 3.58158 12.766C2.56004 13.7879 1.99744 15.1465 1.99744 16.5915C1.99744 18.0368 2.56026 19.3956 3.5823 20.4177C4.63725 21.4725 6.02285 22 7.40851 22C8.79418 22 10.1799 21.4725 11.2347 20.4177C11.2347 20.4177 11.2347 20.4175 11.2347 20.4175L14.2956 17.3566C14.7182 16.934 14.7182 16.2487 14.2955 15.8261C13.873 15.4035 13.1878 15.4035 12.7651 15.8261Z"
            fill="black"
          />
          <path
            d="M22.0024 7.40856C22.0024 5.96323 21.4395 4.6044 20.4175 3.58235C18.3077 1.47261 14.8748 1.47268 12.7651 3.58235C12.7651 3.5825 12.7649 3.58257 12.7649 3.58272L9.70407 6.64344C9.28142 7.06601 9.28142 7.75134 9.70407 8.17391C9.91546 8.3853 10.1924 8.49092 10.4693 8.49092C10.7462 8.49092 11.0233 8.38523 11.2345 8.17391L14.2953 5.11318C14.2954 5.11304 14.2955 5.11297 14.2957 5.11282C15.5614 3.84706 17.6211 3.84699 18.887 5.11282C19.5002 5.72608 19.838 6.54142 19.838 7.40856C19.838 8.27563 19.5004 9.09083 18.8872 9.70409L18.887 9.7043L15.8262 12.7652C15.4036 13.1878 15.4036 13.8731 15.8262 14.2957C16.0376 14.507 16.3145 14.6127 16.5914 14.6127C16.8684 14.6127 17.1454 14.507 17.3567 14.2957L20.4176 11.2348C20.4178 11.2346 20.418 11.2343 20.4182 11.2341C21.4398 10.2122 22.0024 8.85354 22.0024 7.40856Z"
            fill="black"
          />
          <path
            d="M8.17379 15.8261C8.38511 16.0374 8.66209 16.1431 8.93899 16.1431C9.21597 16.1431 9.49294 16.0374 9.70426 15.8261L15.8261 9.70423C16.2488 9.28166 16.2488 8.5964 15.8261 8.17376C15.4036 7.75119 14.7183 7.75119 14.2957 8.17376L8.17379 14.2956C7.75115 14.7183 7.75115 15.4035 8.17379 15.8261Z"
            fill="black"
          />
        </svg>
        <span class="font-bold text-xl">Salin Link</span>
      </button>
    </div>
    <div
      class="bg-white mb-6 border px-10 pt-12 rounded-lg shadow-sm overflow-hidden"
    >
      <div class="grid gap-3 grid-cols-2 mb-6">
        <general-combobox
          v-model="obj.typeId"
          :items="dropdownTaskType"
          has-search-field
          id-target="dropdownTaskTypeEdit"
          id-trigger="triggerDropdownTaskTypeEdit"
          is-inline-add
          label="Tipe Penugasan"
          placeholder="Pilih Tipe Tugas"
          required
          @on-click-add="onAddTaskType"
        />

        <general-text-input
          id="title"
          label="Judul Penugasan"
          placeholder="Ex: Pengiriman Device"
          v-model="obj.title"
        />
      </div>
      <div class="grid gap-3 grid-cols-2 mb-6">
        <general-combobox
          v-model="obj.employee_id"
          :items="dropdownEmployees"
          has-search-field
          id-target="dropdownEmployeeEdit"
          id-trigger="triggerDropdownEmployeeEdit"
          is-inline-add
          label="Karyawan"
          placeholder="Pilih Karyawan"
          required
        />

        <general-datepicker
          id="editTaskemployes"
          v-model="obj.deadline"
          :min-date="new Date()"
          format="yyyy-MM-dd H:mm:ss"
          label="Tenggat Waktu"
          placeholder="Pilih Waktu Pengambilan"
          required
        />
      </div>
      <div
        class="relative h-[300px] w-full bg-gray-100 rounded-lg mb-4 overflow-hidden border mb-6"
      >
        <div class="absolute top-0 bottom-0 left-0 right-0 z-10">
          <general-leaflet-map
            :center="[Number(obj.lat_asal), Number(obj.long_asal)]"
            :zoom="12"
            is-clickable
          >
            <template #marker>
              <l-marker
                :draggable="true"
                :lat-lng="[Number(obj.lat_asal), Number(obj.long_asal)]"
              >
                <l-icon :icon-anchor="[14, 28]" icon-size="28">
                  <icon-marker class="stroke-error-500" size="28" />
                </l-icon>
              </l-marker>

              <l-marker
                v-for="(to, i) in obj.destination"
                :key="i"
                :draggable="true"
                :lat-lng="[Number(to.lat_tujuan), Number(to.long_tujuan)]"
              >
                <l-icon :icon-anchor="[14, 28]" icon-size="28">
                  <icon-marker
                    class="stroke-error-500"
                    color="#3498DB"
                    size="28"
                  />
                </l-icon>
              </l-marker>
            </template>
          </general-leaflet-map>
        </div>
      </div>

      <div class="w-full gap-3 mb-6">
        <div
          v-if="detailTask?.childTasks?.length > 0"
          class="flex flex-col items-start w-full mb-4"
        >
          <div class="flex justify-between w-full">
            <div>
              <label
                for="pickup-position"
                class="mb-1.5 text-sm font-[600] text-gray-700"
              >
                Nama Lokasi Awal
              </label>
            </div>
            <div>
              <label
                class="mb-1.5 text-sm font-[600] text-gray-700 flex items-center space-x-2"
                for="pickup-position"
              >
                <span @click="openModalPickup">
                  <icon-edit
                    class="mr-4"
                    color="#F36A6A"
                    size="16"
                    style="display: inline-block; vertical-align: middle"
                  />
                </span>
              </label>
            </div>
          </div>

          <div
            class="grid gap-6 md:grid-cols-2 p-2 border rounded-lg mb-4 bg-gray-100 w-full"
          >
            <div class="col-span-2 md:col-span-1">
              <div class="flex flex-col bg-gray-100 p-4 rounded-lg mb-4 w-full">
                <div>
                  <p class="text-lg font-semibold text-gray-700">
                    Alamat Lokasi Awal
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    {{ pickup.nama }}
                  </p>
                </div>
              </div>
            </div>
            <div class="col-span-2 md:col-span-1">
              <div class="flex flex-col bg-gray-100 p-4 rounded-lg mb-4">
                <div>
                  <p class="text-lg font-semibold text-gray-700">
                    Alamat Pengambilan
                  </p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">
                    {{ pickup.address }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div
          v-for="(childLocation, i) in obj?.destination"
          :key="i"
          class="flex flex-col items-start w-full"
        >
          <div class="w-full">
            <div class="flex justify-between">
              <label
                class="mb-1.5 text-sm font-[600] text-gray-700"
                for="pickup-position"
              >
                Lokasi Tujuan {{ i + 1 }}
              </label>
              <label
                class="mb-1.5 text-sm font-[600] text-gray-700 flex items-center space-x-2"
                for="pickup-position"
              >
                <span @click="openModalDestination(i)">
                  <icon-edit
                    class="mr-4"
                    color="#F36A6A"
                    size="16"
                    style="display: inline-block; vertical-align: middle"
                  />
                </span>
              </label>
            </div>

            <div
              class="grid gap-12 md:grid-cols-2 p-2 border rounded-lg mb-4 bg-gray-100 w-full"
            >
              <div class="col-span-2 md:col-span-1 w-full">
                <div
                  class="flex flex-col bg-gray-100 p-4 rounded-lg mb-4 w-full"
                >
                  <div>
                    <p class="text-lg font-semibold text-gray-700">
                      Nama Lokasi Tujuan
                    </p>
                  </div>
                  <div>
                    <p class="text-sm text-gray-500">
                      {{ childLocation?.nama }}
                    </p>
                  </div>
                </div>
              </div>
              <div class="col-span-2 md:col-span-1 w-full">
                <div
                  class="flex flex-col bg-gray-100 p-4 rounded-lg mb-4 w-full"
                >
                  <div>
                    <p class="text-lg font-semibold text-gray-700">
                      Alamat Lokasi Tujuan
                    </p>
                  </div>
                  <div>
                    <p class="text-sm text-gray-500">
                      {{ childLocation?.address }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="grid gap-3 grid-cols-2 mb-6">
        <general-text-input
          id="note"
          label="Deskripsi Penugasan"
          placeholder="Ex:Pengiriman"
          v-model="obj.description"
        />

        <general-text-input
          id="referenceNumber"
          label="No Referensi"
          placeholder="Masukan No Referensi"
          v-model="obj.no_referensi"
        />
      </div>

      <label
        for="pickup-position"
        class="mb-3 text-sm font-[600] text-gray-700"
      >
        Tipe Pengerjaan Tugas
      </label>
      <div class="flex mt-5">
        <div class="flex items-center me-4">
          <general-radio
            required
            v-model="obj.tipe_penugasan"
            name="worktype"
            id="workTypeTersusun"
            label="Tersusun"
            class="mb-8"
            value="TERSUSUN"
            :checked="obj.tipe_penugasan == 'TERSUSUN' ? true : false"
          />
        </div>
        <div class="flex items-center me-4">
          <general-radio
            v-model="obj.tipe_penugasan"
            required
            name="worktype"
            id="workTypeBebas"
            label="Bebas"
            class="mb-8"
            value="BEBAS"
            :checked="obj.tipe_penugasan == 'BEBAS' ? true : false"
          />
        </div>
      </div>
    </div>

    <div v-if="detailTask?.childTasks?.length > 0">
      <label
        for="lampiran-penugasan"
        class="mb-5 text-lg font-[600] text-gray-700"
      >
        Lampiran Penugasan
      </label>
      <div
        v-for="(childLocation, i) in detailTask?.childTasks"
        :key="i"
        class="bg-white mb-6 border px-10 pt-12 mt-5 rounded-lg shadow-sm overflow-hidden"
      >
        <label
          for="title-location"
          class="mb-5 text-lg font-[600] text-gray-700"
        >
          Lokasi {{ i + 1 }}
        </label>
        <div class="grid gap-3 grid-cols-2 mb-6 mt-3">
          <label for="tenggat" class="mb-0 text-sm font-[600] text-gray-700">
            Tenggat Upload Tugas
          </label>

          <label
            for="pickup-position"
            class="mb-0 text-sm font-[600] text-gray-700"
          >
            Waktu Upload Tugas
          </label>

          <label
            for="pickup-position"
            class="mb-5 text-sm font-[400] text-gray-700"
          >
            {{ formatDate(childLocation.deadline) }}
          </label>

          <label
            for="pickup-position"
            class="mb-5 text-sm font-[400] text-gray-700"
          >
            {{ formatDatetoTime(childLocation.created_at) }}
          </label>
          <label
            for="pickup-position"
            class="mb-0 text-sm font-[600] text-gray-700"
          >
            Tanda Tangan
          </label>
          <label
            for="pickup-position"
            class="mb-0 text-sm font-[600] text-gray-700"
          >
            E-POD
          </label>

          <div class="p-2 w-full h-64">
            <div v-if="childLocation.sign" class="text-center">
              <nuxt-img
                :src="childLocation.sign"
                alt="Image"
                class="border border-gray-300 p-10 w-full h-full object-cover"
              />
            </div>

            <div v-else class="text-center">
              <empty-image />
            </div>
          </div>

          <div
            :class="
              childLocation.foto_task
                ? 'w-full flex flex-wrap border p-2'
                : 'w-full h-64 text-center p-2'
            "
          >
            <nuxt-img
              v-for="json in encodeJson(detailTask.foto_task)"
              v-if="detailTask.foto_task"
              :src="json"
              alt="Image"
              class="border border-gray-300 p-10 w-full h-full object-cover"
              @click="
                onClickImage(
                  `https://api-mytask-staging.transtrack.id/task/url-photo-generator?url=${json}`
                )
              "
            />
            <empty-image v-else />
          </div>
        </div>
      </div>
    </div>

    <div v-if="detailTask?.childTasks?.length < 1">
      <label
        for="lampiran-penugasan"
        class="mb-5 text-lg font-[600] text-gray-700"
      >
        Lampiran Penugasan
      </label>
      <div
        class="bg-white mb-6 border px-10 pt-12 mt-5 rounded-lg shadow-sm overflow-hidden"
      >
        <label
          for="title-location"
          class="mb-5 text-lg font-[600] text-gray-700"
        >
          Lokasi 1
        </label>
        <div class="grid gap-3 grid-cols-2 mb-6 mt-3">
          <label for="tenggat" class="mb-0 text-sm font-[600] text-gray-700">
            Tenggat Upload Tugas
          </label>

          <label
            for="pickup-position"
            class="mb-0 text-sm font-[600] text-gray-700"
          >
            Waktu Upload Tugas
          </label>

          <label
            for="pickup-position"
            class="mb-5 text-sm font-[400] text-gray-700"
          >
            {{ formatDate(detailTask?.deadline) }}
          </label>

          <label
            for="pickup-position"
            class="mb-5 text-sm font-[400] text-gray-700"
          >
            {{ formatDatetoTime(detailTask?.created_at) }}
          </label>
          <label
            for="pickup-position"
            class="mb-0 text-sm font-[600] text-gray-700"
          >
            Tanda Tangan
          </label>
          <label
            for="pickup-position"
            class="mb-0 text-sm font-[600] text-gray-700"
          >
            E-POD
          </label>

          <div class="p-2 w-full flex flex-wrap h-64">
            <nuxt-img
              v-if="detailTask.sign"
              :src="detailTask?.sign"
              alt="Image"
              class="p-10 w-full h-64 object-contain"
              @click="
                onClickImage(
                  `https://api-mytask-staging.transtrack.id/task/url-photo-generator?url=${childLocation.sign}`
                )
              "
            />
            <empty-image v-else />
          </div>

          <div class="p-2 w-full flex flex-wrap h-64">
            <nuxt-img
              v-for="json in encodeJson(detailTask.foto_task)"
              v-if="detailTask.foto_task"
              :src="json"
              alt="Image"
              class="p-10 h-64 object-contain cursor-pointer"
              @click="
                onClickImage(
                  `https://api-mytask-staging.transtrack.id/task/url-photo-generator?url=${json}`
                )
              "
            />
            <empty-image v-else />
          </div>
        </div>
      </div>
    </div>

    <div class="flex w-2/3 space-x-3">
      <general-button
        type="Ubah Tugas"
        :label="'Update'"
        class="w-52"
        @click="updateForm"
      />
    </div>
    <!-- END DIV -->
  </div>

  <transition name="fade">
    <div
      v-if="pickupLocationCard"
      class="fixed top-0 left-0 w-full h-full flex items-center justify-center z-50"
    >
      <div
        class="bg-black bg-opacity-50 absolute top-0 left-0 w-full h-full"
      ></div>
      <div class="p-4 bg-white rounded-lg shadow-lg relative w-[500px] h-[570]">
        <div class="p-6">
          <h2 class="text-xl -ml-5 font-bold mb-2">Lokasi Awal</h2>
          <p class="text-gray-400 -ml-5 text-sm">
            Pastikan Lokasi yang di inputkan sudah sesuai
          </p>
        </div>
        <div
          class="relative h-[230px] w-full bg-gray-100 rounded-lg mb-4 overflow-hidden border mb-6"
        >
          <div class="absolute top-0 bottom-0 left-0 right-0 z-10">
            <general-leaflet-map
              :center="[Number(obj.lat_asal), Number(obj.long_asal)]"
              :zoom="14"
              is-clickable
            >
              <template #marker>
                <l-marker
                  :draggable="true"
                  :lat-lng="[Number(obj.lat_asal), Number(obj.long_asal)]"
                  @dragend="onMarkerDragEnd('origin', $event)"
                >
                  <l-icon :icon-anchor="[14, 28]" icon-size="28">
                    <icon-marker class="stroke-error-500" size="28" />
                  </l-icon>
                </l-marker>
              </template>
            </general-leaflet-map>
          </div>
        </div>
        <general-text-input
          v-model="pickup.nama"
          id="nameLocation"
          class="mt-5"
          label="Nama Tempat Awal Pickup"
          placeholder="Ex: Transtrack Bandung"
          required
        />

        <general-combobox
          v-model="pickupSearch"
          :is-inline-add="false"
          class="mt-2"
          has-search-field
          id-target="dropdownTujuan"
          id-trigger="triggerDropdownTujuan"
          label="Alamat Lengkap"
          placeholder="Ex: Alamat Tujuan"
          plain=""
          required
          :items="dropdownSearchMaps"
          :update:model-value="pickupSearchLocation"
          @on-input-search="pickupSearchLocation"
          @update:model-value="setMaps()"
        />
        <div class="flex w-2/3 space-x-3 mt-8">
          <button
            :disabled="!pickup.nama || !pickupSearch"
            @click="
              () => {
                toast.success('berhasil tambah data lokasi tujuan')
                pickupLocationCard = false
              }
            "
            class="border border-red-500 text-white bg-red-500 px-4 py-2 rounded-md hover:bg-red-600 focus:outline-none focus:bg-red-600 transition duration-300"
          >
            Simpan
          </button>
          <button
            id="hideLokasiAsal"
            class="border border-red-500 text-red-500 px-4 py-2 rounded-md hover:bg-red-500 hover:text-white focus:outline-none focus:bg-red-500 focus:text-white transition duration-300"
            @click="pickupLocationCard = false"
          >
            Batal
          </button>
        </div>
      </div>
    </div>
  </transition>

  <transition name="fade">
    <div
      v-if="destinationLocationCard"
      class="fixed top-0 left-0 w-full h-full flex items-center justify-center z-50"
    >
      <div
        class="bg-black bg-opacity-50 absolute top-0 left-0 w-full h-full"
      ></div>
      <div class="p-4 bg-white rounded-lg shadow-lg relative w-[500px] h-[570]">
        <div class="p-6">
          <h2 class="text-xl -ml-5 font-bold mb-2">Lokasi Tujuan</h2>
          <p class="text-gray-400 -ml-5 text-sm">
            Pastikan Lokasi yang di inputkan sudah sesuai
          </p>
        </div>
        <div
          class="relative h-[230px] w-full bg-gray-100 rounded-lg mb-4 overflow-hidden border mb-6"
        >
          <div class="absolute top-0 bottom-0 left-0 right-0 z-10">
            <general-leaflet-map
              :center="[
                Number(obj.destination[indxActiveDestinationModal].lat_tujuan),
                Number(obj.destination[indxActiveDestinationModal].long_tujuan),
              ]"
              :zoom="14"
              is-clickable
            >
              <template #marker>
                <l-marker
                  :draggable="true"
                  :lat-lng="[
                    Number(
                      obj.destination[indxActiveDestinationModal].lat_tujuan
                    ),
                    Number(
                      obj.destination[indxActiveDestinationModal].long_tujuan
                    ),
                  ]"
                  @dragend="onMarkerDragEnd('destination', $event)"
                >
                  <l-icon :icon-anchor="[14, 28]" icon-size="28">
                    <icon-marker class="stroke-error-500" size="28" />
                  </l-icon>
                </l-marker>
              </template>
            </general-leaflet-map>
          </div>
        </div>
        <general-text-input
          v-model="obj.destination[indxActiveDestinationModal].nama"
          id="nameLocation"
          class="mt-5"
          label="Nama Tempat Awal Pickup"
          placeholder="Ex: Transtrack Bandung"
          required
        />

        <general-combobox
          v-model="destinationSearch"
          :is-inline-add="false"
          class="mt-2"
          has-search-field
          id-target="dropdownTujuan"
          id-trigger="triggerDropdownTujuan"
          label="Alamat Lengkap"
          placeholder="Ex: Alamat Tujuan"
          plain=""
          required
          :items="dropdownSearchMaps"
          :update:model-value="destinationSearchLocation"
          @on-input-search="destinationSearchLocation"
          @update:model-value="setMapsDestination()"
        />
        <div class="flex w-2/3 space-x-3 mt-8">
          <!-- :disabled="!pickup.nama || !pickupSearch" -->
          <button
            @click="
              () => {
                toast.success('berhasil tambah data lokasi tujuan')
                destinationLocationCard = false
              }
            "
            class="border border-red-500 text-white bg-red-500 px-4 py-2 rounded-md hover:bg-red-600 focus:outline-none focus:bg-red-600 transition duration-300"
          >
            Simpan
          </button>
          <button
            id="hideLokasiAsal"
            class="border border-red-500 text-red-500 px-4 py-2 rounded-md hover:bg-red-500 hover:text-white focus:outline-none focus:bg-red-500 focus:text-white transition duration-300"
            @click="destinationLocationCard = false"
          >
            Batal
          </button>
        </div>
      </div>
    </div>
  </transition>
</template>

