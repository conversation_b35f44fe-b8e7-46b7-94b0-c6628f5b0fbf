<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default: '24'
  }
})
</script>

<template>
  <svg xmlns="http://www.w3.org/2000/svg" :width="props.size" :height="props.size" viewBox="0 0 11 12" fill="none">
    <g clip-path="url(#clip0_8924_133142)">
      <path d="M5.50033 10.5834C8.03163 10.5834 10.0837 8.53139 10.0837 6.00008C10.0837 3.46878 8.03163 1.41675 5.50033 1.41675C2.96902 1.41675 0.916992 3.46878 0.916992 6.00008C0.916992 8.53139 2.96902 10.5834 5.50033 10.5834Z" stroke-linecap="round" stroke-linejoin="round"/>
    </g>
    <defs>
      <clipPath id="clip0_8924_133142">
        <rect width="11" height="11" fill="white" transform="translate(0 0.5)"/>
      </clipPath>
    </defs>
  </svg>
</template>

<style scoped>

</style>