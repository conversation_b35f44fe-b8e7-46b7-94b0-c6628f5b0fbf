<script setup lang="ts">
import { useCombobox } from '~/composables/combobox'
import type { ElementEvent, ItemCombobox } from '~/types/element'
import Button from '~/components/general/Button.vue'
import { isSameString } from '~/utils/functions'

const props = defineProps({
  modelValue: {
    type: [String, Number, Object as () => null, Array as () => string[]],
    default: null,
  },
  text: {
    type: String || null,
    default: null,
  },
  idTrigger: {
    type: String,
    default: '',
  },
  idTarget: {
    type: String,
    default: '',
  },
  required: {
    type: Boolean,
    default: false,
  },
  plain: {
    type: Boolean,
    default: false,
  },
  isInlineAdd: {
    type: Boolean,
    default: false,
  },
  isSearchable: {
    type: Boolean,
    default: true,
  },
  isMultiSelect: {
    type: Boolean,
    default: false,
  },
  isDropdownOnTop: {
    type: Boolean,
    default: false,
  },
  addButtonLabel: {
    type: String,
    default: 'Add',
  },
  label: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '',
  },
  searchPlaceholder: {
    type: String,
    default: 'Search',
  },
  closeOnSelect: {
    type: Boolean,
    default: true,
  },
  items: {
    type: Array as () => ItemCombobox[],
    default: [],
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  clearable: {
    type: Boolean,
    default: false,
  },
  isHasCreate: {
    type: Boolean,
    default: true,
  },
  isName: {
    type: Boolean,
    default: false,
  }
})

const emit = defineEmits([
  'mounted',
  'on-show',
  'on-hide',
  'on-change',
  'on-click-clear',
  'on-input-search',
  'on-click-add',
  'update:modelValue',
])

const searchKey = ref('')
const isHover = ref<boolean>(false)
const isVisible = ref<boolean>(false)
const dropdown = ref(null)

let $combobox: HTMLElement | null = null

const setCombobox: ElementEvent = {
  show: () => toggleCombobox(true),
  hide: () => toggleCombobox(false),
  toggle: () => toggleCombobox(!isVisible.value),
}

onMounted(() => {
  $combobox = document.getElementById(props.idTarget)
  emit('mounted', setCombobox)
})

watch(
  () => isVisible.value,
  (isVisible: boolean) => {
    if (isVisible) {
      emit('on-show')
    } else {
      searchKey.value = ''
      emit('on-hide')
    }
  }
)

function onClickRemoveItem(value: string) {
  emit('update:modelValue', (props.modelValue as string[]).filter(v => v !== value))
}

const filteredItems = computed(() => {
  return props.items.filter((item) => isSameString(item.text, searchKey.value))
})

const selectedItem = computed((): string | string[] => {
  // if (props.isMultiSelect) {
  //   return ''
  // }
  return (
    props.items?.find((item) => item.value === props.modelValue)?.text ??
    props.text
  )
})

function toggleCombobox(value: boolean) {
  if (value) {
    $combobox?.classList.remove('hidden')
    nextTick(() => {
      adjustDropdownPosition()
    })
  } else {
    $combobox?.classList.add('hidden')
  }

  isVisible.value = value
  useCombobox().value = !useCombobox().value
}

function adjustDropdownPosition() {
  if (!dropdown.value || !$combobox) return

  const comboboxRect = $combobox.getBoundingClientRect()
  const dropdownRect = dropdown.value.getBoundingClientRect()
  const spaceBelow =
    window.innerHeight - comboboxRect.bottom + $combobox.offsetTop
  const spaceAbove = comboboxRect.top - $combobox.offsetTop

  if (
    (spaceBelow < dropdownRect.height && spaceAbove > dropdownRect.height) ||
    props.isDropdownOnTop
  ) {
    dropdown.value.style.top = 'auto'
    dropdown.value.style.bottom = '100%'
  } else {
    dropdown.value.style.top = '100%'
    dropdown.value.style.bottom = 'auto'
  }
}

function onFocusOut() {
  if (!isHover.value) {
    toggleCombobox(false)
  }
}

function onSelectItem(event: Event, item: Item) {
  if (!props.closeOnSelect) {
    event.preventDefault()
  } else {
    toggleCombobox(false)
  }

  if (props.isMultiSelect) {
    if (props.modelValue?.includes(item.value)) {
      emit(
        'update:modelValue',
        props.modelValue?.filter((value: string) => value !== item.value)
      )
    } else {
      emit('update:modelValue', [...props.modelValue, item.value])
    }
  } else {
    emit('update:modelValue', item.value)
  }
}

function onClickAddButton() {
  if (props.isMultiSelect) {
    emit('on-input-search', '')
    searchKey.value = ''
  } else {
    emit('on-click-add')
  }
}

/**
 * Handles the main input event.
 *
 * @param {string} event - The input event.
 */
function handleMainInput(event: string) {
  if (event.length == 0) {
    if (typeof props.modelValue == 'string') {
      emit('update:modelValue', '')
    } else if (typeof props.modelValue == 'number') {
      emit('update:modelValue', 0)
    }
  }
  emit('on-input-search', event)
  searchKey.value = event
}

watch(
  () => props.modelValue,
  (value) => {
    if (value === '' || !value) searchKey.value = ''
  }
)
</script>

<template>
  <div class="relative overflow-visible" @focusout="onFocusOut()">
    <div class="relative">
      <general-text-input
        :clearable="props.clearable"
        :disabled="props.disabled"
        :required="props.required"
        :label="props.label"
        :id="props.idTrigger"
        :model-value="props.isName
          ? (searchKey ? searchKey : selectedItem) 
          : (searchKey ? searchKey : (selectedItem ? selectedItem : props.modelValue))"
        :placeholder="props.placeholder"
        @focus.capture="toggleCombobox(true)"
        @update:model-value="handleMainInput"
        @on-click-clear="$emit('on-click-clear')"
      >
      <template #prefix-above>
          <div v-if="props.isMultiSelect" class="pa-2 pb-2 gap-2 flex flex-wrap">
            <general-chip
              v-for="value in (props.modelValue as string[])"
              :label="props.items.find(item => item.value === value)?.text"
              class="!text-sm border border-primary-500"
            >
              <template #suffix>
                <div class="p-0.5 rounded-full hover:bg-primary-200 transition-all cursor-pointer" @click="onClickRemoveItem(value)">
                  <icon-close size="16" class="stroke-primary-500"/>
                </div>
              </template>
            </general-chip>
          </div>
        </template>

        <template #suffix>
          <div>
            <slot name="suffix" />
          </div>

          <icon-chevron-up
            v-if="!$slots.suffix"
            size="20"
            class="cursor-pointer transition-transform"
            :class="
              isVisible
                ? 'rotate-0 stroke-primary-500'
                : 'rotate-180 stroke-gray-500'
            "
            @click="toggleCombobox(!isVisible)"
          />
        </template>
      </general-text-input>
    </div>

    <div
      ref="dropdown"
      :id="props.idTarget"
      class="absolute left-0 right-0 top-[100%] z-40 transition hidden"
      @mouseover="isHover = true"
      @mouseleave="isHover = false"
    >
      <div class="bg-white mx-1 rounded-lg border shadow-elevated">
        <div v-if="!$props.isInlineAdd && !props.plain" class="shadow-sm">
          <button
            type="button"
            class="flex items-center justify-start w-full py-2.5 px-3.5 text-sm font-[600] text-primary-500 transition hover:bg-[#FDEBEB]"
            @mousedown.prevent="onClickAddButton()"
          >
            <icon-plus size="20" class="mr-3 stroke-primary-500" />
            <span>{{ props.addButtonLabel }}</span>
          </button>
        </div>

        <div v-if="props.isLoading" class="py-2.5 flex justify-center">
          <svg
            aria-hidden="true"
            class="w-6 h-6 animate-spin text-primary-300 fill-white"
            viewBox="0 0 100 101"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
              fill="currentColor"
            />
            <path
              d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
              fill="currentFill"
            />
          </svg>
        </div>

        <div v-else>
          <ul
            v-if="(props.isSearchable ? filteredItems : props.items).length > 0"
            class="max-h-52 overflow-y-auto"
          >
            <li
              v-for="(item, i) in props.isSearchable
                ? filteredItems
                : props.items"
              :id="`${props.idTarget}${i}`"
              :class="[
                `${
                  Array.isArray(props.modelValue)
                    ? modelValue.includes(item.value)
                    : item.value === modelValue
                    ? 'bg-gray-50'
                    : ''
                }`,
                'py-2.5 px-3.5 flex items-center justify-between cursor-pointer transition hover:bg-gray-100',
              ]"
              @mousedown="onSelectItem($event, item)"
            >
              <p>{{ item.text }}</p>
              <icon-check
                v-if="
                  Array.isArray(props.modelValue)
                    ? modelValue.includes(item.value)
                    : item.value === modelValue
                "
                size="20"
                class="stroke-primary-500"
              />
            </li>
          </ul>

          <div v-else>
            <button
              v-if="props.isInlineAdd"
              type="button"
              class="py-2.5 px-3.5 w-full text-left"
              @click.prevent="
                emit('on-click-add', searchKey),
                  $combobox?.classList.add('hidden')
              "
            >
              <p v-if="props.isHasCreate" class="text-sm font-semibold space-x-3">
                <span>Create</span>
                <span
                  class="font-normal py-1 px-3 bg-primary-100 text-primary-500 rounded"
                  >{{ searchKey }}</span
                >
              </p>
            </button>

            <p
              v-else
              class="py-3.5 text-center cursor-default text-gray-500"
              @mousedown.prevent=""
            >
              No Items
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
