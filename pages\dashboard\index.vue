
<script setup lang="ts">
import { usePageStore } from '~/store/page'
import { useDashboardStore } from '~/store/dashboard'
import type { Dashboard } from '~/types/server-response'
import { ref, computed, onMounted, watch, nextTick, reactive } from 'vue'
import { useEmployeesStore } from '~/store/employees'
import type {ItemCombobox, ElementEvent} from '~/types/element'
import {toast} from 'vue3-toastify'
import api from '~/services/api'


const $employees = useEmployeesStore()

// Form data untuk Analisa AI
const aiAnalysisForm = ref({
  employee_id: '',
  date_start: '',
  date_end: ''
})

const form = reactive({ ...aiAnalysisForm.value })

const isAnalyzing = ref(false)
const $responseAi = ref<any>(null)

// Computed untuk employee dropdown
const dropdownEmployees = computed<ItemCombobox[]>(() =>
  $employees.listEmployee.map(
    (e): ItemCombobox => ({
      text: e.name,
      value: `${e.id}`,
      detail: undefined,
    })
  )
)

// Function untuk call API Analisa AI
async function callAIAnalysis() {
  if (!aiAnalysisForm.value.employee_id || 
      !aiAnalysisForm.value.date_start || 
      !aiAnalysisForm.value.date_end) {
    toast.error('Mohon lengkapi semua field yang diperlukan')
    return
  }
  
  isAnalyzing.value = true
  try {
    // Prepare query parameters
    const queryParams = {
      employee_id: aiAnalysisForm.value.employee_id,
      date_start: aiAnalysisForm.value.date_start,
      date_end: aiAnalysisForm.value.date_end
    }
    
    // Call API menggunakan service api yang sudah ada
    const response = await api.get('/task/task-ai-analysis', {
      queryParams,
      useGo: false // Menggunakan Laravel API (bukan Go API)
    })
    if (response) {
      toast.success('Analisa AI berhasil dijalankan')
      $responseAi.value = response
    }
    
  } catch (error: any) {
    console.error('AI Analysis error:', error)
    toast.error('Terjadi kesalahan saat menjalankan analisa AI')
  } finally {
    isAnalyzing.value = false
  }
}

// Load employees on mount
onMounted(async () => {
  $page.setTitle('Dashboard')
  await fetchDashboard()
  await $employees.fetchListEmployee({ perpage: '1000' })
  
  // Create line chart after DOM is ready
  await nextTick()
  createLineChart()
})

import {
  Chart,
  LineController,
  LinearScale,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  CategoryScale,
  BarController,
  BarElement
} from 'chart.js'

Chart.register(
  LineController,
  LinearScale,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  CategoryScale,
  BarController,
  BarElement
)

const lineChart = ref(null)
const barChart = ref(null)
const $page = usePageStore()
const $dashboard = useDashboardStore()
async function fetchDashboard() {
  await $dashboard.fetchDashboard()
}

const dashboard = computed<Dashboard | null>(() => {
  return $dashboard.$state.data || null
})

const sumValueStatistik = computed(() => {
  return dashboard.value?.data_statistik?.weekActivity?.reduce((acc: any, curr: any) => {
    const antrianValue = parseInt(curr[0].split(' : ')[1])
    return acc + antrianValue
  }, 0)
})

const mergedchartQueue = computed(() => {
  return dashboard.value?.data_statistik?.weekActivity?.map((item: any) => {
    const queueValue = item[0].split(' : ')[1]
    return parseInt(queueValue)
  })
})

const mergedchartProgress = computed(() => {
  return dashboard.value?.data_statistik?.weekActivity?.map((item: any) => {
    const progressValue = item[1].split(' : ')[1]
    return parseInt(progressValue)
  })
})

const mergedchartDone = computed(() => {
  return dashboard.value?.data_statistik?.weekActivity?.map((item: any) => {
    const doneValue = item[2].split(' : ')[1]
    return parseInt(doneValue)
  })
})

const lineChartInstance = ref(null)
const barChartInstance = ref(null)

// Function untuk membuat line chart
const createLineChart = async () => {
  if (!lineChart.value || !dashboard.value?.data_statistik?.labelStatistik) return
  
  // Destroy existing chart
  if (lineChartInstance.value) {
    lineChartInstance.value.destroy()
  }
  
  await nextTick()
  
  const ctx = lineChart.value.getContext('2d')
  if (!ctx) {
    console.error('Cannot get canvas context for line chart')
    return
  }
  
  lineChartInstance.value = new Chart(ctx, {
    type: 'line',
    data: {
      labels: dashboard.value.data_statistik.labelStatistik,
      datasets: [
        {
          label: 'Antrian',
          backgroundColor: '#616161',
          borderColor: '#616161',
          borderWidth: 1,
          data: mergedchartQueue.value,
        },
        {
          label: 'Dikerjakan',
          backgroundColor: '#0094BC',
          borderColor: '#0094BC',
          borderWidth: 1,
          data: mergedchartProgress.value,
        },
        {
          label: 'Selesai',
          backgroundColor: '#2FA841',
          borderColor: '#2FA841',
          borderWidth: 1,
          data: mergedchartDone.value,
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
        },
      },
    }
  })
}

onMounted(async () => {
  $page.setTitle('Dashboard')
  await fetchDashboard()

  createLineChart()
})

// Function untuk membuat bar chart
const createBarChart = async () => {
  console.log('🎨 createBarChart called')
  console.log('📊 barChart.value:', barChart.value)
  console.log('📊 $responseAi.value:', $responseAi.value)
  
  if (!barChart.value || !$responseAi.value?.graph) {
    console.log('❌ Prerequisites not met:', {
      hasBarChart: !!barChart.value,
      hasResponseAi: !!$responseAi.value,
      hasGraph: !!$responseAi.value?.graph
    })
    return
  }
  
  // Destroy existing chart
  if (barChartInstance.value) {
    console.log('🗑️ Destroying existing chart')
    barChartInstance.value.destroy()
    barChartInstance.value = null
  }
  
  await nextTick()
  
  const ctx = barChart.value.getContext('2d')
  if (!ctx) {
    console.error('❌ Cannot get canvas context for bar chart')
    return
  }
  
  console.log('✅ Canvas context obtained')
  
  const graph = $responseAi.value.graph
  console.log('📊 Graph data:', graph)
  
  try {
    barChartInstance.value = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: ['Total Task', 'Task Done', 'Total Hour', 'Task Done Late'],
        datasets: [{
          label: 'AI Analysis Data',
          data: [
            graph.total_task,
            graph.total_task_done,
            graph.total_hour,
            graph.total_task_done_but_late
          ],
          backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'],
          borderColor: ['#2563EB', '#059669', '#D97706', '#DC2626'],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: true,
            text: 'AI Analysis - Task Statistics'
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              stepSize: 1
            }
          }
        }
      }
    })
    
    console.log('✅ Bar chart created successfully:', barChartInstance.value)
    
  } catch (error) {
    console.error('❌ Error creating bar chart:', error)
  }
}

// Watch untuk update chart ketika response AI berubah
watch(() => $responseAi.value, async (newValue) => {
  if (newValue?.graph) {
    console.log('AI Response received:', newValue.graph)
    await nextTick()
    createBarChart()
  }
}, { deep: true })
</script>

<template>
  <div>
    <div class="flex gap-5">
      <div
        class="bg-white text-gray-500 border h-44 w-96 border-gray-300 rounded-xl shadow-sm pt-7"
      >
        <p class="text-xl text-center font-bold">Jumlah Karyawan</p>
        <div class="flex gap-5">
          <div
            class="p-2 aspect-square w-max rounded-full mt-2 ml-7 bg-error-100 border-8 border-error-50"
          >
            <div class="p-2 aspect-square w-max rounded-full bg-error-200">
              <icon-users class="stroke-error-500" />
            </div>
          </div>
          <div class="-ml-2 mt-8">
            <p class="font-extrabold text-black text-5xl">
              {{ dashboard?.jumlah_karyawan }}
            </p>
          </div>
        </div>
      </div>
      <div
        class="bg-white text-gray-500 border h-44 w-96 border-gray-300 rounded-xl shadow-sm pt-7"
      >
        <p class="text-xl text-center font-bold">Tugas yang Dibuat</p>
        <div class="flex gap-5">
          <div
            class="p-2 aspect-square w-max rounded-full mt-2 ml-7 bg-error-100 border-8 border-error-50"
          >
            <div class="p-2 aspect-square w-max rounded-full bg-error-200">
              <icon-file class="stroke-error-500" />
            </div>
          </div>
          <div class="-ml-2 mt-8">
            <p class="font-extrabold text-black text-5xl">
              {{ dashboard?.tugas_bulan_ini }}
            </p>
          </div>
        </div>
      </div>
      <div
        class="bg-white text-gray-500 border h-44 w-96 border-gray-300 rounded-xl shadow-sm pt-7"
      >
        <p class="text-xl text-center font-bold">Tugas Belum Selesai</p>
        <div class="flex gap-5">
          <div
            class="p-2 aspect-square w-max rounded-full mt-2 ml-7 bg-error-100 border-8 border-error-50"
          >
            <div class="p-2 aspect-square w-max rounded-full bg-error-200">
              <nuxt-img
                src="/dashboard/Activity.png"
                width="27"
                height="27"
                class="rounded-lg"
                alt="App icon"
              />
            </div>
          </div>
          <div class="-ml-2 mt-8">
            <p class="font-extrabold text-black text-5xl">
              {{ dashboard?.tugas_belum_selesai }}
            </p>
          </div>
        </div>
      </div>
      <div
        class="bg-white text-gray-500 border h-44 w-96 border-gray-300 rounded-xl shadow-sm pt-7"
      >
        <p class="text-xl text-center font-bold">Tugas Selesai</p>
        <div class="flex gap-5">
          <div
            class="p-2 aspect-square w-max rounded-full mt-2 ml-7 bg-error-100 border-8 border-error-50"
          >
            <div class="p-2 aspect-square w-max rounded-full bg-error-200">
              <nuxt-img
                src="/dashboard/file-check-02.png"
                width="27"
                height="27"
                class="rounded-lg"
                alt="App icon"
              />
            </div>
          </div>
          <div class="-ml-2 mt-8">
            <p class="font-extrabold text-black text-5xl">
              {{ dashboard?.tugas_selesai }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white border rounded-xl shadow-sm overflow-hidden mt-10">
      <p class="text-2xl font-bold ml-7 mt-8">Statistik Aktifitas Mingguan</p>
      <div class="flex gap-3">
        <div class="flex flex-row">
          <div class="rounded-md bg-gray-500 w-5 h-5 ml-6 mt-3"></div>
          <p class="text-gray-500 ml-4 mt-3">Belum Dikerjakan</p>
        </div>
        <div class="flex flex-row">
          <div class="rounded-md bg-blue-500 w-5 h-5 ml-6 mt-3"></div>
          <p class="text-gray-500 ml-4 mt-3">Dalam Proses</p>
        </div>
        <div class="flex flex-row">
          <div class="rounded-md bg-success-500 w-5 h-5 ml-6 mt-3"></div>
          <p class="text-gray-500 ml-4 mt-3">Selesai</p>
        </div>
      </div>
      <div class="h-250 min-w-full">
        <div
          v-if="sumValueStatistik < 1"
          style="text-align: center"
          class="pb-5"
        >
          <nuxt-img
            src="/dashboard/Illustrasi_tidak_ada_data.png"
            width="300"
            height="300"
            class="rounded-lg p-8"
            alt="App icon"
            style="
              aspect-ratio: 300 / 300;
              object-fit: cover;
              display: inline-block;
            "
          />
          <p class="text-sm font-bold">Tidak ada data tersedia</p>
          <p class="text-xs">
            Tidak ada yang dapat ditampilkan dengan data filter tersebut
          </p>
        </div>

        <div v-else class="p-5">
          <canvas ref="lineChart" width="400" height="400"></canvas>
        </div>
      </div>
    </div>

    <div class="bg-white border rounded-xl shadow-sm overflow-hidden mt-10">
      <div class="flex justify-between">
        <p class="text-2xl font-bold ml-7 mt-8">Absensi Hari Ini</p>
        <div class="mr-10 flex space-x-1 mt-9">
          <icon-users class="stroke-slate-900" />
          <p class="text-lg font-bold text-error-600">
            ({{
              dashboard?.list_attendance && dashboard?.list_attendance.length
            }})<span class="font-medium ml-2 text-gray-500">Orang</span>
          </p>
        </div>
      </div>
      <div class="overflow-x-auto pt-5">
        <table class="w-full text-lg text-left" style="empty-cells: show">
          <thead class="text-sm text-gray-700 bg-gray-50 border-b">
            <tr>
              <th scope="col" class="px-6 py-3 font-medium">No</th>
              <th scope="col" class="px-6 py-3 font-medium">Nama Karyawan</th>
              <th scope="col" class="px-6 py-3 font-medium">Lokasi Check In</th>
              <th scope="col" class="px-6 py-3 font-medium">
                Tanggal Check In
              </th>
              <th scope="col" class="px-6 py-3 font-medium">Waktu Check In</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-if="
                dashboard?.list_attendance &&
                dashboard?.list_attendance.length < 1
              "
            >
              <td colspan="5" class="p-8" style="text-align: center">
                <nuxt-img
                  src="/dashboard/Illustrasi_tidak ada kehadiran.png"
                  width="300"
                  height="300"
                  class="rounded-lg p-8"
                  alt="App icon"
                  style="
                    aspect-ratio: 300 / 300;
                    object-fit: cover;
                    display: inline-block;
                  "
                />
                <p class="text-sm font-bold">Tidak ada data kehadiran</p>
                <p class="text-xs">
                  Maaf belum ada data kehadiran/Check In dari
                </p>
                <p class="text-xs">karyawan hari ini</p>
              </td>
            </tr>
            <tr
              v-else
              v-for="(attendance, i) in dashboard?.list_attendance"
              :key="i"
            >
              <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
                {{ getDataTableNumber(i, 1) }}
              </td>
              <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
                {{ attendance?.nama_karyawan }}
              </td>
              <td class="px-6 py-4 font-normal text-gray-900 max-w-xs">
                <div>
                  {{ attendance?.lokasi_check_in }}
                </div>
              </td>
              <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
                {{ attendance?.check_in_date }}
              </td>
              <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
                {{ attendance?.check_in_time }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="bg-white border rounded-xl shadow-sm mt-10">
      <div class="flex justify-between">
        <p class="text-2xl font-bold ml-7 mt-8">Hasil Analisa AI</p>
      </div>
      
      <!-- Form Input -->
      <div class="p-6">
        <div class="grid gap-6 md:grid-cols-3 mb-6">
          <!-- Start Date -->
          <div>
            <general-datepicker
              v-model="aiAnalysisForm.date_start"
              id="inputFilterStartDate"
              label="Start Date"
              placeholder="dd/mm/yyyy"
              format="yyyy-MM-dd"
              class="flex-1"
            />
          </div>
          
          <!-- End Date -->
          <div>
            <general-datepicker
              v-model="aiAnalysisForm.date_end"
              id="inputFilterEndDate"
              label="End Date"
              placeholder="dd/mm/yyyy"
              format="yyyy-MM-dd"
              class="flex-1"
            />
          </div>
          
          <!-- Employee Selection -->
          <div>
            <general-combobox
              v-model="aiAnalysisForm.employee_id"
              id="selectEmployee"
              has-search-field
              id-trigger="triggerDropdownEmployee"
              id-target="dropdownEmployee"
              label="Pegawai"
              plain
              placeholder="Pilih Pegawai"
              :items="dropdownEmployees"
              :loading="$employees.isLoading.list"
            />
          </div>
        </div>
        
        <!-- Submit Button -->
        <div class="flex justify-end mb-6">
          <general-button
            label="Mulai Analisa AI"
            :loading="isAnalyzing"
            :disabled="!aiAnalysisForm.employee_id || !aiAnalysisForm.date_start || !aiAnalysisForm.date_end"
            @click="callAIAnalysis"
          />
        </div>
      </div>
      
      <!-- Chart Section -->
      <div v-if="$responseAi?.graph" class="p-6 border-t">
        <div class="mb-4">
          <h3 class="text-lg font-semibold text-gray-800 mb-2">Statistik Task</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <!-- Summary Cards -->
            <div class="bg-blue-50 p-4 rounded-lg">
              <p class="text-sm text-blue-600 font-medium">Total Task</p>
              <p class="text-2xl font-bold text-blue-800">{{ $responseAi.graph.total_task }}</p>
            </div>
            <div class="bg-green-50 p-4 rounded-lg">
              <p class="text-sm text-green-600 font-medium">Task Selesai</p>
              <p class="text-2xl font-bold text-green-800">{{ $responseAi.graph.total_task_done }}</p>
            </div>
            <div class="bg-yellow-50 p-4 rounded-lg">
              <p class="text-sm text-yellow-600 font-medium">Total Jam</p>
              <p class="text-2xl font-bold text-yellow-800">{{ $responseAi.graph.total_hour }}</p>
            </div>
            <div class="bg-red-50 p-4 rounded-lg">
              <p class="text-sm text-red-600 font-medium">Task Terlambat</p>
              <p class="text-2xl font-bold text-red-800">{{ $responseAi.graph.total_task_done_but_late }}</p>
            </div>
          </div>
        </div>
        
        <!-- Bar Chart -->
        <div class="bg-gray-50 p-4 rounded-lg">
          <div class="h-96">
            <canvas ref="barChart"></canvas>
          </div>
        </div>
        
        <!-- AI Analysis Text Section -->
        <div class="mt-8 space-y-6">
          <h3 class="text-xl font-semibold text-gray-800 mb-4">Hasil Analisis AI</h3>
          
          <!-- Analysis -->
          <div class="bg-blue-50 border-l-4 border-blue-400 p-6 rounded-r-lg">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <div class="ml-3">
                <h4 class="text-lg font-semibold text-blue-800 mb-2">Analisis Data</h4>
                <p class="text-blue-700 leading-relaxed">{{ $responseAi.analysis }}</p>
              </div>
            </div>
          </div>
          
          <!-- Insight -->
          <div class="bg-purple-50 border-l-4 border-purple-400 p-6 rounded-r-lg">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
              </div>
              <div class="ml-3">
                <h4 class="text-lg font-semibold text-purple-800 mb-2">Insight</h4>
                <p class="text-purple-700 leading-relaxed">{{ $responseAi.insight }}</p>
              </div>
            </div>
          </div>
          
          <!-- Prediction -->
          <div class="bg-orange-50 border-l-4 border-orange-400 p-6 rounded-r-lg">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
              </div>
              <div class="ml-3">
                <h4 class="text-lg font-semibold text-orange-800 mb-2">Prediksi</h4>
                <p class="text-orange-700 leading-relaxed">{{ $responseAi.prediction }}</p>
              </div>
            </div>
          </div>
          
          <!-- Conclusion -->
          <div class="bg-gray-50 border-l-4 border-gray-400 p-6 rounded-r-lg">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
              </div>
              <div class="ml-3">
                <h4 class="text-lg font-semibold text-gray-800 mb-2">Kesimpulan</h4>
                <p class="text-gray-700 leading-relaxed">{{ $responseAi.conclusion }}</p>
              </div>
            </div>
          </div>
          
          <!-- Recommendation -->
          <div class="bg-green-50 border-l-4 border-green-400 p-6 rounded-r-lg">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                </svg>
              </div>
              <div class="ml-3">
                <h4 class="text-lg font-semibold text-green-800 mb-2">Rekomendasi</h4>
                <p class="text-green-700 leading-relaxed">{{ $responseAi.recommendation }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- No Data State -->
      <div v-else-if="$responseAi && !$responseAi.graph" class="p-6 text-center">
        <p class="text-gray-500">Tidak ada data grafik tersedia</p>
      </div>
    </div>
  </div>
</template>

<style scoped></style>





