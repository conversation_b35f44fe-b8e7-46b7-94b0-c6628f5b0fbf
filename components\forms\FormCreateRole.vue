<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from "vue";
import { useRouter } from "vue-router";
import { toast } from "vue3-toastify";
import { useRolesStore } from "~/store/roles";
import { usePermissionsStore } from "~/store/permissions";
import { useCompaniesStore } from "~/store/companies";
import { useAuthStore } from "~/store/auth";

const $rolesStore = useRolesStore();
const $permissionsStore = usePermissionsStore();
const $companiesStore = useCompaniesStore();
const $auth = useAuthStore();

const router = useRouter();

const props = defineProps<{
  roleId?: string;
  isEdit?: boolean;
}>();

const role = ref({
  name: "",
  company_id: "",
  description: "",
});

// Add refs for tracking role permissions
const rolePermissions = ref<string[]>([]);

onMounted(async () => {
  await $permissionsStore.fetchPermissions();
  await $companiesStore.fetchCompanies({ entries: '1000', page_size: 1000 });

  // If editing, fetch role details
  if (props.isEdit && props.roleId) {
    await $rolesStore.fetchDetail(props.roleId);
    populateFormData();
  }
});

// Add method to populate form with existing data
const populateFormData = () => {
  if ($rolesStore.detail) {
    role.value.name = $rolesStore.detail.name;
    role.value.company_id = $rolesStore.detail.company_id?.toString() || "";
    role.value.description = $rolesStore.detail.description || "";

    // Set role permissions - this will trigger the computed property to update
    rolePermissions.value =
      $rolesStore.detail.permissions?.map((p) => p.id) || [];
  }
};

// Add method to update permission checkboxes
const updatePermissionCheckboxes = () => {
  features.value.forEach((feature: any) => {
    Object.values(feature.permissions).forEach((permission: any) => {
      if (permission.id && rolePermissions.value.includes(permission.id)) {
        permission.checked = true;
      }
    });
  });
};

const permissions = computed(() => $permissionsStore.permissions);
const companies = computed(() => $companiesStore.companies);

const featureSearch = ref("");

// Add this ref to track selected features
const selectedFeatures = ref<Set<string>>(new Set());

// Create a reactive state for permission selections that can be modified
const permissionSelections = ref<Map<string, boolean>>(new Map());

// Transform the permissions data for the table
const features = computed(() => {
  if (!permissions.value) return [];

  // Local helper to know if a permission should be checked
  const isChecked = (perm: any) => {
    const permId = perm.id?.toString();
    // Check reactive selections first, then fall back to rolePermissions
    if (permissionSelections.value.has(permId)) {
      return permissionSelections.value.get(permId);
    }
    return rolePermissions.value.includes(perm.id);
  };

  return permissions.value.map((group: any) => {
    const permissionsMap: Record<string, { id: string; checked: boolean }> = {};
    let anyChecked = false;
    group.permissions.forEach((permission: any) => {
      const permissionType = permission.name.toLowerCase();
      const checked = isChecked(permission);
      if (checked) anyChecked = true;
      
      permissionsMap[permissionType] = {
        id: permission.id,
        checked
      };
    });

    const featureId = group.group;
    // If any permission in the feature is checked, mark the feature as selected
    if (anyChecked) {
      selectedFeatures.value.add(featureId);
    }

    return {
      id: featureId,
      name: group.group
        .split("_")
        .map((word: any) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" "),
      selected: anyChecked,
      permissions: {
        view: permissionsMap.view || { id: "", checked: false },
        create: permissionsMap.create || { id: "", checked: false },
        edit: permissionsMap.edit || { id: "", checked: false },
        delete: permissionsMap.delete || { id: "", checked: false },
      },
    };
  });
});

// Computed property for filtered features based on search
const filteredFeatures = computed(() => {
  if (!featureSearch.value) return features.value;
  const searchTerm = featureSearch.value.toLowerCase();
  return features.value.filter((feature: any) =>
    feature.name.toLowerCase().includes(searchTerm)
  );
});

// Add a computed property to check if a feature is selected
const isFeatureSelected = computed(() => (featureId: string) => {
  return selectedFeatures.value.has(featureId);
});

// Toggle all features
const areAllFeaturesSelected = computed(() => {
  return (
    filteredFeatures.value.length > 0 &&
    filteredFeatures.value.every((feature: any) =>
      isFeatureSelected.value(feature.id)
    )
  );
});

const toggleAllFeatures = (checked: boolean) => {
  const newSelectedFeatures = new Set(checked ? 
    filteredFeatures.value.map(f => f.id) : 
    []
  );
  
  const newPermissionSelections = new Map(permissionSelections.value);
  
  filteredFeatures.value.forEach((feature: any) => {
    Object.keys(feature.permissions).forEach(permissionType => {
      const permissionKey = feature.permissions[permissionType].id;
      newPermissionSelections.set(permissionKey, checked);
    });
  });
  
  selectedFeatures.value = newSelectedFeatures;
  permissionSelections.value = newPermissionSelections;
};


const toggleFeatureSelection = (featureId: string, checked: boolean) => {
  if (checked) {
    selectedFeatures.value.add(featureId);
    // Select all permissions for this feature
    const feature = features.value.find(f => f.id === featureId);
    if (feature) {
      Object.keys(feature.permissions).forEach(permissionType => {
        const permissionKey = feature.permissions[permissionType].id;
        permissionSelections.value.set(permissionKey, true);
      });
    }
  } else {
    selectedFeatures.value.delete(featureId);
    // Deselect all permissions for this feature
    const feature = features.value.find(f => f.id === featureId);
    if (feature) {
      Object.keys(feature.permissions).forEach(permissionType => {
        const permissionKey = feature.permissions[permissionType].id;
        permissionSelections.value.set(permissionKey, false);
      });
    }
  }
  // Create a new Set to trigger reactivity
  selectedFeatures.value = new Set(selectedFeatures.value);
};

const handlePermissionChange = (featureId: string, permissionType: string, checked: boolean) => {
  const feature = features.value.find(f => f.id === featureId);
  if (!feature) return;

  const permissionKey = feature.permissions[permissionType].id;
  permissionSelections.value.set(permissionKey, checked);

  // Auto-select view permission if another permission is selected
  if (checked && permissionType !== 'view' && feature.permissions.view?.id) {
    permissionSelections.value.set(feature.permissions.view.id, true);
  }
};

// Get selected permissions
const getSelectedPermissions = () => {
  const selected: any = [];
  features.value.forEach((feature: any) => {
    Object.values(feature.permissions).forEach((permission: any) => {
      if (permission.checked && permission.id) {
        selected.push(permission.id);
      }
    });
  });
  return selected;
};

function goBack() {
  navigateTo("/roles");
}

const showPassword = ref(false);

const limitDescription = (e: any) => {
  if (role.value.description.length > 200) {
    role.value.description = role.value.description.slice(0, 200);
  }
};

const saveRole = async () => {
  try {
    const selectedPermissionIds = getSelectedPermissions();
    const requestData: any = {
      name: role.value.name,
      description: role.value.description,
      permissions: selectedPermissionIds,
    };

    if (
      $auth.session?.role?.name === "Super Admin" ||
      $auth.session?.session?.role?.name === "Super Admin"
    ) {
      requestData.company_id = role.value.company_id.toString();
    }

    let response;
    if (props.isEdit && props.roleId) {
      response = await $rolesStore.updateRole(props.roleId, requestData);
    } else {
      response = await $rolesStore.createRole(requestData);
    }

    router.push("/roles");
  } catch (error) {
    console.error("Error saving role:", error);
  }
};
</script>

<template>
  <div>
    <p class="text-gray-500 mb-10">
      Atur peran dan izin akses pengguna sesuai tanggung jawabnya
    </p>

    <div class="mt-4">
      <button
        class="flex mb-5 items-center space-x-2 px-3 py-2 text-black rounded-md"
        @click="goBack"
      >
        <svg
          id="back-arrow"
          class="-ml-4"
          height="24"
          viewBox="0 0 24 24"
          width="24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M0 0h24v24H0V0z" fill="none" opacity=".87"></path>
          <path
            d="M16.62 2.99c-.49-.49-1.28-.49-1.77 0L6.54 11.3c-.39.39-.39 1.02 0 1.41l8.31 8.31c.49.49 1.28.49 1.77 0s.49-1.28 0-1.77L9.38 12l7.25-7.25c.48-.48.48-1.28-.01-1.76z"
          ></path>
        </svg>
        <span class="font-bold text-xl">Kembali</span>
      </button>
    </div>

    <div class="p-6">
      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <h2 class="text-lg font-semibold mb-4">
          {{ isEdit ? "Edit Role" : "Informasi Role" }}
        </h2>

        <div class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div
              v-if="
                $auth.session?.role?.name === 'Super Admin' ||
                $auth.session?.session?.role?.name === 'Super Admin'
              "
            >
              <label
                for="company"
                class="block text-sm font-medium text-gray-700 mb-1"
                >Perusahaan <span class="text-red-500">*</span></label
              >
              <select
                id="company"
                v-model="role.company_id"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500"
              >
                <option value="">Pilih Perusahaan</option>
                <option
                  v-for="company in companies"
                  :key="company.id"
                  :value="company.id"
                >
                  {{ company.name }}
                </option>
              </select>
            </div>
            <div>
             <label 
                for="role-name"
                class="block text-sm font-medium text-gray-700 mb-1"
                >Nama Role <span class="text-red-500">*</span></label
              >
              <input
                id="role-name"
                v-model="role.name"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Ex : Admin"
              />
            </div>
          </div>
        </div>

        <div>
          <div class="flex justify-between items-center mb-1 mt-3">
            <label class="block text-sm font-medium text-gray-700"
              >Deskripsi</label
            >
            <span class="text-xs text-gray-500">
              {{ 200 - (role.description?.length || 0) }} / 200
            </span>
          </div>
          <textarea
            v-model="role.description"
            rows="3"
            maxlength="200"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-5 00"
            placeholder="Ex: Untuk mengatur user"
            @input="limitDescription"
          ></textarea>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6 mb-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-semibold mb-2">
            Informasi Perizinan Perusahaan<span class="text-red-500">*</span>
          </h2>
          <div class="text-sm font-semibold">
            <div class="flex items-center">
              <p>Total Fitur</p>
              (
              <p class="text-red-500">{{ selectedFeatures.size }}</p>
              )
            </div>
          </div>
        </div>
        <div class="mb-4">
          <div class="relative">
            <input
              v-model="featureSearch"
              type="text"
              class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Cari fitur..."
            />
            <div
              class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
            >
              <svg
                class="h-5 w-5 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                ></path>
              </svg>
            </div>
          </div>
        </div>

        <div class="overflow-x-auto">
          <table
            class="min-w-full divide-y divide-gray-200"
            aria-describedby="Tabel Daftar Fitur"
          >
            <thead>
              <tr>
                <th
                  class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  <div class="flex items-center">
                    <input
                      type="checkbox"
                      :checked="areAllFeaturesSelected"
                      @change="toggleAllFeatures($event.target.checked)"
                      class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded mr-2"
                    />
                    Fitur
                  </div>
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  View
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Create
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Edit
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Delete
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="feature in filteredFeatures" :key="feature.id">
                <td
                  class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"
                >
                  <div class="flex items-center">
                    <input
                      type="checkbox"
                      :checked="isFeatureSelected(feature.id)"
                      @change="
                        toggleFeatureSelection(
                          feature.id,
                          $event.target.checked
                        )
                      "
                      class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded mr-2"
                    />
                    {{ feature.name }}
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-center">
                  <input
                    :checked="feature.permissions.view.checked"
                    @change="handlePermissionChange(feature.id, 'view', $event.target.checked)"
                    type="checkbox"
                    class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                  />
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-center">
                  <input
                    :checked="feature.permissions.create.checked"
                    @change="handlePermissionChange(feature.id, 'create', $event.target.checked)"
                    type="checkbox"
                    class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                  />
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-center">
                  <input
                    :checked="feature.permissions.edit.checked"
                    @change="handlePermissionChange(feature.id, 'edit', $event.target.checked)"
                    type="checkbox"
                    class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                  />
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-center">
                  <input
                    :checked="feature.permissions.delete.checked"
                    @change="handlePermissionChange(feature.id, 'delete', $event.target.checked)"
                    type="checkbox"
                    class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
                  />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div class="flex justify-end space-x-4">
        <button
          @click="$router.back()"
          class="px-4 py-2 border border-red-500 text-red-500 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
        >
          Batal
        </button>
        <button
          type="button"
          @click="saveRole"
          class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          :disabled="$rolesStore.loading"
        >
          {{
            $rolesStore.loading ? "Menyimpan..." : isEdit ? "Update" : "Tambah"
          }}
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
input[type="checkbox"]:checked {
  @apply bg-red-500 border-red-500;
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

input[type="checkbox"]:indeterminate {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M4 8h8v1H4z'/%3e%3c/svg%3e");
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}
</style>
