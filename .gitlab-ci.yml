stages:
  - build
  - test
  - publish
  - release

default:
  tags:
    - build-gcp

variables:
  CONTAINER_REGISTRY: ${CI_REGISTRY_IMAGE}/application
  KEY: $key
  SONAR_TOKEN: $sonar_access_token
  REPOPO_TOKEN: $repopo_token

build-check:
  stage: build
  script:
    - cp .env.example .env
    - docker build -t $CONTAINER_REGISTRY:${CI_COMMIT_SHA} -t $CONTAINER_REGISTRY:latest -f Dockerfile .
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
  artifacts:
    paths:
      - .env
    expire_in: 2 hours
    when: always

build:
  stage: build
  script:
    - echo "$ENV_STAGING_HSE" > "$(pwd)/.env"
    - docker build -t $CONTAINER_REGISTRY:${CI_COMMIT_TAG} -t $CONTAINER_REGISTRY:latest -f Dockerfile .
  except:
    - branches
  artifacts:
    paths:
      - .env
    expire_in: 1 days
    when: always

sonarqube-check:
  stage: test
  tags:
    - tools-gcp
  image:
    name: sonarsource/sonar-scanner-cli:5.0
    entrypoint: [""]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
    - sonar-scanner
  #allow_failure: true
  except:
    - branches
  only:
    - merge_requests
    - /^([A-Za-z0-9.-]+)$/

report-sonar-to-merge-id:
  stage: test
  dependencies:
    - sonarqube-check
  tags:
    - tools-gcp
  script:
    - PROJECT_ID=$CI_PROJECT_ID
    - MERGE_IID=$CI_MERGE_REQUEST_IID
    - KEY=$KEY SONAR_TOKEN=$sonar_access_token repopo_token=$repopo_token PROJECT_ID=$PROJECT_ID MERGE_IID=$MERGE_IID bash -c 'sed -i "1s/^/project_id=$PROJECT_ID merge_iid=$MERGE_IID key=$KEY sonar_token=$sonar_access_token repopo_token=$repopo_token /" report.sh'
    - apt-get update -qy  # Install curl
    - apt-get install -y curl
    - bash report.sh
  except:
    - branches
  only:
    - merge_requests
  needs:
    - job: sonarqube-check

publish:
  stage: publish
  dependencies:
    - sonarqube-check
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker push $CONTAINER_REGISTRY:${CI_COMMIT_TAG}
    - docker push $CONTAINER_REGISTRY:latest
  after_script:
    - docker image rm $CONTAINER_REGISTRY:${CI_COMMIT_TAG}
    - docker image rm $CONTAINER_REGISTRY:latest
    - docker builder prune -af || true
  except:
    - branches
  artifacts:
    paths:
      - .env
    expire_in: 1 days
    when: always

staging-hse:
  stage: release
  dependencies:
    - publish
  variables:
    GIT_STRATEGY: none
    ENVI: staging
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - echo "$ENV_STAGING_HSE" > "$(pwd)/.env"
    - echo "$NOTIFIER" > "$(pwd)/notifier.sh"
    - echo "$DOCKER_COMPOSE_NGINX" | base64 --decode > "$(pwd)/docker-compose.yml"
    - docker compose pull application && docker compose up -d --force-recreate
    - docker image prune -f
    - bash notifier.sh
  tags:
    - hse-staging-gcp
  only:
    - /^([A-Za-z0-9.-]+)$/
  except:
    - branches
  artifacts:
    paths:
      - .env
      - docker-compose.yml
    expire_in: 1 days
    when: always

pre-production-hse:
  stage: release
  variables:
    # GIT_STRATEGY: none
    ENVI: pre-production
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - echo "$ENV_PREPROD_HSE" > "$(pwd)/.env"
    - echo "$NOTIFIER" > "$(pwd)/notifier.sh"
    - echo "$DOCKER_COMPOSE_NGINX" | base64 --decode > "$(pwd)/docker-compose.yml"
    - docker build -t $CONTAINER_REGISTRY:${CI_COMMIT_TAG} -t $CONTAINER_REGISTRY:latest -f Dockerfile .
    - docker compose up -d --force-recreate 
    - docker image prune -f
    - bash notifier.sh
  tags:
    - hse-preprod-gcp
  only:
    - /^([A-Za-z0-9.-]+)$/
  except:
    - branches
  artifacts:
    paths:
      - .env
      - docker-compose.yml
    expire_in: 1 days
    when: always
  when: manual

playground-hse:
  stage: release
  variables:
    # GIT_STRATEGY: none
    ENVI: playground
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - echo "$ENV_PLAYGROUND_HSE" > "$(pwd)/.env"
    - echo "$NOTIFIER" > "$(pwd)/notifier.sh"
    - echo "$DOCKER_COMPOSE_NGINX" | base64 --decode > "$(pwd)/docker-compose.yml"
    - docker build -t $CONTAINER_REGISTRY:${CI_COMMIT_TAG} -t $CONTAINER_REGISTRY:latest -f Dockerfile .
    - docker compose up -d --force-recreate 
    - docker image prune -f
    - bash notifier.sh
  tags:
    - hse-production-gcp
  only:
    - /^([A-Za-z0-9.-]+)$/
  except:
    - branches
  artifacts:
    paths:
      - .env
      - docker-compose.yml
    expire_in: 1 days
    when: always
  when : manual

production-hse:
  stage: release
  variables:
    # GIT_STRATEGY: none
    ENVI: production
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - echo "$ENV_RELEASE_HSE" > "$(pwd)/.env"
    - echo "$NOTIFIER" > "$(pwd)/notifier.sh"
    - echo "$DOCKER_COMPOSE_NGINX" | base64 --decode > "$(pwd)/docker-compose.yml"
    - docker build -t $CONTAINER_REGISTRY:${CI_COMMIT_TAG} -t $CONTAINER_REGISTRY:latest -f Dockerfile .
    - docker compose up -d --force-recreate 
    - docker image prune -f
    - bash notifier.sh
  tags:
    - hse-production-gcp
  only:
    - /^([A-Za-z0-9.-]+)$/
  except:
    - branches
  artifacts:
    paths:
      - .env
      - docker-compose.yml
    expire_in: 1 days
    when: always
  when : manual