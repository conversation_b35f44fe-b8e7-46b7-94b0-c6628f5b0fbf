meta {
  name: Login
  type: http
  seq: 1
}

post {
  url: {{base_url}}/login
  body: multipartForm
  auth: none
}

body:multipart-form {
  email: <EMAIL>
  password: 123456
  ~rememberme: true
}

script:pre-request {
  // 
}

tests {
  // if(pm.response.to.have.status(200)) {
  //         pm.test("Status code is 200", function () {
  //         pm.response.to.have.status(200);
  //         var result = pm.response.json();
  //         pm.environment.set("token", result.token);
  //     });
  // }
}
