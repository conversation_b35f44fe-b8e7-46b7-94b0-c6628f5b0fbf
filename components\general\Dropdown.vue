<script setup lang="ts">
import {onMounted} from "vue";
import {initFlowbite} from "flowbite";

onMounted(() => {
  initFlowbite()
})

const props = defineProps({
  idActivator: {
    type: String,
    default: ''
  },
  idDropdown: {
    type: String,
    default: ''
  }
})
</script>

<template>
  <slot name="activator" />

  <div :id="props.idDropdown" class="z-10 hidden bg-white rounded-lg border">
    <div :aria-labelledby="props.idActivator" class="divide-y">
      <slot name="content"/>
    </div>
  </div>
</template>

<style scoped>

</style>