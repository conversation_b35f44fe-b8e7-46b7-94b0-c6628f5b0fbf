<script setup lang="ts">
const emit = defineEmits(['on-click-export-excel', 'on-click-export-pdf'])
</script>

<template>
  <general-dropdown id-activator="btnExport" id-dropdown="dropdownExport">
    <template #activator>
      <general-outlined-button
          id="btnExport"
          data-dropdown-toggle="dropdownExport"
          label="Export"
          class="border-primary-500 border-2 text-primary-500"
      >
        <template #prefix>
          <icon-chevron-down class="stroke-primary-500" size="20"/>
        </template>
      </general-outlined-button>
    </template>

    <template #content>
      <div class="py-2.5 px-4 flex items-center cursor-pointer hover:bg-gray-100">
        <p class="text-sm" @click="emit('on-click-export-excel')">Export Excel</p>
      </div>
      <div class="py-2.5 px-4 flex items-center cursor-pointer hover:bg-gray-100">
        <p class="text-sm" @click="emit('on-click-export-pdf')">Export PDF</p>
      </div>
    </template>
  </general-dropdown>
</template>

