<script setup lang="ts">
import {definePageMeta} from "#imports";
import {useAuthStore} from "~/store/auth";
import {onMounted} from "vue";
import {initFlowbite} from "flowbite";
import {object, string} from "yup";
import {toast} from "vue3-toastify";

definePageMeta({
  auth: {
    unauthenticatedOnly: true,
    navigateAuthenticatedTo: '/dashboard'
  },
  layout: false,
  layoutTransition: {
    mode: 'out-in'
  }
})

const { $pwa } = useNuxtApp()
const $auth = useAuthStore()
const isShowNotificationError = ref(false)
const showPass = ref(false);

onMounted(() => {
  initFlowbite()
  $pwa.showInstallPrompt = true
})

const schema = object({
  email: string().required().max(50),
  password: string().required().min(6).max(30)
})

const isValid = computed(() => {
  try {
    return schema.validateSync({
      email: $auth.email,
      password: $auth.password,
    })
  } catch (_) {
    return false
  }
})

async function onSubmitLogin() {
  const response = await $auth.login()

  if (!response) {
    isShowNotificationError.value = true

    toast.error('Email atau password tidak sesuai')
  }
}
</script>

<template>
  <div>
    <NuxtPwaManifest/>

    <client-only>
      <div v-if="$pwa?.showInstallPrompt && !$pwa?.isPWAInstalled" class="block sm:hidden rounded-lg overflow-hidden border border-gray-200 dark:border-gray-800">
        <div class="flex p-4 items-center gap-4">
          <nuxt-img
              src="/order-planning-logo-only.png"
              width="64"
              height="64"
              class="rounded-lg"
              alt="App icon"
              style="aspect-ratio: 64 / 64; object-fit: cover;"
          />
          <div class="flex-1">
            <h3 class="text-lg font-medium leading-none mb-2">Install Web App</h3>
            <p class="text-sm leading-none text-gray-500 dark:text-gray-400">
              Tap <span class="font-medium">Add to Home Screen</span> to add this web app to your home screen.
            </p>
          </div>
          <button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-9 rounded-md px-3"
                  @click="$pwa.install()">
            Install
          </button>
        </div>
      </div>
    </client-only>
    <div class="lg:grid grid-cols-2 overflow-hidden">
      <div class="hidden lg:block relative min-h-screen">
        <div
            class="absolute w-full h-full mx-6 bg-[url('~/assets/image/login-banner.png')] bg-left bg-no-repeat"/>

      </div>

      <div class="py-10 min-h-screen w-full flex flex-col items-center justify-center">
        <div class="w-3/4 lg:w-2/3 flex flex-col">
          <div class="mb-8">
            <p class="text-4xl font-[600] mb-3 text-gray-900">Log in</p>
            <p class="text-gray-500">Welcome back! Please enter your details.</p>
          </div>

          <form @submit.prevent="onSubmitLogin" class="flex flex-col">
            <general-text-input
                v-model="$auth.email"
                id="inputEmail"
                type="text"
                label="Email or Username"
                placeholder="Enter your email or username"
                class="mb-5"
            />

            <general-text-input
                v-model="$auth.password"
                id="inputPassword"
                :type="showPass ? 'text' : 'password'"
                label="Password"
                placeholder="Enter your password"
                class="mb-6"
            >
              <template #suffix>
                <button
                    id="btnLogin"
                    type="button"
                    @click="showPass = !showPass"
                    class="pointer-events-auto h-8"
                >
                  <icon-eye-closed v-if="!showPass" class="stroke-gray-500"/>
                  <icon-eye v-else class="stroke-gray-500"/>
                </button>
              </template>
            </general-text-input>

            <notification-error
                id="notification-error"
                title="Wrong credentials"
                subtitle="Your password is incorrect or this account doesn't exist."
                :is-show="isShowNotificationError"
                class="mb-6"
                @on-click-close="isShowNotificationError = false"
            />

            <general-link v-show="false" id="btnForgotPass" label="Forgot your password?"/>

            <general-button
                id="btnLogin"
                type="submit"
                label="Login"
                :disabled="!isValid || $auth.isLoading"
                :loading="$auth.isLoading"
            />
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
