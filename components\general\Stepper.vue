<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: Number,
    default: 1
  },
  steps: {
    type: Array as () => { text: string, isAllowed: boolean }[],
    required: true
  }
})

const emit = defineEmits(['update:model-value'])

const stepClass = (step: number, isActive: boolean) => {
  const baseClass = 'font-semibold transition cursor-pointer underline'
  const text = step === props.modelValue ? 'text-primary-500' : !isActive ? 'text-gray-400 !cursor-not-allowed' : 'text-gray-900'
  return `${baseClass} ${text}`
}
</script>

<template>
  <ol class="flex w-full">
    <li v-for="(step, i) in props.steps" class="overflow-hidden flex-1 flex flex-col items-center">
      <div class="mb-4 w-full flex items-center">
        <span class="w-full h-0.5 transition"
              :class="i === 0 ? '' : i < props.modelValue ? 'bg-primary-500' : 'bg-gray-200'"/>
        <div class="p-1 h-12 w-12 aspect-square rounded-full flex transition"
             :class="i < props.modelValue - 1 ? 'bg-primary-500': i < props.modelValue ? 'bg-[#FFEBEB]' : 'bg-[#EAECF0]'">
          <span class="h-full w-full rounded-full flex transition"
                :class="i < props.modelValue ? 'bg-primary-500' : 'bg-[#F9FAFB]'">
            <span v-if="i >= props.modelValue - 1" class="w-3 aspect-square rounded-full m-auto transition"
                  :class="i < props.modelValue ? 'bg-white' : 'bg-[#D0D5DD]'"/>
            <icon-check v-else size="24" class="stroke-white m-auto"/>
          </span>
        </div>
        <span class="w-full h-0.5 transition"
              :class="i === props.steps?.length - 1 ? '' : i < props.modelValue - 1 ? 'bg-primary-500' : 'bg-gray-200'"/>
      </div>
      <p
        :class="stepClass(i + 1, step.isAllowed)"
        @click="step.isAllowed ? emit('update:model-value', i + 1) : () => {}"
      >
        {{ step.text }}
      </p>
    </li>
  </ol>
</template>

<style scoped>

</style>