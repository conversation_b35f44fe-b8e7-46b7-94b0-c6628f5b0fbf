import {defineStore} from "pinia";
import {useAuth} from "#imports";
import api from '~/services/api'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    session: null,
    isLoading: false,
    email: '', //<EMAIL>
    password: '' //123456
  }),
  actions: {
    async login () {
      this.isLoading = true

      const matchResult = this.email
        .match(/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/)

      const email = matchResult ? this.email : ''

      const {signIn} = useAuth()

      return await signIn(
        { email, password: this.password},
        { callbackUrl: '/dashboard' }
      ).then((response) => {
        return true
      }).catch((error) => {
        return false
      }).finally(() => { this.isLoading = false })
    },
    async getAuthSession (useGo = true) {
      this.isLoading = true

      const result = await api.get('/api/v1/auth/session', { useGo })

      this.isLoading = false

      this.session = result.data
    },
    async logout () {
      this.$reset()

      this.isLoading = true

      const {signOut} = useAuth()

      return await signOut({
        callbackUrl: '/login',
      })
        .then(() => {
          return true
        })
        .catch(() => {
          return false
        })
        .finally(() => { this.isLoading = false})
    }
  }
})
