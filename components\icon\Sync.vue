<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default: '24'
  }
})
</script>

<template>
  <svg :width="props.size" :height="props.size" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M3.93744 11.8172C3.96087 10.793 4.17181 9.79922 4.56791 8.86406C4.97337 7.90313 5.55462 7.04297 6.29525 6.3C7.03587 5.55703 7.89837 4.97578 8.85931 4.57031C9.85306 4.15078 10.9078 3.9375 11.9976 3.9375C13.0874 3.9375 14.1421 4.15078 15.1335 4.57031C16.0917 4.97495 16.9618 5.56246 17.6953 6.3C17.9273 6.53203 18.1453 6.77813 18.3468 7.03594L16.9359 8.1375C16.908 8.1591 16.8867 8.18813 16.8746 8.22127C16.8624 8.25441 16.8599 8.2903 16.8672 8.32483C16.8746 8.35935 16.8915 8.39109 16.9161 8.41642C16.9407 8.44174 16.9719 8.45961 17.0062 8.46797L21.1242 9.47578C21.2413 9.50391 21.3562 9.41484 21.3562 9.29531L21.3749 5.05547C21.3749 4.89844 21.1945 4.80937 21.0726 4.90781L19.7507 5.94141C17.9484 3.63516 15.1453 2.15625 11.9952 2.15625C6.62572 2.15625 2.25697 6.45937 2.15619 11.8078C2.15556 11.8328 2.15995 11.8577 2.16909 11.881C2.17823 11.9043 2.19195 11.9255 2.20942 11.9434C2.22689 11.9613 2.24777 11.9756 2.27083 11.9853C2.29389 11.995 2.31866 12 2.34369 12H3.74994C3.85306 12 3.93509 11.918 3.93744 11.8172ZM21.6562 12H20.2499C20.1468 12 20.0648 12.082 20.0624 12.1828C20.039 13.207 19.8281 14.2008 19.432 15.1359C19.0265 16.0969 18.4453 16.9594 17.7046 17.7C16.9571 18.4506 16.0683 19.0458 15.0896 19.4513C14.1109 19.8568 13.0616 20.0645 12.0023 20.0625C10.9433 20.0645 9.89434 19.8567 8.91603 19.4513C7.93772 19.0458 7.04938 18.4506 6.30228 17.7C6.07025 17.468 5.85228 17.2219 5.65072 16.9641L7.06166 15.8625C7.08957 15.8409 7.11083 15.8119 7.12297 15.7787C7.13512 15.7456 7.13767 15.7097 7.13032 15.6752C7.12298 15.6407 7.10604 15.6089 7.08145 15.5836C7.05687 15.5583 7.02563 15.5404 6.99134 15.532L2.87337 14.5242C2.75619 14.4961 2.64134 14.5852 2.64134 14.7047L2.62494 18.9469C2.62494 19.1039 2.80541 19.193 2.92728 19.0945L4.24916 18.0609C6.0515 20.3648 8.85463 21.8438 12.0046 21.8438C17.3765 21.8437 21.7429 17.5383 21.8437 12.1922C21.8443 12.1672 21.8399 12.1423 21.8308 12.119C21.8216 12.0957 21.8079 12.0745 21.7905 12.0566C21.773 12.0387 21.7521 12.0244 21.729 12.0147C21.706 12.005 21.6812 12 21.6562 12Z" fill="white"/>
  </svg>

</template>

<style scoped>

</style>
