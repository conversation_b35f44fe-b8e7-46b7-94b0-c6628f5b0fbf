<script setup lang="ts">
import { object, string, array, boolean } from "yup";
import Plus from '~/components/icon/Plus.vue';
import GripVertical from '~/components/icon/GripVertical.vue';
import { ref, onMounted, onUnmounted, computed, reactive, watch } from 'vue';
import { useRouter } from 'vue-router';
import Draggable from 'vuedraggable';
import { useActivityCategoryStore } from '~/store/report-category'
import {toast} from 'vue3-toastify'
import BoxImage from '~/assets/image/Box.png'
import FileUpload from '~/assets/image/Upload-file.png'


const props = defineProps({
  reportCategory: {
    type: Object,
    default: null
  }
})

const store = useActivityCategoryStore()
const showDropdown = ref(false)

const inputTypes = computed(() => {
  return store.inputTypes.map(type => ({
    id: type.id,
    value: type.type,
    text: type.input_name
  }));
});

const isLocked = computed(() => {
  console.log(props.reportCategory, 'detail');
  
  return props.reportCategory?.is_have_report ?? false;
});

const handleAddInputClick = async () => {
  if (!store.inputTypes.length) {
    await store.fetchInputTypes()
  }
  showDropdown.value = !showDropdown.value
}

const emit = defineEmits(["on-success", "on-cancel"]);
const router = useRouter();

const form = reactive({
  typeIdentity: "",
  title: "",
  description: "",
  inputs: [] as Array<{
    id: string;
    type: string;
    text: string;
    description: string;
    required: boolean;
    selectedOption: string | string[];
    options: ItemCombobox[];
    file?: File;
    filePreview?: string;
    isNew: boolean;
  }>,
});

// Initialize form with report category data if in edit mode
function initializeForm() {
  if (props.reportCategory) {
    form.typeIdentity = props.reportCategory.type_identity || '';
    form.title = props.reportCategory.title || '';
    form.description = props.reportCategory.description || '';
    
    // Map category_input to form inputs
    if (props.reportCategory.category_input && Array.isArray(props.reportCategory.category_input)) {
        form.inputs = props.reportCategory.category_input.map(input => {
          const isMandatory = input.is_mandatory === "1" || 
                             input.is_mandatory === 1 || 
                             input.is_mandatory === true;
          
          return {
            id: input.id.toString(),
            type: input.input_type.type,
            text: input.title,
            description: input.description || '',
            required: isMandatory,
            selectedOption: input.options || [],
            options: input.options?.map(opt => ({
              id: opt.id.toString(),
              text: opt.text,
              value: opt.value
            })) || [],
            isNew: false
          };
        });
    }
  }
}

const obj = reactive({
  date_time: '',
})

const errors = reactive({
  title: '',
  description: ''
});

const schema = object({
  typeIdentity: string().required("Type identity field is required"),
  name: string().required("Name field is required"),
  description: string(),
  inputs: array().of(
    object({
      type: string().required(),
      label: string().required("Label is required"),
      description: string().required("Deskripsi is required"),
      required: boolean(),
      options: array().when("type", {
        is: (val: string) => val === "dropdown" || val === "multiple_choice",
        then: (schema) =>
          schema
            .of(object({
              text: string().required("Option is required"),
              value: string().required("Option is required"),
            }))
            .min(1, "At least one option is required"),
      }),
    })
  ),
});

const dropdownRef = ref<HTMLDivElement | null>(null);

const handleClickOutside = (event: MouseEvent) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    showDropdown.value = false;
  }
};

onMounted(async () => {
  document.addEventListener('click', handleClickOutside);
  await store.fetchInputTypes();
  
  if (props.reportCategory) {
    initializeForm();
  } else if (!form.inputs.length) {
    addInput('SHORT_TEXT');
  }
});

const goBack = () => {
  navigateTo('/report-category')
}

const generateRandomId = () => {
     const array = new Uint32Array(1);
     window.crypto.getRandomValues(array);
     return array[0].toString(16);
};

const addInput = (type: string) => {
  form.inputs.push({
    id: generateRandomId(),
    type,
    text: "",
    description: "",
    required: false,
    selectedOption: type === 'MULTIPLE_OPTION' ? [] : "",
    options: [],
    file: undefined,
    filePreview: undefined,
    isNew: true,
  });
  showDropdown.value = false;
};

const handleFileChange = (event: Event, input: any) => {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files.length > 0) {
    const file = target.files[0];
    input.file = file;
    input.text = file.name;

    if (input.type === 'IMAGE' && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        input.filePreview = e.target?.result as string;
      };
      reader.readAsDataURL(file);
    }
  }
};

const removeFile = (input: any) => {
  input.file = undefined;
  input.filePreview = undefined;
  input.text = '';
};

const initialForm = ref({
  typeIdentity: '',
  name: '',
  description: '',
  inputs: [],
});

const triggerModal = reactive({
  showAddOptionDialog: false,
  currentInputIndex: -1
})

const newOptions = ref([{ value: '' }]);

const openAddOptionsDialog = (index: number) => {
  triggerModal.currentInputIndex = index;

  const currentInput = form.inputs[index];
  
  if (currentInput && currentInput.options && currentInput.options.length > 0) {
    newOptions.value = currentInput.options.map(option => ({ 
      value: option.text 
    }));
  } else {
    newOptions.value = [{ value: '' }];
  }
  triggerModal.showAddOptionDialog = true;
};

const addNewOption = () => {
  newOptions.value.push({ value: '' });
};

const removeOption = (index: number) => {
  if (newOptions.value.length > 1) {
    newOptions.value.splice(index, 1);
  }
};

const saveOptions = () => {
  const validOptions = newOptions.value
    .map(option => option.value.trim())
    .filter(value => value !== '');
    
  if (validOptions.length > 0 && triggerModal.currentInputIndex !== -1) {
    const currentInput = form.inputs[triggerModal.currentInputIndex];
    
    if (currentInput) {
      const formattedOptions = validOptions.map(value => ({
        text: value,
        value: value
      }));
      
      // Update the options for the current input
      currentInput.options = formattedOptions;
      
      // Handle selection differently based on input type
      if (currentInput.type === 'SINGLE_OPTION') {
        // For single option, set first option as selected if none selected
        if (!currentInput.selectedOption || 
            !formattedOptions.some(opt => opt.value === currentInput.selectedOption)) {
          currentInput.selectedOption = formattedOptions[0].value;
        }
      } else if (currentInput.type === 'MULTIPLE_OPTION') {
        // For multiple options, initialize as empty array if not set
        if (!Array.isArray(currentInput.selectedOption)) {
          currentInput.selectedOption = [];
        } else {
          currentInput.selectedOption = (currentInput.selectedOption as string[]).filter(
            selected => formattedOptions.some(opt => opt.value === selected)
          );
        }
      }
    }
  }
  
  // Reset modal state
  triggerModal.showAddOptionDialog = false;
};

const cancelAddOptions = () => {
  triggerModal.showAddOptionDialog = false;
};

const validateForm = () => {
  let isValid = true;
  errors.title = '';
  errors.description = '';

  // Validate title
  if (!form.title || form.title.trim() === '') {
    errors.title = 'Nama Kategori harus diisi';
    isValid = false;
  }

  // Validate description
  if (!form.description || form.description.trim() === '') {
    errors.description = 'Deskripsi harus diisi';
    isValid = false;
  }

  // Validate inputs
  if (!form.inputs || form.inputs.length === 0) {
    toast.error('Minimal harus ada satu input');
    isValid = false;
  } else {
    for (let i = 0; i < form.inputs.length; i++) {
      const input = form.inputs[i];
      
      if (!input.text || input.text.trim() === '') {
        toast.error(`Judul #${i + 1} harus di isi`);
        isValid = false;
      }
    }
  }

  return isValid;
};

const handleSubmit = async () => {
  if (!validateForm()) return;

  try {
    const formData: any = {
      type_identity: form.typeIdentity,
      title: form.title,
      description: form.description,
    };

    if (props.reportCategory) {
      // For update operation, separate new and existing inputs
      const newInputs = form.inputs.filter(input => input.isNew);
      const existingInputs = form.inputs.filter(input => !input.isNew);

      if (newInputs.length > 0) {
        formData.field_input = newInputs.map(input => ({
          id: input.id,
          input_type_id: store.inputTypes.find(type => type.type === input.type)?.id,
          title: input.text,
          description: input.description,
          is_mandatory: input.required.toString(),
          input: input.options?.map(opt => opt.value) || [],
        }));
      }

      if (existingInputs.length > 0) {
        formData.update_input = existingInputs.map(input => {
          // Find the matching category input from props
          const originalInput = props.reportCategory.category_input?.find(
            cat => cat.id.toString() === input.id
          );

          return {
            id: input.id,
            category_input_id: input.id,
            title: input.text,
            description: input.description,
            is_mandatory: input.required.toString(),
            input: input.options?.map(opt => opt.value) || [],
          };
        });
      }

      // Update existing category
      await store.updateCategory(props.reportCategory.id, formData);
    } else {
      // For create operation, use field_input for all inputs
      formData.field_input = form.inputs.map(input => ({
        id: input.id,
        input_type_id: store.inputTypes.find(type => type.type === input.type)?.id,
        title: input.text,
        description: input.description,
        is_mandatory: input.required.toString(),
        input: input.options?.map(opt => opt.value) || [],
      }));

      // Create new category
      await store.createCategory(formData);
    }

    emit('on-success');
    router.push('/report-category');
  } catch (error) {
    console.error('Error submitting form:', error);
  }
};

const boxImageUrl = BoxImage;
const fileUploadUrl = FileUpload


</script>

<template>
<div>
    <p class="text-gray-500 -mt-14 mb-10">
      {{ props.reportCategory ? 'Edit data kategori laporan' : 'Isi data di bawah ini untuk menambah data kategori laporan' }}
    </p>
    
    <div class="mt-4">
      <button
        class="flex mb-5 items-center space-x-2 px-3 py-2 text-black rounded-md"
        @click="goBack"
      >
        <svg
          id="back-arrow"
          class="-ml-4"           
          height="24"
          viewBox="0 0 24 24"
          width="24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M0 0h24v24H0V0z" fill="none" opacity=".87"></path>
          <path
            d="M16.62 2.99c-.49-.49-1.28-.49-1.77 0L6.54 11.3c-.39.39-.39 1.02 0 1.41l8.31 8.31c.49.49 1.28.49 1.77 0s.49-1.28 0-1.77L9.38 12l7.25-7.25c.48-.48.48-1.28-.01-1.76z"
          ></path>
        </svg>
        <span class="font-bold text-xl">Kembali</span>
      </button>
    </div>
    <div
        class="bg-white mb-6 border px-10 pt-12 rounded-lg shadow-sm"
    >
    <p class="font-bold text-2xl">Informasi Kategori</p>
      <div class="grid gap-3 grid-cols-2 mb-2 mt-6">
        <div class="mb-7">
          <general-text-input
              id="typeName"
              v-model="form.title"
              label="Nama Kategori"
              class="p-0"
              placeholder="Cth: Pembelian Barang"
              :disabled="isLocked"
              @update:model-value="() => form.typeIdentity = form.title.replace(/[^a-zA-Z0-9]/g, '').toUpperCase()"
              required
          />
          <p v-if="errors.title" class="text-red-500 text-sm mt-1">{{ errors.title }}</p>
          <p class="p-0 text-gray-500">Nama kategori tidak boleh sama</p>
        </div>
        <div>
          <general-textarea
              id="typeDescription"
              v-model="form.description"
              label="Deskripsi"
              required
             :disabled="isLocked"
              placeholder="Cth: Tipe penugasan ini di gunakan untuk ..."
              class="mb-5"
          />
          <p v-if="errors.description" class="text-red-500 text-sm mt-1">{{ errors.description }}</p>
        </div>
      </div>
    </div>
    <div
        class="bg-white mb-6 border px-10 pt-12 rounded-lg shadow-sm"
    >
    <div class="flex justify-between">
      <p class="font-bold text-2xl">Daftar Input laporan</p>
    </div>
        <div class="mt-8 space-y-6 mb-8">
            <Draggable
              v-model="form.inputs"
              item-key="id"
              handle=".drag-handle"
              :animation="200"
              ghost-class="ghost"
              :disabled="isLocked"
            >
                <template #item="{ element: input, index }">
                    <div class="p-4 border mb-8 rounded-lg bg-white relative group transition-all">
                        <div class="flex justify-between items-center mb-4">
                            <div class="flex items-center gap-2">
                            </div>
                        </div>
                        <button
                            v-if="input.isNew"  
                            @click="form.inputs.splice(index, 1)"
                            class="absolute top-4 right-4 text-red-500 opacity-0 group-hover:opacity-100 transition-all duration-200 hover:text-red-600"
                            title="Hapus input"
                        >
                        <icon-trash
                          color="#F36A6A"
                          size="25"
                        />
                        </button>
                        <div v-if="input.type === 'SHORT_TEXT'">
                          <div class="flex">
                            <div class="flex items-center">
                                <button 
                                    v-if="!isLocked && input.isNew" 
                                  class="drag-handle cursor-move text-gray-400 hover:text-gray-600 transition-colors mr-4 mt-4"
                                >
                                  <GripVertical size="25" />
                                </button>
                            </div>
                            <div class="w-full">
                              <div class="flex mb-2" >
                                <input v-model="input.text" placeholder="Judul" class="p-0" :readonly="isLocked && !input.isNew" /> <span class="text-primary-500">*</span>
                              </div>
                              <general-text-input
                                  :id="'input-label-' + index"
                                  v-model="input.description"
                                  placeholder="Cth: Pemeliharaan Rutin Mesin Produksi di PT XYZ"
                                  required
                                  :disabled="isLocked && !input.isNew"
                              > 
                            </general-text-input>
                            </div>
                          </div>
                          <div class="mt-4 ml-9">
                            <general-checkbox
                                v-model="input.required"
                                :checked="input.required"
                                :id="'checkbox-required-' + index"
                                :value="'required-' + index"
                                :name="'required-' + index"
                                label="Wajib diisi"
                            />
                        </div>
                        </div>
                        <div v-if="input.type === 'LONG_TEXT'">
                          <div class="flex">
                            <div class="flex items-center">
                                <button 
                                  v-if="!isLocked && input.isNew" 
                                  class="drag-handle cursor-move text-gray-400 hover:text-gray-600 transition-colors mr-4 mt-4"
                                >
                                  <GripVertical size="25" />
                                </button>
                            </div>
                            <div class="w-full">
                              <div class="flex mb-2" >
                                <input v-model="input.text" :readonly="isLocked && !input.isNew" placeholder="Judul" class="p-0" /> <span class="text-primary-500">*</span>
                              </div>
                              <general-textarea
                                  :id="'input-description-' + index"
                                  v-model="input.description"
                                  required
                                  placeholder="cth: Laporan untuk ... "
                                  class="mb-5"
                                  :disabled="isLocked && !input.isNew"
                              />
                            </div>
                          </div>
                          <div class="mt-4 ml-9">
                            <general-checkbox
                                v-model="input.required"
                                :checked="input.required"
                                :id="'checkbox-required-' + index"
                                :value="'required-' + index"
                                :name="'required-' + index"
                                label="Wajib diisi"
                            />
                          </div>
                        </div>
                        <div v-if="input.type === 'SINGLE_OPTION'">
                          <div class="flex">
                            <div class="flex items-center">
                                <button 
                                  v-if="!isLocked && input.isNew" 
                                  class="drag-handle cursor-move text-gray-400 hover:text-gray-600 transition-colors mr-4 mt-4"
                                >
                                  <GripVertical size="25" />
                                </button>
                            </div>
                            <div class="w-full">
                              <div class="flex mb-2" >
                                <input v-model="input.text" :readonly="isLocked && !input.isNew" placeholder="Judul" class="p-0" /> <span class="text-primary-500">*</span>
                              </div>
                              <div class="flex gap-2">
                                <input
                                  v-model="input.selectedOption"
                                  id-target="dropdownOption"
                                  id-trigger="triggerDropdownOption"
                                  plain
                                  readonly
                                  :items="input.options"
                                  :disabled="isLocked && !input.isNew"
                                  placeholder="Pilih opsi"
                                  class="mb-6 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 w-full p-2.5 dark:focus:border-blue-500"
                                />
                              </div>
                            </div>
                          </div>
                          <div class="flex justify-between mt-4 ml-9">
                            <general-checkbox
                              v-model="input.required"
                              :checked="input.required"
                              :id="'checkbox-required-' + index"
                              :value="'required-' + index"
                              :name="'required-' + index"
                              label="Wajib diisi"
                            />
                            <div 
                              class="flex gap-1" 
                              :class="{ 'cursor-pointer': !isLocked }"
                              @click="(!isLocked || input.isNew) && openAddOptionsDialog(index)"
                            >
                               <p :class="{ 'underline text-bold-500': !isLocked, 'text-gray-500': isLocked }">Tambah Pilihan</p><span class="text-primary-500">({{ input.options.length }})</span>
                            </div>
                          </div>
                        </div>
                        <div v-if="input.type === 'MULTIPLE_OPTION'">
                          <div class="flex">
                            <div class="flex items-center">
                                <button 
                                  v-if="!isLocked && input.isNew" 
                                  class="drag-handle cursor-move text-gray-400 hover:text-gray-600 transition-colors mr-4 mt-4"
                                >
                                  <GripVertical size="25" />
                                </button>
                            </div>
                            <div class="w-full">
                              <div class="flex mb-2" >
                                <input v-model="input.text" :readonly="isLocked && !input.isNew" placeholder="Judul" class="p-0" /> <span class="text-primary-500">*</span>
                              </div>
                              <div class="flex gap-2">
                                <input
                                  v-model="input.selectedOption"
                                  id-target="dropdownOption"
                                  id-trigger="triggerDropdownOption"
                                  plain
                                  readonly
                                  :items="input.options"
                                  :disabled="isLocked && !input.isNew"
                                  placeholder="Pilih opsi"
                                  class="mb-6 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 w-full p-2.5 dark:focus:border-blue-500"
                                />
                              </div>
                            </div>
                          </div>
                          <div class="flex justify-between mt-4 ml-9">
                            <general-checkbox
                              v-model="input.required"
                              :checked="input.required"
                              :id="'checkbox-required-' + index"
                              :value="'required-' + index"
                              :name="'required-' + index"
                              label="Wajib diisi"
                            />
                            <div 
                              class="flex gap-1" 
                              :class="{ 'cursor-pointer': !isLocked }"
                               @click="(!isLocked || input.isNew) && openAddOptionsDialog(index)"
                            >
                               <p :class="{ 'underline text-bold-500': !isLocked, 'text-gray-500': isLocked }">Tambah Pilihan</p><span class="text-primary-500">({{ input.options.length }})</span>
                            </div>
                          </div>
                        </div>
                        <div v-if="input.type === 'LOCATION'">
                          <div class="flex">
                            <div class="flex items-center">
                                <button 
                                  v-if="!isLocked && input.isNew" 
                                  class="drag-handle cursor-move text-gray-400 hover:text-gray-600 transition-colors mr-4 mt-4"
                                >
                                  <GripVertical size="25" />
                                </button>
                            </div>
                            <div class="w-full">
                              <div class="flex mb-2" >
                                <input v-model="input.text" :readonly="isLocked && !input.isNew" placeholder="Lokasi" class="p-0" /> <span class="text-primary-500">*</span>
                              </div>
                              <general-text-input
                                  :id="'input-label-' + index"
                                  v-model="input.description"
                                  placeholder="Lokasi"
                                  :disabled="isLocked && !input.isNew"
                                  required
                              />
                            </div>
                          </div>
                          <div class="mt-4 ml-9">
                            <general-checkbox
                              v-model="input.required"
                              :checked="input.required"
                              :id="'checkbox-required-' + index"
                              :value="'required-' + index"
                              :name="'required-' + index"
                              label="Wajib diisi"
                            />
                          </div>
                        </div>
                        <div v-if="input.type === 'DATE_TIME'">
                          <div class="flex">
                            <div class="flex items-center">
                                <button 
                                  v-if="!isLocked && input.isNew" 
                                  class="drag-handle cursor-move text-gray-400 hover:text-gray-600 transition-colors mr-4 mt-4"
                                >
                                  <GripVertical size="25" />
                                </button>
                            </div>
                            <div class="w-full">
                              <div class="flex mb-2" >
                                <input v-model="input.text" :readonly="isLocked && !input.isNew" placeholder="Tanggal & waktu" class="p-0" /> <span class="text-primary-500">*</span>
                              </div>
                              <general-text-input
                                v-model="input.description"
                                placeholder="Pilih tanggal & waktu "
                                :disabled="isLocked && !input.isNew"
                                required
                              />
                            </div>
                          </div>
                          <div class="mt-4 ml-9">
                            <general-checkbox
                              v-model="input.required"
                              :checked="input.required"
                              :id="'checkbox-required-' + index"
                              :value="'required-' + index"
                              :name="'required-' + index"
                              label="Wajib diisi"
                            />
                          </div>
                        </div>
                        <div v-if="input.type === 'FILE'">
                          <div class="flex">
                            <div class="flex items-center">
                                <button 
                                  v-if="!isLocked && input.isNew" 
                                  class="drag-handle cursor-move text-gray-400 hover:text-gray-600 transition-colors mr-4 mt-4"
                                >
                                  <GripVertical size="25" />
                                </button>
                            </div>
                            <div class="w-full">
                              <div class="flex mb-2">
                                <input v-model="input.text" :readonly="isLocked && !input.isNew" placeholder="Upload file" class="p-0" />
                                <span class="text-primary-500">*</span>
                              </div>
                              <div class="mt-4">
                                <div class="relative group">
                                  <img
                                    :src="fileUploadUrl"
                                    class="max-w-xs h-32 object-contain rounded-lg border border-gray-200"
                                    alt="Tanda Tangan"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="mt-4 ml-9">
                            <general-checkbox
                                v-model="input.required"
                                :checked="input.required"
                                :id="'checkbox-required-' + index"
                                :value="'required-' + index"
                                :name="'required-' + index"
                                label="Wajib diisi"
                            />
                          </div>
                        </div>
                        <div v-if="input.type === 'IMAGE'">
                          <div class="flex">
                            <div class="flex items-center">
                              <button  v-if="!isLocked && input.isNew"   class="drag-handle cursor-move text-gray-400 hover:text-gray-600 transition-colors mr-4 mt-4">
                                      <GripVertical size="25" />
                                  </button>
                            </div>
                            <div class="w-full">
                              <div class="flex mb-2">
                                <input v-model="input.text" :readonly="isLocked && !input.isNew" placeholder="Tanda Tangan Bukti" class="p-0" />
                                <span class="text-primary-500">*</span>
                              </div>
                              <div class="mt-4">
                                <div class="relative group">
                                  <img
                                    :src="boxImageUrl"
                                    class="max-w-xs h-32 object-contain rounded-lg border border-gray-200"
                                    alt="Tanda Tangan"
                                  />
                                </div>
                                <p class="text-sm text-gray-500 mt-2">Tanda Tangan</p>
                              </div>
                            </div>
                          </div>
                          <div class="mt-4 ml-9">
                            <general-checkbox
                              v-model="input.required"
                              :checked="input.required"
                              :id="'checkbox-required-' + index"
                              :value="'required-' + index"
                              :name="'required-' + index"
                              label="Wajib diisi"
                            />
                          </div>
                        </div>
                    </div>
                </template>
            </Draggable>

            <div class="relative mb-5" ref="dropdownRef">
            <general-button
                    label="Tambah Input Laporan"
                    @click="handleAddInputClick"
                >
                    <template #prefix>
                        <Plus size="20"/>
                    </template>
                </general-button>
                <div v-if="showDropdown" class="absolute left-0 z-50 bg-white border rounded-lg shadow-lg p-2 w-56 mt-2">
                <button
                  v-for="type in inputTypes"
                  :key="type.value"
                  class="w-full text-left px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors duration-150 text-sm flex items-center gap-2"
                  @click="addInput(type.value)"
                >
                  <span class="flex-shrink-0 w-6 h-6 flex items-center justify-center text-gray-600">
                    <svg v-if="type.value === 'SHORT_TEXT'" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <line x1="3" y1="6" x2="15" y2="6"></line>
                      <line x1="3" y1="12" x2="9" y2="12"></line>
                    </svg>
                    <svg v-else-if="type.value === 'LONG_TEXT'" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <line x1="3" y1="6" x2="21" y2="6"></line>
                      <line x1="3" y1="12" x2="21" y2="12"></line>
                      <line x1="3" y1="18" x2="21" y2="18"></line>
                    </svg>
                    <svg v-else-if="type.value === 'SINGLE_OPTION'" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                    <svg v-else-if="type.value === 'MULTIPLE_OPTION'" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                    </svg>
                    <svg v-else-if="type.value === 'LOCATION'" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                      <circle cx="12" cy="10" r="3"></circle>
                    </svg>
                    <svg v-else-if="type.value === 'DATE_TIME'" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                      <line x1="16" y1="2" x2="16" y2="6"></line>
                      <line x1="8" y1="2" x2="8" y2="6"></line>
                      <line x1="3" y1="10" x2="21" y2="10"></line>
                    </svg>
                    <svg v-else-if="type.value === 'FILE'" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                      <polyline points="14 2 14 8 20 8"></polyline>
                      <line x1="12" y1="18" x2="12" y2="12"></line>
                      <line x1="9" y1="15" x2="15" y2="15"></line>
                    </svg>
                    <svg v-else-if="type.value === 'IMAGE'" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                      <circle cx="8.5" cy="8.5" r="1.5"></circle>
                      <polyline points="21 15 16 10 5 21"></polyline>
                    </svg>
                  </span>
                  {{ type.text }}
                </button>
              </div>
            </div>
        </div>
        <div>
          <transition name="fade">
            <div
              v-if="triggerModal.showAddOptionDialog"
              class="fixed top-0 left-0 w-full h-full flex items-center justify-center z-50"
            >
              <div
                class="bg-black bg-opacity-50 absolute top-0 left-0 w-full h-full"
                @click="triggerModal.showAddOptionDialog = false"
              ></div>
              <div
                class="p-4 bg-white rounded-lg shadow-lg relative w-[500px]"
              >
              <div class="p-2">
                <div class="p-6">
                  <h2 class="text-xl -ml-5 font-bold mb-2">Tambah Pilihan</h2>
                </div>
                <div class="p-2 border">
                  <div class="space-y-4 px-6">
                    <div v-for="(option, index) in newOptions" :key="index" class="flex gap-2 mt-4">
                      <input
                        v-model="option.value"
                        type="text"
                        placeholder="Masukkan pilihan"
                        class="w-full p-2 border rounded"
                      />
                      <button
                        @click="removeOption(index)"
                        class="text-red-500 hover:text-red-700"
                      >
                      <icon-close class="stroke-black" />
                      </button>
                    </div>
          
                    <div class="m-0 mr-7">
                      <general-outlined-button
                          class="w-full text-red-500 mb-8"
                          type="button"
                          label="Tambah Opsi"
                          @click="addNewOption"
                        />
                    </div>
                  </div>
                </div>

                <div class="justify-between mt-5">
                      <div class="flex gap-2">
                        <general-button
                          type="button"
                          label="Batal"
                          @click="cancelAddOptions"
                        />
                        <general-button
                          type="button"
                          label="Tambah"
                          @click="saveOptions"
                        />
                      </div>
                    </div>
              </div>
              </div>
            </div>
          </transition>
        </div>
    </div>
    
    <div class="flex space-x-3">
      <general-button
        :label="props.reportCategory ? 'Update' : 'Submit'"
        class="w-52"
        :loading="store.$state.isLoading.form"
        @click="handleSubmit"
      />
      <general-button
        label="Batal"
        variant="secondary"
        class="w-52"
        @click="goBack"
      />
    </div>
  </div>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}

.ghost {
  opacity: 0.5;
  background: #f3f4f6;
  border: 2px dashed #9ca3af;
}

.group:hover {
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
}
</style>
