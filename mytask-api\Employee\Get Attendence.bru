meta {
  name: Get Attendence
  type: http
  seq: 6
}

get {
  url: {{base_url}}/employee/get-attendence
  body: none
  auth: bearer
}

query {
  ~perpage: 5
  ~page: 2
  ~query: pujud
  ~field: name
  ~sort: desc
  ~from: 2021-11-01
  ~to: 2021-11-30
}

auth:bearer {
  token: {{token}}
}

script:pre-request {
  // 
}

tests {
  // if(pm.response.code == 200) {
  //     pm.test("Status code is 200");
  // } else if(pm.response.code == 401) {
  //     pm.test("Status code is 401");
  // }
  // 
  // var getParams = pm.request.url.query.all();
  // if(getParams) {
  //     getParams.forEach(function(getParam){
  //         pm.test("Param value = " + getParam.value);
  //     });   
  // }
  //  
}
