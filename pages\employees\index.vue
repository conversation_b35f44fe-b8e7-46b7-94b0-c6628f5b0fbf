<script setup lang="ts">
import {usePageStore} from "~/store/page";
import {watchDebounced} from "@vueuse/shared";
import {useRouter} from "vue-router";
import {useRoute} from "#app";
import {useEmployeesStore} from "~/store/employees";

const $page = usePageStore()
const $employees = useEmployeesStore()
const $router = useRouter()
const $route = useRoute()

const initialQuery = {
  query: '',
  field: 'name',
  sort: 'desc',
  status: '',
  from: '',
  to: '',
  page: 1,
}

const query = ref(initialQuery)

onMounted(() => {
  $page.setTitle('Karyawan')

  if (Object.keys($route.query).length > 0) {
    query.value = {...$route.query} as any
  }

  // $router.push({query: query.value})
  fetchListEmployee()
})

watchDebounced(() => query.value.query, () => {
  fetchListEmployee()
}, {debounce: 300})

watch(() => query, (value) => {
  $router.push({value})
}, {deep: true})

watch(() => query.value.page, () => {
  fetchListEmployee()
})

function resetQuery() {
  query.value = {...initialQuery}
}

function fetchListEmployee() {
  $employees.fetchListEmployee({...query.value})
}

watch(() => query.value.query, (newQuery) => {
  if (newQuery) {
    query.value.page = 1; 
  }
});
</script>


<template>
  <div>
    <!--    <general-modal-->
    <!--        is-has-close-->
    <!--        id="modal-detail-task"-->
    <!--        @mounted="modalDetailLocation = $event"-->
    <!--        @modal-opened="dispatchResizeEvent()"-->
    <!--    >-->
    <!--      <template #icon>-->
    <!--        <icon-marker-pin class="stroke-primary-500"/>-->
    <!--      </template>-->
    <!--      <template #body>-->
    <!--        <detail-task-->
    <!--            :task="selectedTask"-->
    <!--            @on-click-update="isAddingLocation = false ;modalDetailLocation?.hide(); modalFormLocations?.show()"-->
    <!--            @on-click-delete="modalDetailLocation?.hide(); modalConfirmDelete?.show()"-->
    <!--        />-->
    <!--      </template>-->
    <!--    </general-modal>-->

    <div class="bg-white border rounded-lg shadow-sm overflow-hidden">
      <div class="w-full p-6 flex space-x-3 justify-between">
        <general-text-input v-model="query.query" clearable id="inputSearchEmployee" placeholder="Search">
          <template #prefix>
            <icon-search size="20"/>
          </template>
        </general-text-input>

<!--        <general-outlined-button v-show="false" label="Filters">-->
<!--          <template #prefix>-->
<!--            <icon-filter-lines size="20"/>-->
<!--          </template>-->
<!--        </general-outlined-button>-->

        <div class="flex space-x-3">
          <app-export-button @on-click-export-excel="$employees.fetchExportExcel()" @on-click-export-pdf="$employees.fetchExportPDF()"/>

          <general-button
              id="btnSyncAttendances"
              label="Sinkronisasi Data"
              :loading="$employees.isLoading.sync"
              @on-click="$employees.fetchSyncEmployee()"
          >
            <template #prefix>
              <icon-sync size="20"/>
            </template>
          </general-button>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="w-full text-sm text-left" style="empty-cells: show;">
          <thead class="text-xs text-gray-700 bg-gray-50 border-b">
          <tr>
            <th scope="col" class="px-6 py-3 font-medium">
              No
            </th>
            <th scope="col" class="px-6 py-3 font-medium">
              Nama Karyawan
            </th>
            <th scope="col" class="px-6 py-3 font-medium">
              IMEI
            </th>
            <th scope="col" class="px-6 py-3 font-medium">
              Device ID
            </th>
            <th scope="col" class="px-6 py-3 font-medium">
              No Sim Card
            </th>
            <th scope="col" class="px-6 py-3 font-medium">
              Tugas Selesai
            </th>
            <th scope="col" class="px-6 py-3 font-medium">
              Tugas Tidak Selesai
            </th>
            <th scope="col" class="px-6 py-3 font-medium"/>
          </tr>
          </thead>
          <tbody>
          <tr v-if="$employees.$state.isLoading.list">
            <td></td>
            <td></td>
            <td></td>
            <td class="h-24 flex items-center justify-center">
              <icon-circle-loading/>
            </td>
            <td></td>
          </tr>
          <tr v-else v-for="(employee, i) in $employees.listEmployee" :key="employee.id" class="bg-white border-b">
            <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
              {{ getDataTableNumber(i, query.page) }}
            </td>
            <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
              {{ employee.name }}
            </td>
            <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
              {{ employee.imei }}
            </td>
            <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
              {{ employee.device_id }}
            </td>
            <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
              {{ employee.sim_number }}
            </td>
            <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
              {{ employee.task_completed }}
            </td>
            <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">
              {{ employee.task_uncompleted }}
            </td>
            <td class="px-6 py-4 font-normal text-gray-900 whitespace-nowrap">

            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
    <general-pagination
        v-model="query.page"
        :active-page="$employees.meta?.page"
        :total-page="Math.ceil($employees.meta?.total/$employees.meta?.perpage)"
    />
  </div>
</template>

<style scoped>

</style>
