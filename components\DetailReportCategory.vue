<script setup lang="ts">
import { useActivityCategoryStore } from '~/store/report-category'
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import BoxImage from '~/assets/image/Box.png'
import FileUpload from '~/assets/image/Upload-file.png'

const props = defineProps<{
  id: string | string[]
  isPublic?: boolean
}>()

const $router = useRouter()
const $reportCategory = useActivityCategoryStore()
const isEditMode = ref(false)
const isLoading = ref(false)

async function fetchDetail() {
  try {
    await $reportCategory.fetchDetail(Number(props.id))
  } catch (error) {
    console.error('An error occurred:', error)
  }
}

async function toggleStatus() {
  try {
    if ($reportCategory.detailActivityCategory) {
      await $reportCategory.updateStatusCategory(
        $reportCategory.detailActivityCategory.id,
        $reportCategory.detailActivityCategory.status
      )
      await fetchDetail()
    }
  } catch (error) {
    console.error('Error toggling status:', error)
  }
}

function getStatusText(status: 'ACTIVE' | 'INACTIVE') {
  return status === 'ACTIVE' ? 'Aktif' : 'Tidak Aktif'
}

function getStatusColor(status: 'ACTIVE' | 'INACTIVE') {
  return status === 'ACTIVE' ? 'text-green-600' : 'text-red-600'
}

function goBack() {
  navigateTo('/report-category')
}

async function goToEdit() {
  isLoading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    isEditMode.value = true
    window.location.hash = 'edit'
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  fetchDetail()
  if (window.location.hash === '#edit') {
    goToEdit()
  }
})

watch(() => window.location.hash, (newHash) => {
    if (newHash !== '#edit') {
    isEditMode.value = false
  }
})

const boxImageUrl = BoxImage;
const fileUploadUrl = FileUpload


</script>

<template>
  <div v-if="isLoading" class="flex justify-center items-center min-h-screen">
    <general-loading />
  </div>
  <div v-else-if="!isEditMode">
    <p class="text-gray-500 -mt-14 mb-10">
      Detail data kategori laporan
    </p>

    <div class="mt-4">
      <button
        class="flex mb-5 items-center space-x-2 px-3 py-2 text-black rounded-md"
        @click="goBack"
      >
        <svg
          id="back-arrow"
          class="-ml-4"           
          height="24"
          viewBox="0 0 24 24"
          width="24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M0 0h24v24H0V0z" fill="none" opacity=".87"></path>
          <path
            d="M16.62 2.99c-.49-.49-1.28-.49-1.77 0L6.54 11.3c-.39.39-.39 1.02 0 1.41l8.31 8.31c.49.49 1.28.49 1.77 0s.49-1.28 0-1.77L9.38 12l7.25-7.25c.48-.48.48-1.28-.01-1.76z"
          ></path>
        </svg>
        <span class="font-bold text-xl">Kembali</span>
      </button>
    </div>

    <div v-if="$reportCategory.$state.isLoading.detail" class="flex justify-center">
      <general-loading />
    </div>

    <template v-else>
      <div>
        <div class="bg-white mb-6 border px-10 pt-12 rounded-lg shadow-sm">
          <p class="font-bold text-2xl">Informasi Kategori</p>
          <div class="grid gap-3 grid-cols-2 mb-2 mt-6">
            <div class="mb-7">
              <label class="block mb-1.5 text-sm font-[600] text-gray-700">Nama Kategori</label>
              <div class="mt-2">
                <p class="text-base text-gray-900">
                  {{ $reportCategory.detailActivityCategory?.title }}
                </p>
              </div>
            </div>
            <div>
              <label class="block mb-1.5 text-sm font-[600] text-gray-700">Deskripsi</label>
              <div class="mt-2">
                <p class="text-base text-gray-900">
                  {{ $reportCategory.detailActivityCategory?.description || '-' }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white mb-6 border px-10 pt-12 rounded-lg shadow-sm">
          <div class="flex justify-between mb-4">
          <p class="font-bold text-2xl mb-6">Daftar Input Laporan</p>
          <button
            class="flex items-center gap-2 px-4 py-2 text-white bg-red-600 hover:bg-red-700 rounded-lg"
            @click="goToEdit"
          >
            <icon-edit />
            <span>Edit</span>
          </button>
          </div>
          <div class="space-y-4">
            <template v-for="(input, index) in $reportCategory.detailActivityCategory?.category_input" :key="index">
             <div class="p-3 m-0">
                <div class="p-4 border rounded-lg bg-white">
                <div v-if="input.input_type?.type === 'SHORT_TEXT'">
                  <div class="flex">
                    <div class="w-full">
                      <div class="flex mb-2">
                        <input v-model="input.title" placeholder="Judul" class="p-0 w-full" disabled /> 
                      </div>
                      <general-text-input
                        :id="'input-label-' + index"
                        v-model="input.description"
                        placeholder="Cth: Pemeliharaan Rutin Mesin Produksi di PT XYZ"
                        disabled
                      />
                    </div>
                  </div>
                </div>

                <div v-if="input.input_type?.type === 'LONG_TEXT'">
                  <div class="flex">
                    <div class="w-full">
                      <div class="flex mb-2">
                        <input v-model="input.title" placeholder="Judul" class="p-0 w-full" disabled />
                      </div>
                      <general-textarea
                        :id="'input-description-' + index"
                        v-model="input.description"
                        class="mb-5"
                        disabled
                      />
                    </div>
                  </div>
                </div>

                <div v-if="input.input_type?.type === 'SINGLE_OPTION' || input.input_type?.type === 'MULTIPLE_OPTION'">
                  <div class="flex">
                    <div class="w-full">
                      <div class="flex mb-2">
                        <input v-model="input.title" placeholder="Judul" class="p-0 w-full" disabled />
                      </div>
                      <div class="flex gap-2">
                        <general-combobox
                          placeholder="Pilih opsi"
                          class="w-full"
                          :items="input.options"
                          disabled
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div v-if="input.input_type?.type === 'LOCATION'">
                  <div class="flex">
                    <div class="w-full">
                      <div class="flex mb-2">
                        <input v-model="input.title" placeholder="Judul" class="p-0 w-full" disabled />
                      </div>
                      <general-text-input
                        :id="'input-location-' + index"
                        v-model="input.description"
                        placeholder="Cth: Lokasi pengerjaan"
                        disabled
                      />
                    </div>
                  </div>
                </div>

                <div v-if="input.input_type?.type === 'FILE'">
                  <div class="flex">
                    <div class="w-full">
                      <div class="flex mb-2">
                        <input v-model="input.title" placeholder="Judul" class="p-0 w-full" disabled />
                      </div>
                      <div class="mt-4">
                        <div class="relative group">
                          <img
                            :src="fileUploadUrl"
                            class="max-w-xs h-32 object-contain rounded-lg border border-gray-200"
                            alt="Tanda Tangan"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div v-if="input.input_type?.type === 'DATE_TIME'">
                  <div class="flex">
                    <div class="w-full">
                      <div class="flex mb-2">
                        <input v-model="input.title" placeholder="Judul" class="p-0 w-full" disabled />
                      </div>
                      <general-text-input
                        v-model="input.description"
                        placeholder="Pilih tanggal dan waktu"
                        disabled
                      />
                    </div>
                  </div>
                </div>

                <div v-if="input.input_type?.type === 'IMAGE'">
                  <div class="flex">
                    <div class="w-full">
                      <div class="flex mb-2">
                        <input v-model="input.title" placeholder="Judul" class="p-0 w-full" disabled />
                      </div>
                      <div class="mt-4">
                        <div class="relative group">
                          <img
                            :src="boxImageUrl"
                            class="max-w-xs h-32 object-contain rounded-lg border border-gray-200"
                            alt="Tanda Tangan"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
             </div>
            </template>
          </div>
        </div>
      </div>
    </template>
  </div>

  <div v-else>
    <forms-form-category-report 
      :report-category="$reportCategory.detailActivityCategory"
      @on-cancel="goBack"
      @on-success="fetchDetail(); goBack()"
    />
  </div>
</template>

<style scoped>
</style>
