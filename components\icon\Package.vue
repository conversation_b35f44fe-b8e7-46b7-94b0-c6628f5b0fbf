<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default: '24'
  }
})
</script>

<template>
  <svg xmlns="http://www.w3.org/2000/svg" :width="props.size" :height="props.size" viewBox="0 0 25 25" fill="none">
    <path d="M21 7.77832L12.5 12.5005M12.5 12.5005L3.99997 7.77832M12.5 12.5005L12.5 22.0006M21.5 16.5591V8.44202C21.5 8.09937 21.5 7.92805 21.4495 7.77526C21.4049 7.64008 21.3318 7.516 21.2354 7.41131C21.1263 7.29297 20.9766 7.20977 20.677 7.04337L13.277 2.93225C12.9934 2.7747 12.8516 2.69592 12.7015 2.66503C12.5685 2.6377 12.4315 2.6377 12.2986 2.66503C12.1484 2.69592 12.0066 2.7747 11.723 2.93226L4.32297 7.04337C4.02345 7.20977 3.87369 7.29297 3.76463 7.41131C3.66816 7.516 3.59515 7.64008 3.55048 7.77526C3.5 7.92806 3.5 8.09938 3.5 8.44202V16.5591C3.5 16.9018 3.5 17.0731 3.55048 17.2259C3.59515 17.3611 3.66816 17.4851 3.76463 17.5898C3.87369 17.7082 4.02345 17.7914 4.32297 17.9578L11.723 22.0689C12.0066 22.2264 12.1484 22.3052 12.2986 22.3361C12.4315 22.3634 12.5685 22.3634 12.7015 22.3361C12.8516 22.3052 12.9934 22.2264 13.277 22.0689L20.677 17.9578C20.9766 17.7914 21.1263 17.7082 21.2354 17.5898C21.3318 17.4851 21.4049 17.3611 21.4495 17.2259C21.5 17.0731 21.5 16.9018 21.5 16.5591Z" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M17 10L8 5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>
</template>

<style scoped>

</style>