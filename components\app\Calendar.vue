<script setup lang="ts">
import {ref} from 'vue'
import {addDays, addMonths, endOfWeek, isSameMonth, isSameYear, startOfWeek, subDays, subMonths} from "date-fns";

const props = defineProps({
  modelValue: {
    type: Date,
    default: new Date(),
    required: true
  },
  variant: {
    type: String as () => 'sm' | 'lg',
    default: 'lg'
  },
  events: {
    type: Array as () => any[],
    default: []
  },
  isClickable: {
    type: Boolean,
    default: false
  },
  isHasChangeMode: {
    type: Boolean,
    default: true
  },
  currentMonthYear: {
    type: Object as () => { month: number, year: number } | null,
    default: null
  }
})

const emit = defineEmits(['update:model-value', 'on-click-more', 'on-click-event', 'on-change-month-year'])

const now = new Date()
const currentMonth = ref<number>(now.getMonth())
const currentYear = ref<number>(now.getFullYear())
const selectedCalendarMode = ref<'WEEK' | 'MONTH' | 'YEAR'>('MONTH')

const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December']
const calendarModes = ['Month', 'Week']

const formattedMonthYear = computed(() => {
  let monthYear = ''

  if (selectedCalendarMode.value === 'WEEK') {
    const startDay = startOfWeek(props.modelValue)
    const endDay = endOfWeek(props.modelValue)
    const sameMonth = isSameMonth(startDay, endDay)
    const sameYear = isSameYear(startDay, endDay)

    monthYear = `${startDay.getDate()} ${sameMonth ? '' : monthNames[startDay.getMonth()]} ${sameYear ? '' : startDay.getFullYear()} - ${endDay.getDate()} ${monthNames[endDay.getMonth()]} ${endDay.getFullYear()}`
  } else if (selectedCalendarMode.value === 'MONTH') {
    monthYear = `${monthNames[currentMonth.value]} ${currentYear.value}`
  }

  return monthYear
})

watch(() => props.modelValue, (date: Date) => {
  currentMonth.value = date.getMonth()
  currentYear.value = date.getFullYear()
})

watch(() => props.currentMonthYear, (value: { month: number, year: number } | null) => {
  if (value) {
    currentMonth.value = value.month
    currentYear.value = value.year
  }
})

watch([currentMonth, currentYear], (currentValue) => {
  emit('on-change-month-year', {
    month: currentValue[0],
    year: currentValue[1]
  })
})

function changeWeek(type: 'NEXT' | 'PREV') {
  const newDate = type === 'NEXT'
    ? addDays(props.modelValue, 7)
    : subDays(props.modelValue, 7)

  emit('update:model-value', newDate)
}

function changeMonth(type: 'NEXT' | 'PREV') {
  const currentDate = new Date(currentYear.value, currentMonth.value, 1)
  const newDate = type === 'NEXT'
    ? addMonths(currentDate, 1)
    : subMonths(currentDate, 1)

  currentMonth.value = newDate.getMonth()
  currentYear.value = newDate.getFullYear()
}

function onClickChange(type: 'NEXT' | 'PREV') {
  switch (selectedCalendarMode.value) {
    case 'WEEK':
      changeWeek(type);
      break
    case 'MONTH':
      changeMonth(type);
      break
  }
}

const setToday = () => {
  const today = new Date()
  currentMonth.value = today.getMonth()
  currentYear.value = today.getFullYear()
  emit('update:model-value', today)
}
</script>

<template>
  <div class="calendar">
    <div class="calendar-header flex justify-between items-center p-4" :class="variant === 'lg' ? 'border-b' : ''">
      <div class="flex items-center space-x-4 flex-1">
        <slot name="action-button"/>

        <div v-if="props.isHasChangeMode" class="flex space-x-1">
          <general-icon-button class="!w-8 !h-8 !border-none" @on-click="onClickChange('PREV')">
            <template #icon>
              <icon-chevron-left size="24" class="stroke-gray-900"/>
            </template>
          </general-icon-button>
          <general-icon-button class="!w-8 !h-8 !border-none" @on-click="onClickChange('NEXT')">
            <template #icon>
              <icon-chevron-right size="24" class="stroke-gray-900"/>
            </template>
          </general-icon-button>
        </div>

        <p class="flex-1" :class="variant === 'lg' ? 'text-3xl' : ''">
          {{ formattedMonthYear }}
        </p>
      </div>

      <div class="flex flex-1 items-center justify-end space-x-4">
        <general-combobox
          v-if="props.isHasChangeMode"
          v-model="selectedCalendarMode"
          id-trigger="selectMode"
          id-target="modes"
          is-inline-add
          :items="calendarModes.map(mode => ({
            text: mode,
            value: mode.toUpperCase()
          }))"
        />

        <div v-if="variant === 'lg'" class="flex space-x-4">
          <general-outlined-button id="btn-set-today" label="Today" @on-click="setToday"/>
        </div>

        <div v-if="!props.isHasChangeMode" class="flex space-x-1">
          <general-icon-button class="!w-8 !h-8 !border-none" @on-click="onClickChange('PREV')">
            <template #icon>
              <icon-chevron-left size="20" class="stroke-gray-900"/>
            </template>
          </general-icon-button>
          <general-icon-button class="!w-8 !h-8 !border-none" @on-click="onClickChange('NEXT')">
            <template #icon>
              <icon-chevron-right size="20" class="stroke-gray-900"/>
            </template>
          </general-icon-button>
        </div>
      </div>
    </div>

    <app-calendar-month
      v-if="selectedCalendarMode === 'MONTH'"
      :model-value="props.modelValue"
      :variant="props.variant"
      :current-month="currentMonth"
      :current-year="currentYear"
      :is-clickable="props.isClickable"
      :events="props.events"
      @on-click-more="emit('on-click-more', $event)"
      @on-click-event="emit('on-click-event', $event)"
      @update:model-value="emit('update:model-value', $event)"
    />

    <app-calendar-week
      v-else-if="selectedCalendarMode === 'WEEK'"
      :model-value="props.modelValue"
      :events="props.events"
      @on-click-event="emit('on-click-event', $event)"
    >
    </app-calendar-week>
  </div>
</template>

<style scoped>
.calendar-header {
  @apply flex justify-between items-center p-4;
}
</style>
