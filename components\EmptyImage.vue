<template>
  <div class="flex flex-col items-center">
    <nuxt-img
        src="/empty-image.png"
        width="210"
        height="210"
        class="rounded-lg p-8"
        style="aspect-ratio: 210 / 210; object-fit: cover; display: inline-block"
    />
    <p class="text-sm font-bold text-center">Belum Ada Gambar yang Dikirim</p>
    <p class="text-xs text-center">
      Belum ada data gambar yang dikirim Pada lokasi ini
    </p>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'EmptyImage',
  setup() {},
})
</script>
