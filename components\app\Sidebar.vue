<script setup lang="ts">
import { computed } from "vue";
import type { Ref } from "vue";
import { Collapse, Drawer, initFlowbite } from "flowbite";
import type {
    DrawerInterface,
    CollapseInterface,
    CollapseOptions,
} from "flowbite";
import { useAuthStore } from "~/store/auth";
import type { Permissions } from "~/types/server-response";

const $auth = useAuthStore()

let drawerSidebar: DrawerInterface | null = null;
let collapseMaster: CollapseInterface | null = null;
let collapseSettings: CollapseInterface | null = null;

const isVisibleCollapseMaster = ref(false);
const isVisibleCollapseSettings = ref(false);

const emit = defineEmits(["on-mounted", "on-click-close-sidebar"]);

const setupDrawer = (targetId: string) => {
    const $target = document.getElementById(targetId);
    return new Drawer($target);
};

const setupCollapse = (
    triggerId: string,
    targetId: string,
    isVisible: Ref<boolean>,
) => {
    const $trigger = document.getElementById(triggerId);
    const $target = document.getElementById(targetId);

    const options: CollapseOptions = {
        onToggle: (collapse: CollapseInterface) => {
            isVisible.value = collapse._visible;
        },
    };

    return new Collapse($target, $trigger, options);
};

onMounted(() => {
    initFlowbite();
    $auth.getAuthSession()
    drawerSidebar = setupDrawer("targetDrawerSidebar");

    emit("on-mounted", drawerSidebar);
});

const routePath = computed((): string => {
    return useRoute().path;
});

const getColor = (destination: string) => {
    return routePath.value.startsWith(destination)
        ? "stroke-primary-500"
        : "stroke-gray-900";
};
</script>

<template>
    <aside
        id="targetDrawerSidebar"
        class="w-full md:w-fit border-r h-screen overflow-y-auto py-10 px-4 z-40 bg-white transition-transform fixed top-0 -translate-x-full md:translate-x-0"
    >
        <div class="mb-10 px-3 flex items-center justify-between">
            <div class="flex items-center">
                <nuxt-img class="mr-4 w-20 h-20" src="logo-mytask.webp" />
                <p class="whitespace-nowrap text-2xl font-[600] text-gray-900">
                    MyTask
                </p>
            </div>

            <general-icon-button
                class="block md:hidden"
                @click="emit('on-click-close-sidebar')"
            >
                <template #icon>
                    <icon-close />
                </template>
            </general-icon-button>
        </div>

        <div class="py-10 border-t border-b">
            <ul class="space-y-3">
                <li>
                    <app-navigation-link
                        id="menuDashboard"
                        name="Dashboard"
                        destination="/dashboard"
                    >
                        <template #icon>
                            <icon-presentation-chart
                                :class="getColor('/dashboard')"
                            />
                        </template>
                    </app-navigation-link>
                </li>

                <li>
                    <app-navigation-link
                        id="menuTasks"
                        name="Daftar Tugas"
                        destination="/tasks"
                    >
                        <template #icon>
                            <icon-file :class="getColor('/tasks')" />
                        </template>
                    </app-navigation-link>
                </li>

                <li>
                    <app-navigation-link
                        id="menuAttendances"
                        name="Daftar Absen"
                        destination="/attendances"
                    >
                        <template #icon>
                            <icon-user-check
                                :class="getColor('/attendances')"
                            />
                        </template>
                    </app-navigation-link>
                </li>

                <li>
                    <app-navigation-link
                        id="menuActivityReport"
                        name="Laporan Aktifitas"
                        destination="/activity-report"
                    >
                        <template #icon>
                            <icon-activity-report :class="getColor('/activity-report')" />
                        </template>
                    </app-navigation-link>
                </li>

                <li>
                    <app-navigation-link
                        id="menuEmployees"
                        name="Karyawan"
                        destination="/employees"
                    >
                        <template #icon>
                            <icon-users :class="getColor('/employees')" />
                        </template>
                    </app-navigation-link>
                </li>
                <li>
                    <app-navigation-link
                        id="menuGPSTracking"
                        name="GPS Tracking"
                        target="_blank"
                        destination="https://telematics.transtrack.id"
                    >
                        <template #icon>
                            <icon-marker-pin
                                :class="getColor('/gps-tracking')"
                            />
                        </template>
                    </app-navigation-link>
                </li>
                <li>
                    <app-navigation-link
                        id="menuTaskType"
                        name="Tipe Penugasan"
                        destination="/task-types"
                    >
                        <template #icon>
                            <icon-category :class="getColor('/task-types')" />
                        </template>
                    </app-navigation-link>
                </li>
                <li>
                    <app-navigation-link
                        id="menuTaskType"
                        name="Kategori Laporan"
                        destination="/report-category"
                    >
                        <template #icon>
                            <icon-category-report :class="getColor('/report-category')" />
                        </template>
                    </app-navigation-link>
                </li>
                <li v-if="$auth.session?.role?.permissions && $auth.session.role.permissions.some((permission: Permissions) => permission.group === 'perusahaan')">
                    <app-navigation-link
                        id="menuCompanies"
                        name="Perusahaan"
                        destination="/companies"
                    >
                        <template #icon>
                            <icon-company :class="getColor('/companies')" />
                        </template>
                    </app-navigation-link>
                </li>
                <li>
                    <app-navigation-link
                        id="menuUsers"
                        name="Pengguna"
                        destination="/users"
                    >
                        <template #icon>
                            <icon-user-add :class="getColor('/users')" />
                        </template>
                    </app-navigation-link>
                </li>
                <li>
                    <app-navigation-link
                        id="menuRoles"
                        name="Manajemen Role"
                        destination="/roles"
                    >
                        <template #icon>
                            <icon-roles :class="getColor('/roles')" />
                        </template>
                    </app-navigation-link>
                </li>
            </ul>
        </div>
    </aside>
</template>

