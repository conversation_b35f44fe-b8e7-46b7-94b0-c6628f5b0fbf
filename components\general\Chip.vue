<script setup lang="ts">
const props = defineProps({
  label: {
    type: String,
    default: ''
  },
  variant: {
    type: String as () => 'primary' | 'error' | 'warning' | 'success' | 'info' | 'blue-gray',
    default: 'primary'
  },
  size: {
    type: String as () => 'text-xs' | 'text-sm' | 'text-md' | 'text-lg',
  }
})

const chipClass = computed(() => {
  const baseClass = `py-0.5 px-2 w-fit flex items-center rounded-full cursor-default select-none ${props.size ?? 'text-xs'}`
  let color = ''
  switch (props.variant) {
    case 'primary':
      color = 'text-primary-700 bg-primary-50';
      break
    case 'error':
      color = 'text-error-700 bg-error-50';
      break
    case 'warning':
      color = 'text-warning-700 bg-warning-50';
      break
    case 'success':
      color = 'text-success-700 bg-success-50';
      break
    case 'info':
      color = 'text-info-700 bg-info-50';
      break
    case 'blue-gray':
      color = 'text-blue-gray-700 bg-blue-gray-50';
      break
  }
  return `${baseClass} ${color}`
})
</script>

<template>
  <div :class="chipClass">
    <slot name="prefix"/>
    <p>{{ label }}</p>
    <slot name="suffix"/>
  </div>
</template>

