<script setup lang="ts">
import { defaultLatLng } from "~/utils/functions";
import type { LeafletMouseEvent } from "leaflet";

const props = defineProps({
  zoom: {
    type: Number,
    default: 4,
  },
  center: {
    type: [
      Array as () => number[],
      Object as () => { lat: number; lng: number },
    ],
    default: defaultLatLng,
  },
  polyline: {
    type: Array as () => number[][],
    default: [],
  },
  isClickable: {
    type: Boolean,
    default: false,
  },
  zoomControl: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["on-click-map"]);

function dispatchResizeEvent() {
  setTimeout(() => {
    dispatchEvent(new Event("resize"));
  }, 300);
}

onMounted(() => {
  dispatchResizeEvent();
});

onUpdated(() => {
  dispatchResizeEvent();
});

const accessToken = computed(() => {
  return useRuntimeConfig().public.accessTokenMapBox;
});

function onClickMap(event: LeafletMouseEvent) {
  if (!props.isClickable) return;
  emit("on-click-map", event.latlng);
}
</script>

<template>
  <client-only>
    <l-map
      ref="map"
      :zoom="props.zoom as number"
      :center="props.center as number[]"
      :fade-animation="true"
      :no-blocking-animations="true"
      :options="{ zoomControl: props.zoomControl, trackResize: true }"
      class="custom-map z-0"
      @click="onClickMap($event)"
    >
      <l-tile-layer
        :url="`https://api.mapbox.com/styles/v1/mapbox/streets-v12/tiles/{z}/{x}/{y}?access_token=${accessToken}`"
        attribution='&amp;copy; <a href="https://www.openstreetmap.org/">OpenStreetMap</a> contributors'
        layer-type="base"
        name="OpenStreetMap"
      />
      <l-control :position="'bottomleft'">
        <!-- Specify the position of the control -->
        <div id="scale-control"></div>
      </l-control>
      <l-polyline :lat-lngs="props.polyline" color="#EF3434" :weight="2" />
      <slot name="marker" />
    </l-map>
  </client-only>
</template>

<style scoped>
.custom-map :deep(.leaflet-marker-icon) {
  background-color: transparent;
  border: none;
}
</style>
