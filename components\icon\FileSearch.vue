<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default: '24'
  }
})
</script>

<template>
  <svg xmlns="http://www.w3.org/2000/svg" :width="props.size" :height="props.size" viewBox="0 0 24 25" fill="none">
    <path
      d="M14 11.5H8M10 15.5H8M16 7.5H8M20 11V7.3C20 5.61984 20 4.77976 19.673 4.13803C19.3854 3.57354 18.9265 3.1146 18.362 2.82698C17.7202 2.5 16.8802 2.5 15.2 2.5H8.8C7.11984 2.5 6.27976 2.5 5.63803 2.82698C5.07354 3.1146 4.6146 3.57354 4.32698 4.13803C4 4.77976 4 5.61984 4 7.3V17.7C4 19.3802 4 20.2202 4.32698 20.862C4.6146 21.4265 5.07354 21.8854 5.63803 22.173C6.27976 22.5 7.11984 22.5 8.8 22.5H11.5M22 22.5L20.5 21M21.5 18.5C21.5 20.433 19.933 22 18 22C16.067 22 14.5 20.433 14.5 18.5C14.5 16.567 16.067 15 18 15C19.933 15 21.5 16.567 21.5 18.5Z"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>
</template>

<style scoped>

</style>