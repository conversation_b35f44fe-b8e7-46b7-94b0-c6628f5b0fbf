meta {
  name: Create Task
  type: http
  seq: 1
}

post {
  url: {{base_url}}/task/create-task
  body: multipartForm
  auth: bearer
}

auth:bearer {
  token: {{token}}
}

body:multipart-form {
  employee_id: 5
  title: Task April- 2023
  no_referensi: 08LJGPT
  description: <PERSON><PERSON> gps
  lat_asal: -5.999720
  long_asal: 105.987640
  lat_tujuan: -7.356490
  long_tujuan: 112.727814
  deadline: 2024-04-25 12:00:00
}

tests {
  // if(pm.response.code == 200) {
  //     pm.test("Status code is 200");
  //     var result = pm.response.json();
  // } else if(pm.response.code == 401) {
  //     pm.test("Status code is 401");
  // }
  // 
  // var status = pm.response.json().data.status;
  // if(status != null) {
  //     pm.test("Status task is " + status);
  // }
  // 
  // 
}
