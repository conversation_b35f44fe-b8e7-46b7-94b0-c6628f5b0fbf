<script setup lang="ts">
import VueDatePicker from "@vuepic/vue-datepicker";
import type { DatePickerInstance } from "@vuepic/vue-datepicker";
import "@vuepic/vue-datepicker/dist/main.css";
import { format, isAfter } from "date-fns";

const props = defineProps({
    modelValue: {
        type: Array as () => Date[],
        default: [],
    },
    id: {
        type: String,
        default: "",
    },
    label: {
        type: String,
        default: "",
    },
    placeholder: {
        type: String,
        default: "",
    },
    required: {
        type: Boolean,
        default: false,
    },
    disabled: {
        type: Boolean,
        default: false,
    },
    inline: {
        type: Boolean,
        default: false,
    },
    enableTimePicker: {
        type: Boolean,
        default: true,
    },
});

const selectedDate = ref<string[]>([]);
const datePicker = ref<DatePickerInstance | null>(null);

const emit = defineEmits(["update:modelValue", "cancel"]);

watch(
    () => props.modelValue,
    (value) => {
        if (props.modelValue?.length === 0) {
            return null;
        }

        selectedDate.value = props.modelValue.map((date) =>
            format(new Date(date), "MM-dd-yyyy"),
        );
        emit("update:modelValue", value);
    },
    { immediate: true },
);

function onSelectDate(value: string) {
    const reformattedDate = format(new Date(value), "MM-dd-yyyy");

    if (
        selectedDate.value.length >= 1 &&
        isAfter(new Date(reformattedDate), new Date(selectedDate.value[0]))
    ) {
        selectedDate.value.push(reformattedDate);
    } else {
        selectedDate.value.unshift(reformattedDate);
    }
}
function validateHour(
    hours: number[],
    isStart: boolean,
    addHour: number,
): boolean {
    if (selectedDate.value[0] === selectedDate.value[1]) {
        if (isStart && hours[0] + addHour > hours[1]) {
            return false;
        }
        if (!isStart && hours[1] + addHour < hours[0]) {
            return false;
        }
    }
    return true;
}

function validateMinute(
    hours: number[],
    minutes: number[],
    isStart: boolean,
    addMinute: number,
): boolean {
    if (selectedDate.value[0] === selectedDate.value[1]) {
        if (isStart && minutes[0] + addMinute > minutes[1]) {
            return false;
        }
        if (!isStart && minutes[1] + addMinute < minutes[0]) {
            return false;
        }
    }
    return true;
}
</script>

<template>
    <div class="flex flex-col items-start text-sm font-[600] text-gray-700">
        <label
            v-if="props.label"
            :for="id"
            class="mb-1.5 text-sm font-[600] text-gray-700"
            @click="datePicker?.openMenu()"
        >
            {{ props.label }}
            <span v-if="props.required" class="text-primary-500">*</span>
        </label>
        <vue-date-picker
            range
            ref="datePicker"
            :id="props.id"
            :disabled="props.disabled"
            :model-value="props.modelValue as Date[]"
            :placeholder="props.placeholder as string"
            input-class-name="!py-2 !rounded-lg focus:!ring-primary-500"
            calendar-cell-class-name="!rounded-full"
            class="custom-date-picker"
            :max-date="new Date()"
            :inline="inline"
            @range-start="
                selectedDate = [];
                onSelectDate($event);
            "
            @range-end="
                onSelectDate($event);
                datePicker?.switchView('time');
            "
            @update:model-value="emit('update:modelValue', $event)"
            @cleared="emit('update:modelValue', [])"
            :enable-time-picker="enableTimePicker"
        >
            <template
                #time-picker-overlay="{ hours, minutes, setHours, setMinutes }"
            >
                <div class="py-5 px-8 space-y-5">
                    <div class="flex flex-col items-center">
                        <p class="font-medium mb-2 text-center">
                            {{ selectedDate[0] ?? "MM-DD-YYYY" }}
                        </p>
                        <div class="flex items-center space-x-3 w-3/4">
                            <div class="flex flex-col items-center space-y-1">
                                <general-icon-button
                                    :disabled="
                                        hours[0] >= 23 ||
                                        !validateHour(hours, true, 1)
                                    "
                                    class="w-6 h-6 !rounded-full border-none"
                                    @on-click="
                                        () => {
                                            if (validateHour(hours, true, 1))
                                                setHours([
                                                    (hours[0] += 1),
                                                    hours[1],
                                                ]);
                                        }
                                    "
                                >
                                    <template #icon>
                                        <icon-chevron-up size="20" />
                                    </template>
                                </general-icon-button>
                                <input
                                    :value="
                                        hours[0] < 10
                                            ? `0${hours[0]}`
                                            : hours[0]
                                    "
                                    type="number"
                                    class="text-center w-full p-1 text-2xl border-none rounded-lg transition focus:bg-gray-100 focus:border-none focus:ring-0"
                                    @input="
                                        setHours([
                                            Number(
                                                $event.target.value > 23
                                                    ? 23
                                                    : $event.target.value,
                                            ),
                                            hours[1],
                                        ])
                                    "
                                />
                                <general-icon-button
                                    :disabled="
                                        hours[0] <= 0 ||
                                        !validateHour(hours, true, -1)
                                    "
                                    class="w-6 h-6 !rounded-full border-none"
                                    @on-click="
                                        () => {
                                            if (validateHour(hours, true, -1))
                                                setHours([
                                                    (hours[0] += -1),
                                                    hours[1],
                                                ]);
                                        }
                                    "
                                >
                                    <template #icon>
                                        <icon-chevron-up
                                            size="20"
                                            class="rotate-180"
                                        />
                                    </template>
                                </general-icon-button>
                            </div>
                            <span class="text-xl">:</span>
                            <div class="flex flex-col items-center space-y-1">
                                <general-icon-button
                                    :disabled="
                                        minutes[0] >= 59 ||
                                        !validateMinute(hours, minutes, true, 1)
                                    "
                                    class="w-6 h-6 !rounded-full border-none"
                                    @on-click="
                                        () => {
                                            if (
                                                validateMinute(
                                                    hours,
                                                    minutes,
                                                    true,
                                                    1,
                                                )
                                            )
                                                setMinutes([
                                                    (minutes[0] += 1),
                                                    minutes[1],
                                                ]);
                                        }
                                    "
                                >
                                    <template #icon>
                                        <icon-chevron-up size="20" />
                                    </template>
                                </general-icon-button>
                                <input
                                    :value="
                                        minutes[0] < 10
                                            ? `0${minutes[0]}`
                                            : minutes[0]
                                    "
                                    type="number"
                                    class="text-center w-full p-1 text-2xl border-none rounded-lg transition focus:bg-gray-100 focus:border-none focus:ring-0"
                                    @input="
                                        setMinutes([
                                            Number(
                                                $event.target.value > 59
                                                    ? 59
                                                    : $event.target.value,
                                            ),
                                            minutes[1],
                                        ])
                                    "
                                />
                                <general-icon-button
                                    :disabled="
                                        minutes[0] <= 0 ||
                                        !validateMinute(
                                            hours,
                                            minutes,
                                            true,
                                            -1,
                                        )
                                    "
                                    class="w-6 h-6 !rounded-full border-none"
                                    @on-click="
                                        () => {
                                            if (
                                                validateMinute(
                                                    hours,
                                                    minutes,
                                                    true,
                                                    -1,
                                                )
                                            )
                                                setMinutes([
                                                    (minutes[0] -= 1),
                                                    minutes[1],
                                                ]);
                                        }
                                    "
                                >
                                    <template #icon>
                                        <icon-chevron-up
                                            size="20"
                                            class="rotate-180"
                                        />
                                    </template>
                                </general-icon-button>
                            </div>
                        </div>
                    </div>
                    <hr />
                    <div class="flex flex-col items-center">
                        <p class="font-medium mb-2 text-center">
                            {{ selectedDate[1] ?? "MM-DD-YYYY" }}
                        </p>
                        <div class="flex items-center space-x-3 w-3/4">
                            <div class="flex flex-col items-center space-y-1">
                                <general-icon-button
                                    :disabled="
                                        hours[1] >= 23 ||
                                        !validateHour(hours, false, 1)
                                    "
                                    class="w-6 h-6 !rounded-full border-none"
                                    @on-click="
                                        () => {
                                            if (validateHour(hours, false, 1))
                                                setHours([
                                                    hours[0],
                                                    (hours[1] += 1),
                                                ]);
                                        }
                                    "
                                >
                                    <template #icon>
                                        <icon-chevron-up size="20" />
                                    </template>
                                </general-icon-button>
                                <input
                                    :value="
                                        hours[1] < 10
                                            ? `0${hours[1]}`
                                            : hours[1]
                                    "
                                    type="number"
                                    class="text-center w-full p-1 text-2xl border-none rounded-lg transition focus:bg-gray-100 focus:border-none focus:ring-0"
                                    @input="
                                        setHours([
                                            hours[0],
                                            Number(
                                                $event.target.value > 23
                                                    ? 23
                                                    : $event.target.value,
                                            ),
                                        ])
                                    "
                                />
                                <general-icon-button
                                    :disabled="
                                        hours[1] <= 0 ||
                                        !validateHour(hours, false, -1)
                                    "
                                    class="w-6 h-6 !rounded-full border-none"
                                    @on-click="
                                        () => {
                                            if (validateHour(hours, false, -1))
                                                setHours([
                                                    hours[0],
                                                    (hours[1] += -1),
                                                ]);
                                        }
                                    "
                                    >>
                                    <template #icon>
                                        <icon-chevron-up
                                            size="20"
                                            class="rotate-180"
                                        />
                                    </template>
                                </general-icon-button>
                            </div>
                            <span class="text-xl">:</span>
                            <div class="flex flex-col items-center space-y-1">
                                <general-icon-button
                                    :disabled="
                                        minutes[1] >= 59 ||
                                        !validateMinute(
                                            hours,
                                            minutes,
                                            false,
                                            1,
                                        )
                                    "
                                    class="w-6 h-6 !rounded-full border-none"
                                    @on-click="
                                        () => {
                                            if (
                                                validateMinute(
                                                    hours,
                                                    minutes,
                                                    false,
                                                    1,
                                                )
                                            )
                                                setMinutes([
                                                    minutes[0],
                                                    (minutes[1] += 1),
                                                ]);
                                        }
                                    "
                                >
                                    <template #icon>
                                        <icon-chevron-up size="20" />
                                    </template>
                                </general-icon-button>
                                <input
                                    :value="
                                        minutes[1] < 10
                                            ? `0${minutes[1]}`
                                            : minutes[1]
                                    "
                                    type="number"
                                    class="text-center w-full p-1 text-2xl border-none rounded-lg transition focus:bg-gray-100 focus:border-none focus:ring-0"
                                    @input="
                                        setMinutes([
                                            minutes[0],
                                            Number(
                                                $event.target.value > 59
                                                    ? 59
                                                    : $event.target.value,
                                            ),
                                        ])
                                    "
                                />
                                <general-icon-button
                                    :disabled="
                                        minutes[1] <= 0 ||
                                        !validateMinute(
                                            hours,
                                            minutes,
                                            false,
                                            -1,
                                        )
                                    "
                                    class="w-6 h-6 !rounded-full border-none"
                                    @on-click="
                                        () => {
                                            if (
                                                validateMinute(
                                                    hours,
                                                    minutes,
                                                    false,
                                                    -1,
                                                )
                                            )
                                                setMinutes([
                                                    minutes[0],
                                                    (minutes[1] -= 1),
                                                ]);
                                        }
                                    "
                                >
                                    <template #icon>
                                        <icon-chevron-up
                                            size="20"
                                            class="rotate-180"
                                        />
                                    </template>
                                </general-icon-button>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
            <template #action-row="{ modelValue, selectDate }">
                <div class="!w-full flex space-x-3">
                    <general-outlined-button
                        v-if="!props.inline"
                        label="Cancel"
                        class="w-full"
                        @on-click="
                            emit('cancel');
                            datePicker?.closeMenu();
                        "
                    />
                    <general-button
                        label="Confirm"
                        class="w-full"
                        @on-click="selectDate"
                    />
                </div>
            </template>
        </vue-date-picker>
    </div>
</template>

<style scoped>
.custom-date-picker :deep(input) {
    font-weight: 400;
}

.custom-date-picker :deep(.dp__month_year_row) {
    font-weight: 400;
}

.custom-date-picker :deep(.dp__calendar_header) {
    font-weight: 500 !important;
}

.custom-date-picker :deep(.dp__calendar) {
    font-weight: 400 !important;
}

.custom-date-picker :deep(.dp__theme_light) {
    --dp-text-color: #344054;
    --dp-primary-color: #ef3434;
    --dp-range-between-dates-background-color: #fff5f5;
    --dp-range-between-dates-text-color: #d72f2f;
}
</style>
