import { defineStore } from 'pinia'
import type { Company, Industry } from '~/types/company'
import { toast } from 'vue3-toastify'
import api from '~/services/api'
import type { ValidationError } from 'yup'

export const useCompaniesStore = defineStore('companies', {
  state: () => ({
    isLoading: {
      list: false,
      form: false,
      detail: false,
      search: false,
      inputTypes: false,
    },
    companies: [] as Company[],
    industries: [] as Industry[],
    company: {} as Company,
    loading: false,
    total: 0,
    page: 1,
    page_size: 10,
    filter_columns: '',
    filter_value: '',
    date_column: 'created_at',
    search_columns: 'name',
    order: 'desc',
  }),

  actions: {
    async fetchCompanies(params: { page?: number; page_size?: number; search?: string; from?: string; to?: string; industry_id?: string; filter_columns?: string; filter_value?: string; entries?: string } = {}, useGo = true) {
      this.loading = true
      try {
        const { data } = await api.get('/api/v1/companies', { queryParams: {
          page: params.page?.toString() || '',
          page_size: params.page_size?.toString() || '',
          search_value: params.search || '',
          date_start: params.from || '',
          date_end: params.to || '',
          industry_id: params.industry_id || '',
          date_column: this.date_column,
          search_columns: this.search_columns,
          filter_columns: params.filter_columns || '',
          filter_value: params.filter_value || '',
          entries: params.entries || '',
          order: this.order
        }, useGo })
        this.companies = data.list
        this.page = data.pagination
      } catch (error: any) {
        toast.error(error.message || 'Failed to fetch companies')
      } finally {
        this.loading = false
      }
    },

    async createCompany(formData: FormData, useGo = true) {
      this.loading = true;
      try {
        const response = await api.post('/api/v1/companies', {
          body: formData,
          useGo
        });
        
        if (response.status) {
          toast.success('Perusahaan berhasil dibuat');
          return response;
        }
        return null;
      } catch (error: any) {
        console.error('Error creating company:', error);
        toast.error(error.message || 'Gagal membuat perusahaan');
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async fetchDetail(companyId: number, useGo = true) {
      this.isLoading.detail = true
      try {
        const response = await api.get(`/api/v1/companies/${companyId}`, { useGo })
  
        this.company = response.data
      } catch (error) {
        console.error('Error fetching company detail:', error)
        toast.error((error as ValidationError).message || 'Gagal mengambil detail perusahaan')
      } finally {
        this.isLoading.detail = false
      }
    },

    async updateCompany(companyId: number, formData: FormData, useGo = true) {
      this.loading = true
      try {

        formData.append('_method', 'PUT'); 

        const response = await api.post(`/api/v1/companies/${companyId}`, {
          body: formData,
          useGo
        });
        
        if (response.status) {
          toast.success('Perusahaan berhasil diperbarui');
          navigateTo('/companies');
          return response;
        }
        return null;
      } catch (error: any) {
        console.error('Error updating company:', error);
        toast.error(error.message || 'Gagal memperbarui perusahaan');
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async deleteCompany(companyId: number, useGo = true) {
      this.loading = true
      try {
        const response = await api.delete(`/api/v1/companies/${companyId}`, { useGo })
        if (response.success) {
          toast.success('Perusahaan berhasil dihapus')
          return response
        }
      } catch (error: any) {
        console.error('Error deleting company:', error)
        toast.error(error.message || 'Gagal menghapus perusahaan')
        throw error
      } finally {
        this.loading = false
      }
    },

    async getIndustries() {
      this.loading = true
      try {
        const { data } = await api.get('/api/v1/industries', { useGo: true })
        this.industries = data
      } catch (error: any) {
        toast.error(error.message || 'Failed to fetch industries')
      } finally {
        this.loading = false
      }
    }
  }
})
